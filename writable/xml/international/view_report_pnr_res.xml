<OTA_AirResRetRS EchoToken="String" TimeStamp="String" Version="String" Target="String" SequenceNmbr="String">
    <AirResRet>
        <FlightSegments>
            <FlightSegment FlightNumber="1355" ArrivalDateTime="2011-08-11T20:20:00.0Z" NumberInParty="3" DepartureDateTime="2011-08-11T16:35:00.0Z" SegmentType="NORMAL" CodeshareInd="false" Ticket="eTicket" ScheduleValidEndDate="1967-08-13" IsChanged="false" RPH="3" Status="RR">
                <DepartureAirport LocationCode="PEK"/>
                <ArrivalAirport LocationCode="HAK"/>
                <MarketingAirline CodeContext="String" CompanyShortName="String" Code="CA"/>
                <BookingClassAvail ResBookDesigCode="A">
                    <SubClass></SubClass>
                </BookingClassAvail>
            </FlightSegment>
        </FlightSegments>
        <BookingReferenceID ID="HQ8WEW">
        </BookingReferenceID>
        <AirTraveler RPH="1" PassengerTypeCode="ADT" Birthday="1967-08-13" Gender="MALE">
            <PersonName>
                <Surname>张</Surname>
                <GivenName>三丰</GivenName>
                <NamePNR>张三丰</NamePNR>
            </PersonName>
            <Telephone PhoneNumber="13666666666"/>
            <Email><EMAIL></Email>
            <Address>
                <CountryName>中华人民共和国</CountryName>
                <StateProv>福建省</StateProv>
                <CityName>厦门市</CityName>
                <AddressLine>华建大厦</AddressLine>
            </Address>
            <Document DocType="1" DocTypeDetail="ID" DocID="350123196708130011"
                      DocIssueCountry="CN" DocHolderNationality="CN" BirthDate="1967-08-13"
                      Gender="MALE" ExpireDate="2027-08-13">
                <DocHolderFormattedName>张三丰</DocHolderFormattedName>
                <Surname>张</Surname>
                <GivenName>三丰</GivenName>
            </Document>
            <PassengerTypeQuantity Age="58" Code="ADT" Quantity="1"/>
        </AirTraveler>
        <AirTraveler RPH="2" PassengerTypeCode="ADT" Birthday="1987-08-13" Gender="FEMALE">
            <PersonName>
                <Surname>刘</Surname>
                <GivenName>亦菲</GivenName>
                <NamePNR>刘亦菲</NamePNR>
            </PersonName>
            <Telephone PhoneNumber="13668886666"/>
            <Email><EMAIL></Email>
            <Address>
                <CountryName>中华人民共和国</CountryName>
                <StateProv>福建省</StateProv>
                <CityName>厦门市</CityName>
                <AddressLine>华建大厦</AddressLine>
            </Address>
            <Document DocType="1" DocTypeDetail="ID" DocID="350123196708130011" DocIssueCountry="CN" DocHolderNationality="CN" BirthDate="1987-08-13" Gender="FEMALE" ExpireDate="2027-08-13">
                <DocHolderFormattedName>刘亦菲</DocHolderFormattedName>
                <Surname>刘</Surname>
                <GivenName>亦菲</GivenName>
            </Document>
            <PassengerTypeQuantity Age="38" Code="ADT" Quantity="1"/>
        </AirTraveler>
        <Ticketing IsIssued="false" OfficeCode="SHA320" IssuedType="TL" TicketTimeLimit="2011-08-10T18:00:00.0Z" RPH="0" Remark="TL/1800/10AUG/SHA320"></Ticketing>
        <Responsibility PNRno="" CRS="" OfficeCode="SHA666" RPH="17"/>
        <ContactInfo ContactCity="SHA" RPH="0" ContactInfo="SHA/T SHA/T 021-52918766/SHA GUANG FA AIR TICKET SERVICECO.,LTD/ZHOU JIANABCDEFG">
            <TravelerRefNumber RPH="4"/>
        </ContactInfo>
        <SpecialRemark RemarkType="" RPH="16">
            <Text>CA/NYF0G6</Text>
        </SpecialRemark>
        <OtherServiceInformation RPH="15" Code="">
            <Text>1E CAET TN/9992162073572-9992162073573</Text>
        </OtherServiceInformation>
        <SpecialServiceRequest ServiceQuantity="3" SSRCode="FOID" BirthDate="1967-08-13" RPH="7">
            <Text>FOID</Text>
        </SpecialServiceRequest>
    </AirResRet>
</OTA_AirResRetRS>