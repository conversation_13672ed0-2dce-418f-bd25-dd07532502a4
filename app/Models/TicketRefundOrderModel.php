<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketRefundOrderModel extends Model
{
    protected $table = 'ticket_refund_orders';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat = 'int';

    //订单状态
    const ORDER_STATUS = [0=>'已申请', 1=>'退票中', 2=>'已退票',3=>'已取消', 4=>'退票失败'];

    // 查询退款费用时传参
    const TICKET_TYPE_INTERNATIONAL = 'INTERNATIONAL';
    const TICKET_TYPE_DOMESTIC = 'DOMESTIC';

   //分页列表
    public function paginate_list($where, $page, $perPage)
    {
        $refund_pax_model = model('TicketRefundPaxModel');
        $refund_seg_model = model('TicketRefundSegModel');
        $book_seg_model = model('TicketBookSegModel');
        $rebook_seg_model = model('TicketRebookSegModel');
        $flightModel = model('FlightModel');

        $this->handle_conditions($where);
        $this->orderBy('id', 'DESC');
        $list = $this->paginate($perPage, 'default', $page);
        if (empty($list)) {
            return [];
        }
        $order_ids = array_column($list, 'id');

        //订单乘客
        $order_passenger_list = $refund_pax_model->whereIn('order_id', $order_ids)->findAll();
        $order_passengers = [];
        foreach ($order_passenger_list as $val) {
            $order_id = $val['order_id'];
            $order_passengers[$order_id][] = [
                'order_id' => $val['order_id'],
                'person_name' => $val['person_name'],
                'ticket_number' => $val['ticket_number'],
            ];
        }
        //航段
        $order_segment_list = $refund_seg_model->whereIn('order_id', $order_ids)->findAll();
        $flight_numbers = array_column($order_segment_list, 'flight_number');

        //航班
        $flight_list = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();
        $flight_list = array_column($flight_list, null, 'flight_number');

        //改签后按订单ID找航段信息
        $order_segments = [];
        foreach ($order_segment_list as $val) {
            $flight_number = $val['flight_number'];
            $order_id = $val['order_id'];
            $order_segments[$order_id][] = [
                'order_id' => $val['order_id'],
                'flight_number' => $flight_number,
                'departure_airport' => $flight_list[$flight_number]['departure_airport'],//出发机场代码
                'arrival_airport' => $flight_list[$flight_number]['arrival_airport'],//到达机场代码
                'cabin' => $val['cabin'],//舱位
                'departure_datetime' => explode(' ', $val['departure_datetime'])[0],//出发日期
            ];
        }

        $data = [];
        foreach ($list as $value) {
            $str_passenger = '';
            foreach ($order_passengers[$value['id']] as $passenger) {
                if ($str_passenger == '') {
                    $str_passenger = $passenger['person_name'];
                } else {
                    $str_passenger = '<br>' . $passenger['person_name'];
                }
            }

            $str_segment = '';
            foreach ($order_segments[$value['id']] as $segment) {
                if (!empty($str_segment)) {
                    $str_segment .= '<br>';
                }
                $str_segment = $segment['flight_number'] . ' ' . $segment['departure_airport'] . '-' . $segment['arrival_airport'] . ' / ' . $segment['cabin'] . '<br>' . $segment['departure_datetime'];
            }

            $data[] = [
                'order_id' => $value['id'],
                'order_no' => $value['order_no'],
                'origin_order_no' => $value['origin_order_no'],
                'pnr' => $value['pnr'],
                'origin_pnr' => $value['origin_pnr'],
                'airline_pnr' => $value['airline_pnr'],//大编码
                'operator_name' => $value['operator_name'],//订票员
                'created_at' => date('Y-m-d H:i:s', $value['created_at']),//下单时间
                'status' => $value['status'],//订单状态
                'status_text' => self::ORDER_STATUS[$value['status']],
                'total_supplier_amount' => $value['total_supplier_amount'],//采购退款总额
                'total_customer_amount' => $value['total_customer_amount'],//销售退款总额
                'passengers' => $str_passenger,//乘客信息
                'segments' => $str_segment,//航程信息
            ];
        }

        return $data;
    }

    //按状态分组统计
    public function total_status($where)
    {
        $this->handle_conditions($where);
        $total_list = $this->select('status, COUNT(*) as total')
            ->groupBy('status')
            ->orderBy('total', 'DESC')
            ->findAll();
        $total_list = array_column($total_list, null,'status');
        $data = [];
        foreach (self::ORDER_STATUS as $key => $value) {
            $total = $total_list[$key]['total'] ?? 0;
            $data[] = [
                'status' => $key,
                'status_text' => $value,
                'total' => $total,
            ];
        }
        return $data;
    }

    private function handle_conditions($where)
    {
        foreach ($where as $k => $v) {
            switch ($k) {
                case 'pnr':
                    $this->where(" pnr = " . $this->db->escape($v) . " or origin_pnr = " . $this->db->escape($v) . " ");
                    break;
                case 'created_at_from':
                    $this->where('created_at >=', $v);
                    break;
                case 'created_at_to':
                    $this->where('created_at <=', $v);
                    break;
                case 'ids':
                    if ($v) {
                        $this->whereIn('id', $v);
                    }
                    break;
                case 'order_no_like':
                    $this->like('order_no', $v, 'after');
                    break;
                default:
                    $this->where($k, $v);
            }
        }
    }
}