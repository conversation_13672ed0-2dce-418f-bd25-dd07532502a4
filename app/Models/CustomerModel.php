<?php

namespace App\Models;

use App\Models\CustomerAccountModel;
use CodeIgniter\Model;

class CustomerModel extends Model
{
    protected $table = 'customers';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat = 'int';

    //客户类型：1散客客户 2企业客户 3公务客户 4分销客户
    const CUSTOMER_TYPE_INDIVIDUAL = 1;
    const CUSTOMER_TYPE_ENTERPRISE = 2;
    const CUSTOMER_TYPE_OFFICIAL = 3;
    const CUSTOMER_TYPE_DISTRIBUTION = 4;

    //会员等级：1初始会员 2中级会员 3高级会员 4VIP会员
    const CUSTOMER_GRADE_INIT = 1;
    const CUSTOMER_GRADE_MIDDLE = 2;
    const CUSTOMER_GRADE_ADVANCED = 3;
    const CUSTOMER_GRADE_VIP = 4;

    //来源：1后台添加 2后台导入 3其他
    const SOURCE_ADD_BACKGROUND = 1;
    const SOURCE_IMPORT_BACKGROUND = 2;
    const SOURCE_OTHER = 3;

    //状态：0禁用 1启用
    const STATUS_DISABLE = 0;
    const STATUS_ENABLE = 1;

    //性别：1男 2女
    const GENDER_MALE = 1;
    const GENDER_FEMALE = 2;

    //企业规模：1.微型企业（10人以下） 2.小型企业（10~99人） 3.中型企业（100~499人） 4.大型企业（500人及以上）
    const COMPANY_SCALE_MICRO = 1;
    const COMPANY_SCALE_SMALL = 2;
    const COMPANY_SCALE_MIDDLE = 3;
    const COMPANY_SCALE_BIG = 4;

    //所属行业：1.行政/事业单位 2.生产/工艺/制造业 3.医疗/护理/制药 4.金融/银行/投资/保险 5.文化/广告/传媒 6.娱乐/艺术/表演 7.律师/法务 8.教育/培训 9.其他
    const INDUSTRY_ADMINISTRATIVE = 1;
    const INDUSTRY_PRODUCTION = 2;
    const INDUSTRY_MEDICAL = 3;
    const INDUSTRY_FINANCE = 4;
    const INDUSTRY_CULTURE = 5;
    const INDUSTRY_ENTERTAINMENT = 6;
    const INDUSTRY_LAWYER = 7;
    const INDUSTRY_EDU = 8;
    const INDUSTRY_OTHER = 9;

    //客户级别：1.一级会员 2.二级会员
    const CUSTOMER_LEVEL_FIRST = 1;
    const CUSTOMER_LEVEL_SECOND = 2;

    //发卡行:1.工商银行 2.农业银行 3.中国银行 4.建设银行 5.邮储银行 6.兴业银行 7.招商银行 8.平安银行 9.厦门银行 10.厦门农商银行 11.厦门国际银行
    const BANK_ICBC = 1;
    const BANK_ABC = 2;
    const BANK_BOC = 3;
    const BANK_CCB = 4;
    const BANK_PSBC = 5;
    const BANK_CIB = 6;
    const BANK_CMB = 7;
    const BANK_PAB = 8;
    const BANK_XMB = 9;
    const BANK_RCB = 10;
    const BANK_XIB = 11;

    /**
     * @desc 银行TextMap
     * @return string[]
     *
     * <AUTHOR> 2025-06-24
     */
    public static function bankTextMap()
    {
        $data = array(
            self::BANK_ICBC => '工商银行',
            self::BANK_ABC => '农业银行',
            self::BANK_BOC => '中国银行',
            self::BANK_CCB => '建设银行',
            self::BANK_PSBC => '邮储银行',
            self::BANK_CIB => '兴业银行',
            self::BANK_CMB => '招商银行',
            self::BANK_PAB => '平安银行',
            self::BANK_XMB => '厦门银行',
            self::BANK_RCB => '厦门农商银行',
            self::BANK_XIB => '厦门国际银行',
        );

        return $data;
    }

    /**
     * @desc 银行
     * @param $code
     * @return string
     *
     * <AUTHOR> 2025-06-24
     */
    public static function bank($code)
    {
        $data = self::bankTextMap();
        return $data[$code] ?? '';
    }

    //客户级别TextMap
    public static function customerLevelTextMap()
    {
        $data = array(
            self::CUSTOMER_LEVEL_FIRST => '一级会员',
            self::CUSTOMER_LEVEL_SECOND => '二级会员',
        );

        return $data;
    }

    //客户级别
    public static function customerLevel($code)
    {
        $data = self::customerLevelTextMap();
        return $data[$code] ?? '';
    }

    //所属行业TextMap
    public static function industryTextMap()
    {
        $data = array(
            self::INDUSTRY_ADMINISTRATIVE => '行政/事业单位',
            self::INDUSTRY_PRODUCTION => '生产/工艺/制造业',
            self::INDUSTRY_MEDICAL => '医疗/护理/制药',
            self::INDUSTRY_FINANCE => '金融/银行/投资/保险',
            self::INDUSTRY_CULTURE => '文化/广告/传媒',
            self::INDUSTRY_ENTERTAINMENT => '娱乐/艺术/表演',
            self::INDUSTRY_LAWYER => '律师/法务',
            self::INDUSTRY_EDU => '教育/培训',
            self::INDUSTRY_OTHER => '其他',
        );

        return $data;
    }

    //所属行业
    public static function industry($code)
    {
        $data = self::industryTextMap();
        return $data[$code] ?? '';
    }

    //企业规模TextMap
    public static function companyScaleTextMap()
    {
        $data = array(
            self::COMPANY_SCALE_MICRO => '微型企业（10人以下）',
            self::COMPANY_SCALE_SMALL => '小型企业（10~99人）',
            self::COMPANY_SCALE_MIDDLE => '中型企业（100~499人）',
            self::COMPANY_SCALE_BIG => '大型企业（500人及以上）',

        );

        return $data;
    }

    //企业规模
    public static function companyScale($code)
    {
        $data = self::companyScaleTextMap();
        return $data[$code] ?? '';
    }

    public static function genderTextMap()
    {
        $data = array(
            self::GENDER_MALE => '男',
            self::GENDER_FEMALE => '女',

        );

        return $data;
    }

    public static function gender($code)
    {
        $data = self::genderTextMap();
        return $data[$code] ?? '';
    }

    public static function statusTextMap()
    {
        $data = array(
            self::STATUS_DISABLE => '禁用',
            self::STATUS_ENABLE => '启用',

        );

        return $data;
    }

    public static function status($code)
    {
        $data = self::statusTextMap();
        return $data[$code] ?? '';
    }

    public static function sourceTextMap()
    {
        $data = array(
            self::SOURCE_ADD_BACKGROUND => '后台添加',
            self::SOURCE_IMPORT_BACKGROUND => '后台导入',
            self::SOURCE_OTHER => '其他',
        );

        return $data;
    }

    public static function source($code)
    {
        $data = self::sourceTextMap();
        return $data[$code] ?? '';
    }

    public static function customerTypeTextMap()
    {
        $data = array(
            self::CUSTOMER_TYPE_INDIVIDUAL => '散客客户',
            self::CUSTOMER_TYPE_ENTERPRISE => '企业客户',
            self::CUSTOMER_TYPE_OFFICIAL => '公务客户',
            self::CUSTOMER_TYPE_DISTRIBUTION => '分销客户',
        );

        return $data;
    }

    public static function customerType($code)
    {
        $data = self::customerTypeTextMap();
        return $data[$code] ?? '';
    }

    public static function customerGradeTextMap()
    {
        $data = array(
            self::CUSTOMER_GRADE_INIT => '初始会员',
            self::CUSTOMER_GRADE_MIDDLE => '中级会员',
            self::CUSTOMER_GRADE_ADVANCED => '高级会员',
            self::CUSTOMER_GRADE_VIP => 'VIP会员',
        );

        return $data;
    }

    public static function customerGrade($code)
    {
        $data = self::customerGradeTextMap();
        return $data[$code] ?? '';
    }

    //自动生成会员卡号
    public function generateCardNo($customerType): string
    {
        $prefixArr = array(
            self::CUSTOMER_TYPE_INDIVIDUAL => 'PM',
            self::CUSTOMER_TYPE_ENTERPRISE => 'BM',
            self::CUSTOMER_TYPE_OFFICIAL => 'GP',
            self::CUSTOMER_TYPE_DISTRIBUTION => 'XX',
        );
        $prefix = $prefixArr[$customerType] ?? '';
        if (empty($prefix)) {
            throw new \Exception("未知类型");
        }
        do {
            // 生成8位随机数字
            $randomNo = mt_rand(0, 99999999);
            $randomNo = str_pad($randomNo, 8, '0', STR_PAD_LEFT);
            $cardCardNo = $prefix . $randomNo;
        } while ($this->isCardNoExists($cardCardNo, $customerType)); // 检查是否已存在

        return $cardCardNo;
    }

    //检查卡号是否已存在
    private function isCardNoExists($cardCardNo, $customerType)
    {
        $res = $this->where(['card_no' => $cardCardNo, 'customer_type' => $customerType])->first();
        return $res ? true : false;
    }

    //会员列表
    public function list($where, $field = [])
    {
        if (!empty($field)) {
            $this->select($field);
        }
        $this->handle_conditions($where);
        $this->orderBy('id', 'DESC');
        return $this->findAll();
    }

    //分页列表
    public function paginate_list($where, $page, $perPage)
    {
        $customerAccountModel = model('CustomerAccountModel');
        $userModel = model('UserModel');

        $this->handle_conditions($where);
        $this->orderBy('id', 'DESC');
        $list = $this->paginate($perPage, 'default', $page);
        if (empty($list)) {
            return [];
        }
        $customerIds = array_column($list, 'id');
        $customerAccounts = $customerAccountModel->whereIn('customer_id', $customerIds)->findAll();
        $customerAccounts = array_column($customerAccounts, null,'customer_id');
        //用户
        $userIds = array_column($list, 'bd_user_id');
        $users = $userModel->whereIn('id', $userIds)->findAll();
        $users = array_column($users, null,'id');

        //上级会员
        $parentIds = array_column($list, 'parent_id');
        $parents = $this->whereIn('id', $parentIds)->findAll();
        $parents = array_column($parents, null,'id');

        $data = [];
        foreach ($list as $value) {
            $customerId = $value['id'];
            $customerAccount = $customerAccounts[$customerId] ?? [];
            $credit_type_text = '';//授信类型
            $credit_balance = 0.00;//授信余额
            $cash_balance = 0.00;//预存金余额
            if (!empty($customerAccount)) {
                if ($customerAccount['credit_flag'] == CustomerAccountModel::CREDIT_FLAG_OPEN) {
                    $credit_type_text = '协议欠款';
                    $credit_balance = bcsub($customerAccount['credit_line'], $customerAccount['credit_used']);
                } elseif ($customerAccount['credit_flag'] == CustomerAccountModel::CREDIT_FLAG_UNOPEN) {
                    $credit_type_text = '未开通';
                } elseif ($customerAccount['status'] == CustomerAccountModel::STATUS_DISABLE) {
                    $credit_type_text = '已冻结';
                }
                $cash_balance = $customerAccount['cash_balance'];
            }

            $data[] = [
                'customer_id' => $customerId,
                'card_no' => $value['card_no'],
                'customer_name' => $value['customer_name'],//会员名称
                'parent_id' => $value['parent_id'],//上级会员
                'parent_name' => $parents[$value['parent_id']]['customer_name'] ?? '',//上级会员名称
                'customer_grade' => self::customerGrade($value['customer_grade']),//会员等级
                'contact_mobile' => $value['contact_mobile'],//会员手机号
                'cash_balance' => $cash_balance,//预存金余额
//                'credit_flag' => CustomerAccountModel::creditFlag($customerAccount['credit_flag']),//开通授信账户：0未开通 1开通
                'credit_type_text' => $credit_type_text,//授信类型
                'credit_balance' => $credit_balance,//授信余额
                'bd_user_name' =>  $users[$value['bd_user_id']]['name'] ?? '',//扩展业务员
                'source_text' => self::source($value['source']),//来源
                'related_company' => $value['related_company'],//所在公司
                'status' => $value['status'],//会员状态
                'status_text' => self::status($value['status']),//会员状态
                'user_id' => $users[$value['user_id']]['name'] ?? '',//操作员
                'created_at' => date('Y-m-d H:i:s', $value['created_at']),//注册时间
            ];
        }

        return $data;
    }

    private function handle_conditions($where)
    {
        foreach ($where as $k => $v) {
            switch ($k) {
                default:
                    $this->where($k, $v);
            }
        }
    }

}