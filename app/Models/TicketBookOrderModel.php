<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketBookOrderModel extends Model
{
    protected $table            = 'ticket_book_orders';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'int';

    const ORDER_AREA_DOMESTIC = 1; //国内
    const ORDER_AREA_INTL     = 2; //国际

    //订单状态
    const ORDER_STATUS = [0 => '订座中', 1 => '已订座', 2 => '已出票', 3 => '部分出票', 4 => '系统取消', 5 => '客户取消', 6 => '已完成'];
    //航程类型
    const JOURNEY_TYPES = [1 => '单程', 2 => '往返', 3 => '联程-两航段', 4 => '联程-多航段', 5 => '多航段'];
    //客户类型
    const CUSTOMER_TYPES = [1 => '自有', 2 => '分销'];
    //订单来源：1系统白屏预订 2分销白屏预订 3差旅白屏预订 4OTA订单 5B2B订单
    const ORDER_SOURCES = [1 => '系统白屏预订', 2 => '分销白屏预订', 3 => '差旅白屏预订', 4 => 'OTA订单', 5 => 'B2B订单'];

    //生成订单号 | T出票 R退款 V作废 改签C
    public function generate_order_no($prefix = 'T')
    {
        // 获取当前时间戳（10位）
        $timestamp = time();
        // 生成2位随机数补足12位
        $random = mt_rand(10, 99);
        // 组合成12位数字（时间戳后8位+随机2位+微秒2位）
        $micro  = substr(microtime(), 2, 2);
        $number = substr($timestamp, 2) . $random . $micro;
        // 确保正好12位
        $number = substr($number, 0, 12);

        return $prefix . $number;
    }

    //分页列表
    public function paginate_list($where, $page, $perPage)
    {
        $order_passenger_model = model('TicketBookPaxModel');
        $order_segment_model   = model('TicketBookSegModel');
        $flightModel           = model('FlightModel');

        $this->handle_conditions($where);
        $this->orderBy('id', 'DESC');
        $list = $this->paginate($perPage, 'default', $page);
        if (empty($list)) {
            return [];
        }
        $order_ids = array_column($list, 'id');
        //订单乘客
        $order_passenger_list = $order_passenger_model->whereIn('order_id', $order_ids)->findAll();
        $order_passengers     = [];
        foreach ($order_passenger_list as $val) {
            $order_id                      = $val['order_id'];
            $order_passengers[$order_id][] = [
                'order_id'      => $val['order_id'],
                'person_name'   => $val['person_name'],
                'ticket_number' => $val['ticket_number'],
            ];
        }
        //PNR-航段
        $order_segment_list = $order_segment_model->whereIn('order_id', $order_ids)->findAll();
        $flight_numbers     = array_column($order_segment_list, 'flight_number');

        //航班
        $flight_list = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();
        $flight_list = array_column($flight_list, null, 'flight_number');

        $pnr_segments = [];
        foreach ($order_segment_list as $val) {
            $flight_number             = $val['flight_number'];
            $order_id                  = $val['order_id'];
            $pnr_segments[$order_id][] = [
                'order_id'           => $val['order_id'],
                'flight_number'      => $flight_number,
                'departure_airport'  => $flight_list[$flight_number]['departure_airport'],//出发机场代码
                'arrival_airport'    => $flight_list[$flight_number]['arrival_airport'],//到达机场代码
                'cabin'              => $val['cabin'],//舱位
                'departure_datetime' => explode(' ', $val['departure_datetime'])[0],//出发日期
            ];
        }
        $data = [];
        foreach ($list as $value) {
            $data[] = [
                'order_id'              => $value['id'],
                'order_no'              => $value['order_no'],
                'pnr'                   => $value['pnr'],
                'airline_pnr'           => $value['airline_pnr'],//大编码
                'operator_name'         => $value['operator_name'],//订票员
                'created_at'            => date('Y-m-d H:i:s', $value['created_at']),//下单时间
                'status'                => $value['status'],//订单状态
                'status_text'           => self::ORDER_STATUS[$value['status']],
                'journey_type'          => $value['journey_type'],//航程类型 1单程 2往返 3联程-两航段 4联程-多航段 5多航段
                //'journey_info' => implode("-", str_split($value['journey_info'], 3)), //航程
                'journey_type_text'     => self::JOURNEY_TYPES[$value['journey_type']],
                'total_customer_amount' => $value['total_customer_amount'],//订单总额
                'order_passengers'      => $order_passengers[$value['id']],//乘客票号信息
                'pnr_segments'          => $pnr_segments[$value['id']],//航程信息
                'customer_type'         => self::CUSTOMER_TYPES[$value['customer_type']],//客户类型
                'member_card_no'        => '123456',//会员卡号
            ];
        }

        return $data;
    }

    //按状态分组统计
    public function total_status($where)
    {
        $this->handle_conditions($where);
        $total_list = $this->select('status, COUNT(*) as total')
                           ->groupBy('status')
                           ->orderBy('total', 'DESC')
                           ->findAll();
        $total_list = array_column($total_list, null, 'status');
        $data       = [];
        foreach (self::ORDER_STATUS as $key => $value) {
            $total  = $total_list[$key]['total'] ?? 0;
            $data[] = [
                'status'      => $key,
                'status_text' => $value,
                'total'       => $total,
            ];
        }

        return $data;
    }

    private function handle_conditions($where)
    {
        foreach ($where as $k => $v) {
            switch ($k) {
                case 'created_at_from':
                    $this->where('created_at >=', $v);
                    break;
                case 'created_at_to':
                    $this->where('created_at <=', $v);
                    break;
                case 'ids':
                    if ($v) {
                        $this->whereIn('id', $v);
                    }
                    break;
                default:
                    $this->where($k, $v);
            }
        }
    }

}