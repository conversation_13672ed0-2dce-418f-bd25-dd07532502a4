<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketRebookPaxModel extends Model {
    protected $table = 'ticket_rebook_pax';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat = 'int'; 

    //旅客类型：1成人 2儿童 3婴儿
    const PASSENGER_TYPE = [1=>'成人', 2=>'儿童', 3=>'婴儿'];
}