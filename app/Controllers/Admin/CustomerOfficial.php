<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
use App\Models\CustomerModel;

/**
 * @desc 公务会员
 *
 * <AUTHOR> 2025-06-18
 */
class CustomerOfficial extends AdminController
{
    /**
     * @desc 公务会员列表
     *
     * <AUTHOR> 2025-06-09
     */
    public function list()
    {
        $validation = service('validation');
        $rules = [
            'page' => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page' => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'customer_grade' => ['label' => '会员等级', 'rules' => 'permit_empty|in_list[1,2,3,4]'],//会员等级：1初始会员 2中级会员 3高级会员 4VIP会员
            'card_no' => ['label' => '会员卡号', 'rules' => 'permit_empty|max_length[20]|min_length[4]'],
            'customer_name' => ['label' => '会员名称', 'rules' => 'permit_empty|max_length[20]|min_length[2]'],
            'contact_mobile' => ['label' => '会员手机号', 'rules' => 'permit_empty|max_length[20]|min_length[4]'],
            'bd_user_id' => ['label' => '业务拓展员ID', 'rules' => 'permit_empty|greater_than[0]'],//业务拓展员ID
            'source' => ['label' => '会员来源', 'rules' => 'permit_empty|in_list[1,2,3]'],//来源：1后台添加 2后台导入 3其他
            'status' => ['label' => '会员状态', 'rules' => 'permit_empty|in_list[1,0]'],//状态：0禁用 1启用
            'parent_id' => ['label' => '上级会员', 'rules' => 'permit_empty|greater_than[0]'],//上一级客户ID
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Type\Official $customerService */
        $customerService = load_service('Customer\Type\Official');
        $data = $customerService->list($validData);

        success('获取成功', $data);
    }

    /**
     * @desc 新增公务会员
     *
     * <AUTHOR> 2025-06-18
     */
    public function create()
    {
        $validation = service('validation');
        $rules = [
            'contact_mobile' => ['label' => '会员手机号', 'rules' => 'required|max_length[20]|min_length[4]'],
            'customer_level' => ['label' => ' 客户级别', 'rules' => 'required|in_list[1,2]'],//客户级别：1.一级会员 2.二级会员
            'parent_id' => ['label' => '上级会员', 'rules' => 'permit_empty|greater_than_equal_to[0]'],
            'customer_name' => ['label' => '会员名称', 'rules' => 'required|max_length[20]|min_length[2]'],
            'customer_grade' => ['label' => '会员等级', 'rules' => 'required|in_list[1,2,3,4]'],//会员等级：1初始会员 2中级会员 3高级会员 4VIP会员
            'customer_name_en' => ['label' => '英文名称', 'rules' => 'permit_empty|max_length[30]|min_length[2]'],
            'gender' => ['label' => '会员性别', 'rules' => 'required|in_list[1,2]'],//性别：1男 2女
            'email' => ['label' => '电子邮箱', 'rules' => 'permit_empty|max_length[20]|min_length[4]'],
            'weixin' => ['label' => '微信账号', 'rules' => 'permit_empty|max_length[20]|min_length[2]'],
            'country' => ['label' => '会员国籍', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'cash_flag' => ['label' => '预存金账户', 'rules' => 'required|in_list[1,0]'],//开通预存金账户：0未开通 1开通
            'credit_flag' => ['label' => '授信账户', 'rules' => 'required|in_list[1,0]'],//开通授信账户：0未开通 1开通
            'temp_credit_flag' => ['label' => '临时授信账户', 'rules' => 'required|in_list[1,0]'],//开通临时授信账户：0未开通 1开通
            'bank_card_no' => ['label' => '公务卡号', 'rules' => 'permit_empty|max_length[20]|min_length[2]'],//公务卡号
            'issuing_bank' => ['label' => '发卡行', 'rules' => 'permit_empty|in_list[1,2,3,4,5,6,7,8,9,10,11]'],//发卡行:1.工商银行 2.农业银行 3.中国银行 4.建设银行 5.邮储银行 6.兴业银行 7.招商银行 8.平安银行 9.厦门银行 10.厦门农商银行 11.厦门国际银行
            'bd_user_id' => ['label' => '业务拓展员ID', 'rules' => 'permit_empty|greater_than[0]'],//业务拓展员ID
            'source' => ['label' => '会员来源', 'rules' => 'permit_empty|in_list[1,2,3]'],//来源：1后台添加 2后台导入 3其他
            'client_manager' => ['label' => '客户经理', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'province' => ['label' => '所在省份', 'rules' => 'permit_empty|max_length[10]|min_length[2]'],
            'city' => ['label' => '所在城市', 'rules' => 'permit_empty|max_length[10]|min_length[2]'],
            'contact_address' => ['label' => '联系地址', 'rules' => 'permit_empty|max_length[100]|min_length[2]'],
            'related_company' => ['label' => '所在公司', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'related_company_telephone' => ['label' => '所在公司电话', 'rules' => 'permit_empty|max_length[50]|min_length[5]'],
            'related_compnay_address' => ['label' => '所在公司地址', 'rules' => 'permit_empty|max_length[50]|min_length[5]'],
            'remark' => ['label' => '备注', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'avatar_img' => ['label' => '头像', 'rules' => 'permit_empty|max_length[70]|min_length[5]'],
            'idcard_images' => ['label' => '身份证图片', 'rules' => 'permit_empty|is_array'],
            'idcards' => ['label' => '证件信息', 'rules' => 'required|is_array'],
            'idcards.*.card_type' => [
                'label' => '证件类型',
                'rules' => 'required|in_list[1,2,3,4,5,6,7]'
            ],
            'idcards.*.card_no' => [
                'label' => '证件号码',
                'rules' => 'required|max_length[20]|min_length[6]'
            ],
            'idcards.*.issuing_country' => [
                'label' => '证件签发国',
                'rules' => 'required|max_length[2]|min_length[2]'
            ],
            'idcards.*.expiration_date' => [
                'label' => '证件有效期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Type\Official $customerService */
        $customerService = load_service('Customer\Type\Official');
        try {
            $insertCustomerId = $customerService->create($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
        success('创建成功', ['customer_id' => $insertCustomerId]);
    }

    /**
     * @desc 修改公务会员
     *
     * <AUTHOR> 2025-06-18
     */
    public function update()
    {
        $validation = service('validation');
        $rules = [
            'customer_id' => ['label' => 'customer_id', 'rules' => 'required|greater_than[0]'],
            'contact_mobile' => ['label' => '会员手机号', 'rules' => 'required|max_length[20]|min_length[4]'],
            'customer_level' => ['label' => ' 客户级别', 'rules' => 'required|in_list[1,2]'],//客户级别：1.一级会员 2.二级会员
            'parent_id' => ['label' => '上级会员', 'rules' => 'permit_empty|greater_than_equal_to[0]'],
            'customer_name' => ['label' => '会员名称', 'rules' => 'required|max_length[20]|min_length[2]'],
            'customer_grade' => ['label' => '会员等级', 'rules' => 'required|in_list[1,2,3,4]'],//会员等级：1初始会员 2中级会员 3高级会员 4VIP会员
            'customer_name_en' => ['label' => '英文名称', 'rules' => 'permit_empty|max_length[30]|min_length[2]'],
            'gender' => ['label' => '会员性别', 'rules' => 'required|in_list[1,2]'],//性别：1男 2女
            'email' => ['label' => '电子邮箱', 'rules' => 'permit_empty|max_length[20]|min_length[4]'],
            'weixin' => ['label' => '微信账号', 'rules' => 'permit_empty|max_length[20]|min_length[2]'],
            'country' => ['label' => '会员国籍', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'cash_flag' => ['label' => '预存金账户', 'rules' => 'required|in_list[1,0]'],//开通预存金账户：0未开通 1开通
            'credit_flag' => ['label' => '授信账户', 'rules' => 'required|in_list[1,0]'],//开通授信账户：0未开通 1开通
            'temp_credit_flag' => ['label' => '临时授信账户', 'rules' => 'required|in_list[1,0]'],//开通临时授信账户：0未开通 1开通
            'bank_card_no' => ['label' => '公务卡号', 'rules' => 'permit_empty|max_length[20]|min_length[2]'],//公务卡号
            'issuing_bank' => ['label' => '发卡行', 'rules' => 'permit_empty|in_list[1,2,3,4,5,6,7,8,9,10,11]'],//发卡行:1.工商银行 2.农业银行 3.中国银行 4.建设银行 5.邮储银行 6.兴业银行 7.招商银行 8.平安银行 9.厦门银行 10.厦门农商银行 11.厦门国际银行
            'bd_user_id' => ['label' => '业务拓展员ID', 'rules' => 'permit_empty|greater_than[0]'],//业务拓展员ID
            'source' => ['label' => '会员来源', 'rules' => 'permit_empty|in_list[1,2,3]'],//来源：1后台添加 2后台导入 3其他
            'client_manager' => ['label' => '客户经理', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'province' => ['label' => '所在省份', 'rules' => 'permit_empty|max_length[10]|min_length[2]'],
            'city' => ['label' => '所在城市', 'rules' => 'permit_empty|max_length[10]|min_length[2]'],
            'contact_address' => ['label' => '联系地址', 'rules' => 'permit_empty|max_length[100]|min_length[2]'],
            'related_company' => ['label' => '所在公司', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'related_company_telephone' => ['label' => '所在公司电话', 'rules' => 'permit_empty|max_length[50]|min_length[5]'],
            'related_compnay_address' => ['label' => '所在公司地址', 'rules' => 'permit_empty|max_length[50]|min_length[5]'],
            'remark' => ['label' => '备注', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'avatar_img' => ['label' => '头像', 'rules' => 'permit_empty|max_length[70]|min_length[5]'],
            'idcard_images' => ['label' => '身份证图片', 'rules' => 'permit_empty|is_array'],
            'idcards' => ['label' => '证件信息', 'rules' => 'required|is_array'],
            'idcards.*.card_type' => [
                'label' => '证件类型',
                'rules' => 'required|in_list[1,2,3,4,5,6,7]'
            ],
            'idcards.*.card_no' => [
                'label' => '证件号码',
                'rules' => 'required|max_length[20]|min_length[6]'
            ],
            'idcards.*.issuing_country' => [
                'label' => '证件签发国',
                'rules' => 'required|max_length[2]|min_length[2]'
            ],
            'idcards.*.expiration_date' => [
                'label' => '证件有效期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
        ];
        $validation->setRules($rules);

        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Type\Official $customerService */
        $customerService = load_service('Customer\Type\Official');
        try {
            $customerId = $customerService->update($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('修改成功', ['customer_id' => $customerId]);
    }

    /**
     * @desc 公务会员明细
     *
     * <AUTHOR> 2025-06-18
     */
    public function detail()
    {
        $validation = service('validation');
        $rules = [
            'customer_id' => ['label' => 'customer_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\Customer\Type\Official $customerService */
        $customerService = load_service('Customer\Type\Official');
        try {
            $data = $customerService->detail($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('获取成功', $data);
    }

    /**
     * @desc 导入公务会员
     *
     * <AUTHOR> 2025-06-18
     */
    public function import()
    {
        $validationRule = [
            'customer_file' => [
                'label' => 'Excel File',
                'rules' => [
                    'uploaded[customer_file]',
                    'mime_in[customer_file,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel]',
                    'max_size[customer_file,10240]', // 限制10MB
                    'ext_in[customer_file,xlsx,xls]',
                ],
            ],
        ];
        if (!$this->validate($validationRule)) {
            error(0, $this->validator->getErrors());
        }

        $file = $this->request->getFile('customer_file');
        if (!$file) {
            error(0,'请上传文件,文件KEY名【customer_file】');
        }
        if (!$file->isValid()) {
            error(0,$file->getErrorString());
        }
        /** @var \App\Services\Customer\Type\Official $customerService */
        $customerService = load_service('Customer\Type\Official');
        try {
            $result = $customerService->import($file);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
        success('导入成功', $result);
    }

    public function export()
    {
        $validation = service('validation');
        $rules = [
            'customer_ids' => ['label' => '会员ids', 'rules' => 'required|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Type\Official $customerService */
        $customerService = load_service('Customer\Type\Official');
        try {
            $customerService->export($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
        success('导出成功');
    }
}