<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
use App\Helpers\Tools;
use App\Models\CabinModel;

class Flight extends AdminController
{
    //机票预订列表
    public function domestic_list(): string
    {
        $validation = service('validation');
        $rules = [
                'departure_airport' => ['label' => '出发机场', 'rules' => 'required|max_length[3]|min_length[3]'],
                'arrival_airport' => ['label' => '到达机场', 'rules' => 'required|max_length[3]|min_length[3]'],
                'departure_date' => ['label' => '出发日期', 'rules' => 'required|valid_date[Y-m-d]'],
                'airline' => ['label' => '航空公司', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
                'journey_type' => ['label' => '行程类型', 'rules' => 'required|in_list[OW,RT,TS,MS]'],
                // 'is_direct' => ['label' => '是否直达', 'rules' => 'required|in_list[0,1]'],
                'cabin_level' => ['label' => '舱位等级', 'rules' => 'permit_empty|in_list[1,2,3]']
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $departure_airport = trim($validData['departure_airport']);
        $arrival_airport = trim($validData['arrival_airport']);
        $departure_date = trim($validData['departure_date']);
        $airline = trim($validData['airline']);
        $journey_type = trim($validData['journey_type']);
        // $is_direct = intval($validData['is_direct']) == 1 ? true : false;
        $cabin_level = intval($validData['cabin_level']);

        $airportModel = model('AirportModel');
        $airlineModel = model('AirlineModel');
        $flightModel = model('FlightModel');
        $cabinModel = model('CabinModel');

        //校验参数有效性
        if (empty($airportModel->where(['airport_code' => $departure_airport])->first())) {
            error(0, '非法departure_airport');
        }
        if (empty($airportModel->where(['airport_code' => $arrival_airport])->first())) {
            error(0, '非法arrival_airport');
        }
        if ($airline && empty($airlineModel->where(['airline_code' => $airline])->first())) {
            error(0, '非法airline');
        }
        $params = [
            'departure_airport' => $departure_airport,
            'arrival_airport' => $arrival_airport,
            'departure_date' => $departure_date,
            'airline' => $airline,
            'is_direct' => true,
            'journey_type' => $journey_type,
        ];
        
        $shopping = new \App\Libraries\Api\IBE\Shopping();
        $list = $shopping->domestic_shopping($params);

        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');

        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');

        // //舱位等级下拉
        // $cabin_level_filter = [
        //     ['3' => '头等舱'],
        //     ['2' => '公务舱'], 
        //     ['1' => '经济舱']
        // ];

        //起飞时段下拉筛选
        $take_off_filter = [
            ['code' =>'1', 'name' => '上午(00:00-12:00)'],
            ['code' =>'2', 'name' => '中午(12:00-14:00)'],
            ['code' =>'3', 'name' => '下午(14:00-18:00)'],
            ['code' =>'4', 'name' => '晚上(18:00-24:00)']
        ];
        $data = [
            'airline_filter' => [],
            'take_off_filter' => $take_off_filter,
            'list' => []
        ];
        $airline_codes = [];
        $flight_numbers = [];
        $flight_list = [];
        if (!empty($list)) {
            foreach ($list as $k => $v) {
                foreach ($v['flight'] as $k1 => $v1) {
                    $airline_codes[] = $v1['airline'];////航司编号2位字母,例MF
                    //航班编号(airline_code + flight_number)
                    $flight_numbers[] = $v1['airline'] . $v1['flight_number'];//例CZ3367
                    $flight_list[] = $v1;
                }
            }
            //航空下拉筛选
            $airline_codes = array_unique($airline_codes);
            $ac_tmp = [];
            foreach ($airline_codes as $ac) {
                $ac_tmp[] = [
                    'code' => $ac,
                    'name' => $airlines[$ac]['airline_cn']
                ];
            }
            $data['airline_filter'] = $ac_tmp;

            //$flight、$cabins信息
            $flights = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();//航班信息
            $flights = array_column($flights, null, 'flight_number');
            $cabins = $cabinModel->whereIn('airline', $airline_codes)->findAll();//舱位表信息
            $cabinsArr = [];
            foreach ($cabins as $val) {
                $tmp_key = $val['airline'] . $val['cabin'];
                $cabinsArr[$tmp_key] = $val;
            }
            foreach ($flight_list as $k1 => $v1) {
                $airline_code = $v1['airline'];//航司编号2位字母
                $v1['airline_cn'] = $airlines[$airline_code]['airline_cn'];//航空公司名称
                $v1['departure_airport_cn'] = $airports[$v1['departure_airport']]['city_cn'] . $airports[$v1['departure_airport']]['airport_name_cn'];//城市+出发机场名称
                $v1['arrival_airport_cn'] = $airports[$v1['arrival_airport']]['city_cn'] . $airports[$v1['arrival_airport']]['airport_name_cn'];//城市+到达机场名称
                //航班编号(airline_code + flight_number)
                $v1['flight_number'] = $airline_code . $v1['flight_number'];//例CZ3367
                $flight_numbers[] = $v1['flight_number'];
                //航司logo
                $v1['airline_logo'] = '/static/images/airline/' . $airline_code . '.png';
                //出发到达时间、间隔时间、机型、起飞时段
                $flight_number = $v1['flight_number'];
                $flight = $flights[$flight_number] ?? [];
                if (empty($flight)) {
                    $v1['departure_time'] = '';
                    $v1['arrival_time'] = '';
                    $v1['interval'] = '';
                    $v1['air_equip_type'] = '';
                    $v1['take_off_code'] = '';
                } else {
                    $v1['departure_time'] = $flight['departure_time'];//出发时间
                    $v1['arrival_time'] = $flight['arrival_time'];//到达时间
                    $interval =  Tools\Time::compute_interval($flight['departure_time'], $flight['arrival_time']);//时间间隔
                    $hours = $interval['hours'] ? $interval['hours'] . 'h' : '';
                    $minutes = $interval['minutes'] ? $interval['minutes'] . 'm' : '';
                    $v1['interval'] = $hours.$minutes;
                    $v1['air_equip_type'] = $flight['air_equip_type'];//机型

                    //起飞时段标识
                    if ($flight['departure_time'] >= '00:00' && $flight['departure_time'] < '12:00') {
                        $v1['take_off_code'] = '1';
                    } elseif ($flight['departure_time'] >= '12:00' && $flight['departure_time'] < '14:00') {
                        $v1['take_off_code'] = '2';
                    } elseif ($flight['departure_time'] >= '14:00' && $flight['departure_time'] < '18:00') {
                        $v1['take_off_code'] = '3';
                    } elseif ($flight['departure_time'] >= '18:00' && $flight['departure_time'] <= '24:00') {
                        $v1['take_off_code'] = '4';
                    }
                }
                //计算最低价、税后金额、折扣、行李重量
                $min_price_info = [];//最低报价
                $economy_cabin = [];//经济舱
                $business_first_cabin = [];//公务/头等舱
                foreach ($v1['cabin'] as $k2 => $v2) {
                    $cabin_no = $k2;//舱位号，如F
                    $cabin = $cabinsArr[$airline_code.$cabin_no] ?? [];
                    if (empty($cabin)) {
                        error(0, "未知航司舱位{$airline_code}_{$cabin_no}");
                    }
                    //税后价格=票价+机场建设费+燃油附加费
                    $after_tax_price = $v2['price'];
                    $unit = '';//货币单位，如$、￥
                    foreach ($v2['texes'] as $texe) {
                        $after_tax_price = bcadd($after_tax_price, $texe['amount'], 2);
                        $unit = $texe['currency'] == 'CNY' ? '￥' : '$';
                    }
                    $v2['after_tax_price'] = $after_tax_price;//税后价格
                    $v2['unit'] = $unit;//货币单位
                    $v2['discount'] = $cabin['discount'] . '折';//折扣
                    $v2['luggage_weight'] = $cabin['luggage_weight'];//行李重量
                    $v2['addition_price'] = 0; //加价
                    $v2['commision'] = 0; //佣金
                    $v2['ext_commision'] = 0; //额外佣金
                    $v2['after_commision'] = 0; //后返
                    $v2['refund_rate'] = '退票10%-45%'; //退票范围 TODO:需要计算出来，此处暂写死

                    if (empty($min_price_info) || $v2['price'] < $min_price_info['price']) {
                        $min_price_info = [
                            'cabin_no' => $cabin_no,//舱位号
                            'price' => $v2['price'],//最低票价
                            'after_tax_price' => $after_tax_price,//税后价格
                            'unit' => $unit,
                            'discount' => $v2['discount'],//折扣
                            'luggage_weight' => $v2['luggage_weight'],//行李重量
                            'addition_price' => $v2['addition_price'],//加价
                            'commision' => 0,//佣金
                            'ext_commision' => $v2['ext_commision'],//额外佣金
                            'after_commision' => $v2['after_commision'],//后返
                        ];
                    }
                    $v2['cabin_no'] = $cabin_no;
                    $v2['product_no'] = $cabin_no;//产品编号
                    $v2['flight_number'] = $v1['flight_number'];//航班号


                    //分拣舱型tab
                    if ($cabin['cabin_grade_id'] == 1) {//舱位等级ID：1经济舱 2公务舱 3头等舱
                        $economy_cabin[] = [
                            'price' => [$v2]
                        ];
                    } elseif (in_array($cabin['cabin_grade_id'],[2,3])) {
                        $business_first_cabin[] = [
                            'price' => [$v2]
                        ];
                    }
                }

                //过滤舱位等级
                if ($cabin_level == CabinModel::CABIN_ECONOMY) {
                    $business_first_cabin = [];
                } elseif (in_array($cabin_level, [CabinModel::CABIN_BUSINESS, CabinModel::CABIN_FIRST])) {
                    $economy_cabin = [];
                }

                $v1['cabin'] = [
                    'economy_cabin' => $economy_cabin,
                    'business_first_cabin' => $business_first_cabin
                ];
                //共享航班标识
                if (!empty($v1['code_share']) && $v1['code_share']['airline'] == $v1['airline'] && $v1['code_share']['flight_number'] == $v1['flight_number']) {
                    $v1['is_share'] = true;
                } else {
                    $v1['is_share'] = false;
                }
                $v1['min_price_info'] = $min_price_info;//最低报价信息
                $flight_list[$k1] = $v1;
            }

            $data['list'] = $flight_list;
        }

        success('获取成功', $data);
    }

    //指定舱位更多价格
    public function cabin_prices(): string 
    {
        $validation = service('validation');
        $rules = [
            'cabins' => ['label' => '舱位信息', 'rules' => 'required|is_array'],
            'cabins.*.flight_number' => [
                'label' => '航班号',
                'rules' => 'required|min_length[6]|max_length[30]'
            ],
            'cabins.*.cabin' => [
                'label' => '舱位',
                'rules' => 'required|min_length[1]|max_length[30]'
            ],
            'cabins.*.departure_date' => [
                'label' => '出发日期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $cabins = $validData['cabins'];

        $flightModel = model('FlightModel');
        $cabinModel = model('CabinModel');

        //校验参数有效性
        //校验航班编号
        $flight_numbers = array_unique(array_column($cabins, 'flight_number'));
        $flights = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();
        $flights = array_column($flights, null, 'flight_number');
        if (count($flight_numbers) != count($flights)) {
            error(0, '航班号有误');
        }      
        //校验舱位信息
        $airline_codes = array_unique(array_column($flights, 'airline_code'));
        $cabin_list = $cabinModel->whereIn('airline', $airline_codes)->findAll();
        $cabinsArr = [];
        foreach ($cabin_list as $val) {
            $tmp_key = $val['airline'] . $val['cabin'];
            $cabinsArr[$tmp_key] = $val;
        }
        foreach ($cabins as $val) {
            $airline_code = $flights[$val['flight_number']]['airline_code'];
            $tmp_key = $airline_code . $val['cabin'];
            if (!isset($cabinsArr[$tmp_key])) {
                error(0, "未知航司舱位{$tmp_key}");
            }
        }

        $flight_airport_map = [];//航班和机场代号map
        $params = [];
        foreach ($cabins as $val) {
            $departure_date = $val['departure_date'];
            $flight_number = $val['flight_number'];
            $flight = $flights[$flight_number];

            $arrival_date = $departure_date;//TODO:默认到达时间等于出发时间，如果到达时间小于出发时间默认加1天，后续要改成读取配置获取
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrival_date = strtotime($departure_date . ' +1 day');
                $arrival_date = date('Y-m-d', $arrival_date);
            }
            $params[] = [
                'passenger_type' => 'ADT',//默认成人价
                'departure_datetime' => $departure_date . 'T' . $flight['departure_time'] . ':00', //出发时间
                'arrival_datetime' => $arrival_date . 'T' . $flight['arrival_time'] . ':00', //到达时间
                'flight_number' => $flight_number, //航班号
                'departure_airport' => $flight['departure_airport'], //出发机场
                'arrival_airport' => $flight['arrival_airport'], //到达机场
                'air_equip_type' => $flight['air_equip_type'], //机型
                'class' => $val['cabin'], // 舱位
                // 'sub_class' => 'V1', // 子舱位（可选）
            ];
            $flight_airport_map[$flight['departure_airport'] . $flight['arrival_airport']] = $flight_number;
        }
        $fare = new \App\Libraries\Api\IBE\Pricing();
        $list = $fare->query_domestic_price_by_segment($params);

        $data = [];
        foreach ($list as $key => $val) {
            $tmp = explode('-', $key);
            $airline_code = $tmp[0];//航空公司
            $departure_airport = $tmp[1];//出发机场
            $arrival_airport = $tmp[2];//到达机场
            foreach ($val as $key1 => $val1) {
                $product_no = $key1;//产品编号
                $price = $val1['price']['amount'];//票面价
                $texes = $val1['price']['texes'];//税费信息
                $remark = $val1['price']['remark'];
                //税后价格=票价+机场建设费+燃油附加费
                $after_tax_price = $price;
                $unit = '';//货币单位，如$、￥
                foreach ($texes as $texe) {
                    $after_tax_price = bcadd($after_tax_price, $texe['amount'], 2);
                    $unit = $texe['currency'] == 'CNY' ? '￥' : '$';
                }
                $airport_key = $departure_airport . $arrival_airport;
                if (!isset($flight_airport_map[$airport_key])) {
                    error(0, '接口返回的出发到达机场和当前航班信息不匹配');
                }
                $data[] = [
                    'airline_code' => $airline_code,
                    'departure_airport' => $departure_airport,
                    'arrival_airport' => $arrival_airport,
                    'product_no' => $product_no,//产品编号
                    'price' => floatval($price),
                    'after_tax_price' => $after_tax_price,
                    'addition_price' => 0,//加价
                    'commision' => 0,//佣金
                    'ext_commision' => 0,//额外佣金
                    'after_commision' => 0,//后返
                    'luggage_weight' => '',
                    'discount' => '',//折扣
                    'unit' => $unit,
                    'texes' => $texes,
                    'remark' => $remark,
                    'fare_type' => $val1['fare_type'],
                    'flight_number' => $flight_airport_map[$airport_key],
                ];
            }
        }
        success('获取成功', $data);
    }

    //退改签规则
    public function refund_change_rules() 
    {
        $validation = service('validation');
        $rules = [
            'cabin' => ['label' => '舱位', 'rules' => 'required|max_length[6]|min_length[1]'],
            'airline' => ['label' => '航空公司', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $cabin = trim($validData['cabin']);
        $airline = trim($validData['airline']);

        $data = [
            'adult_policy' => [
                'refund' => [
                    ['before_hours' => 24, 'fee' => 144, 'currency' => 'CNY', 'unit' => '￥'],
                    ['before_hours' => 72, 'fee' => 288, 'currency' => 'CNY', 'unit' => '￥']
                ],
                'change' => [
                    ['before_hours' => 24, 'fee' => 100, 'currency' => 'CNY', 'unit' => '￥'],
                    ['before_hours' => 72, 'fee' => 100, 'currency' => 'CNY', 'unit' => '￥']
                ],
                'change_condition' => '不得签转'
            ],
            'baggage' => [
                '免费手提行李额:每件8KG，每人1件。单件行李质量不超过8KG，单件体积不超过20x40x55CM',
                '免费托运行李额: 总重20KG，不限件数。单件托运行李质量不超过50KG，单件体积不超过40x60x100CM'
            ],
            'remarks' => '持婴儿客票的旅客，无免费行李额。每个婴儿可免费托运婴儿手推车一辆'
        ];

        success('获取成功', $data);
    }

    //检查价格
    public function check_price()
    {
        $validation = service('validation');
        $rules = [
            'cabins' => ['label' => '舱位信息', 'rules' => 'required|is_array'],
            'cabins.*.flight_number' => [
                'label' => '航班号',
                'rules' => 'required|min_length[6]|max_length[30]'
            ],
            'cabins.*.cabin' => [
                'label' => '舱位',
                'rules' => 'required|min_length[1]|max_length[30]'
            ],
            'cabins.*.departure_date' => [
                'label' => '出发日期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
            'cabins.*.price' => [
                'label' => '票价',
                'rules' => 'required|numeric|greater_than[0]',
            ],
            'cabins.*.product_no' => [
                'label' => '产品编号',
                'rules' => 'required|min_length[1]|max_length[30]'
            ],
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $cabins = $validData['cabins'];
        $flightModel = model('FlightModel');
        $airportModel = model('AirportModel');
        $airlineModel = model('AirlineModel');
        
        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');
        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');

        $data = [];
        try {
            foreach ($cabins as $val) {
                $price = $flightModel->check_price($val, 'ADT', true);//成人价
                $children_price = $flightModel->check_price($val, 'CHD', false);
                $baby_price = $flightModel->check_price($val, 'INF', false);

                $interval = Tools\Time::compute_interval($price['departure_time'], $price['arrival_time']);//时间间隔
                $hours = $interval['hours'] ? $interval['hours'] . 'h' : '';
                $minutes = $interval['minutes'] ? $interval['minutes'] . 'm' : '';

                //航司logo
                $airline_logo = '';
                $logoPath = 'static/images/airline/' . $price['airline_code'] . '.png';
                if (file_exists(FCPATH . $logoPath)) {
                    $airline_logo = base_url($logoPath);
                }
                $data[] = [
                    'price' => $price['price'],
                    'texes' => $price['texes'],
                    'children_price' => $children_price['price'],
                    'baby_price' => $baby_price['price'],
                    'airline_cn' => $airlines[$price['airline_code']]['airline_cn'],//航空公司名称
                    'departure_airport_cn' => $airports[$price['departure_airport']]['city_cn'] . $airports[$price['departure_airport']]['airport_name_cn'],//城市+出发机场名称
                    'arrival_airport_cn' => $airports[$price['arrival_airport']]['city_cn'] . $airports[$price['arrival_airport']]['airport_name_cn'],//城市+到达机场名称
                    'airline_logo' => $airline_logo,
                    'meal' => '有餐食',//TODO:需要计算出来，此处暂写死
                    'refund_rate' => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                    'departure_date' => $price['departure_date'],//出发日期
                    'cabin' => $price['cabin'],//舱位号
                    'flight_number' => $price['flight_number'],
                    'product_no' => $price['product_no'],
                    'departure_time' => $price['departure_time'],
                    'arrival_time' => $price['arrival_time'],
                    'interval' => $hours.$minutes,
                    'air_equip_type' => $price['air_equip_type'],
                    'luggage_weight' => $price['luggage_weight'],
                    'discount' => $price['discount'],
                    'airline_code' => $price['airline_code'],
                    'departure_airport' => $price['departure_airport'],
                    'arrival_airport' => $price['arrival_airport'],
                ];
            }

        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('获取成功', $data); 
    }

    //国内改签变更搜索航班
    public function domestic_reshop()
    {
        $validation = service('validation');
        $rules = [
            'order_id' => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'rebook_type' => ['label' => '改签原因', 'rules' => 'required|in_list[1,2]'],//改签类型：1自愿改签 2被动改签
            'ticket_ids' => ['label' => '票号ids', 'rules' => 'required|is_array'],
            'flight_segments' => ['label' => '航段信息', 'rules' => 'required|is_array'],
            'flight_segments.*.departure_date' => [
                'label' => '新出发日期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
            'flight_segments.*.pnr_segment_id' => [
                'label' => '航段id',
                'rules' => 'required|greater_than[0]'
            ],
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id = intval($validData['order_id']);
        $rebook_type = intval($validData['rebook_type']);
        $ticket_ids = $validData['ticket_ids'];
        $flight_segments = $validData['flight_segments'];

        $order_segment_model = model('TicketBookSegModel');
        $order_passenger_model = model('TicketBookPaxModel');
        $flightModel = model('FlightModel');
        $pnr_passenger_model = model('PnrPassengerModel');

        $airportModel = model('AirportModel');
        $cabinModel = model('CabinModel');

        //校验参数有效性
        $order_segment_ids = array_column($flight_segments, 'pnr_segment_id');
        $order_segments = $order_segment_model->whereIn('id', $order_segment_ids)->where('order_id', $order_id)->findAll();
        if (count($order_segments) !== count($order_segment_ids)) {
            error(0, '航段信息有误');
        }
        $order_segments = array_column($order_segments, null, 'id');//航段

        //航班信息
        $flights = $flightModel->whereIn('flight_number', array_column($order_segments, 'flight_number'))->findAll();
        $flights = array_column($flights, null, 'flight_number');

        //票号及乘客信息
        $order_passengers = $order_passenger_model->whereIn('id', $ticket_ids)->findAll();
        if (count($order_passengers) !== count($ticket_ids)) {
            error(0, '票号id信息有误');
        }
        $order_passengers = array_column($order_passengers, null, 'id');

        $params = [
            'airline' => '',
            'flights' => [],
        ];
        $flight_number_departure_date_map = [];//航班号与出发日期对应关系  ['出发机场+到达机场'=>'出发日期']
        foreach ($flight_segments as $flight_segment) {
            $order_segment = $order_segments[$flight_segment['pnr_segment_id']];//航段
            $new_departure_date = str_replace("-", "", $flight_segment['departure_date']);//新出发日期
            //航班
            $flight = $flights[$order_segment['flight_number']];
            $flight_number_departure_date_map[$flight['departure_airport'] . $flight['arrival_airport']] = $flight_segment['departure_date'];

            //起飞时间
            $departure_datetime = explode(' ', $order_segment['departure_datetime']);
            $departure_datetime_format = $departure_datetime[0] . 'T' . $departure_datetime[1];
            $params['airline'] = $order_segment['airline'];
            $tmp = [
                'departure_airport' => $flight['departure_airport'],//出发机场代码
                'arrival_airport' => $flight['arrival_airport'],//到达机场代码
                'air_equip_type' => $flight['air_equip_type'],//机型
                'marketing_airline' => $order_segment['airline'],//市场航空公司
                'operation_airline' => $order_segment['operating_airline'],//执飞航空公司
                'flight_number' => $order_segment['flight_number'],//航班号
                'departure_time' => $departure_datetime_format,//起飞时间
                'cabin' => $order_segment['cabin'],//舱位
                'change' => true, // 是否要改签
                'type' => '2', //
                'action_code' => $order_segment['action_code'], // 行动代码
            ];
            foreach ($order_passengers as $passenger) {
                //只查询成人价格
                if ($passenger['passenger_type'] == 1) {
                    $tmp['ticket_number_before'] = $passenger['ticket_number'];// 原有电子票号
                    $tmp['is_infant'] = $passenger['passenger_type'] == 3 ? true : false;// 是否婴儿
                    $tmp['passenger_name'] = $passenger['person_name'];// 乘客姓名
                    $tmp['passenger_id'] = $passenger['rph'];// 乘客ID
                    $tmp['involuntary_identifier'] = $rebook_type == 2 ? true : false;// 是否非自愿
                    $tmp['query_depature_date'] = $new_departure_date;// 新的出发日期
                    $params['flights'][] = $tmp;
                    break;
                }
            }
        }
        $shopping = new \App\Libraries\Api\IBE\Shopping();
        $list = $shopping->domestic_reshop($params);
        if (empty($list)) {
            return [];
        }
        $airline_codes = [];
        $flight_numbers = [];
        foreach ($list as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $airline_codes[] = $v1['airline'];//航司编号2位字母,例MF
                //航班编号(airline_code + flight_number)
                $flight_numbers[] = $v1['airline'] . $v1['flight_number'];//例CZ3367
            }
        }
        $airline_codes = array_unique($airline_codes);
        $cabins = $cabinModel->whereIn('airline', $airline_codes)->findAll();//舱位表信息
        $cabinsArr = [];
        foreach ($cabins as $val) {
            $tmp_key = $val['airline'] . $val['cabin'];
            $cabinsArr[$tmp_key] = $val;
        }
        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');

        $data = [];
        foreach ($list as $key => $val) {
            $airport = explode('-', $key);
            $departure_airport = $airport[0];//出发机场代码
            $arrival_airport = $airport[1];//到达机场代码
            $flights = [];
            foreach ($val as $key1 => $val1) {
                $flight_rph = $key1;//航班RPH编号
                $airline_code = $val1['airline'];
                $flight_number = $airline_code . $val1['flight_number'];
                $format_departure_time = substr($val1['departure_time'], 0, 2) . ":" . substr($val1['departure_time'], 2, 2);//出发时间
                $format_arrival_time = substr($val1['arrival_time'], 0, 2) . ":" . substr($val1['arrival_time'], 2, 2);//到达时间

                $departure_date = $flight_number_departure_date_map[$val1['departure_airport'] . $val1['arrival_airport']] ?? '1990-01-01';

                $economy_cabin = [];//经济舱
                $business_first_cabin = [];//公务/头等舱
                if (isset($val1['cabin'])) {
                    foreach ($val1['cabin'] as $key2 => $val2) {
                        $cabin_no = $key2;//舱位号，如F
                        $cabin = $cabinsArr[$airline_code.$cabin_no] ?? [];
                        if (empty($cabin)) {
                            error(0, "未知航司舱位{$airline_code}_{$cabin_no}");
                        }
                        $cabins_tmp = [
                            'cabin_no' => $cabin_no,//舱位编号号
                            'flight_number' => $flight_number,//航班编号
                            'amount' => $val2['amount'],//票面价格
                            'differ_fare' => $val2['differ_fare'],//差价
                            'reissue_fee' => $val2['reissue_fee'],//改签费
                            'taxes' => $val2['taxes'],//税费信息
                            'discount' => $cabin['discount']. '折',//折扣
                            'stock' => 8,//TODO 库存先写死，后续会通过接口获取
                        ];
                        //分拣舱型tab
                        if ($cabin['cabin_grade_id'] == 1) {//舱位等级ID：1经济舱 2公务舱 3头等舱
                            $economy_cabin[] = $cabins_tmp;
                        } elseif (in_array($cabin['cabin_grade_id'],[2,3])) {
                            $business_first_cabin[] = $cabins_tmp;
                        }
                    }
                }
                $flights[] = [
                    'flight_number' => $flight_number,
                    'meal' => '有餐食',//TODO:需要计算出来，此处暂写死
                    'air_equip_type' => $val1['air_equip_type'],//机型
                    'departure_airport' => $val1['departure_airport'],//出发机场代码
                    'arrival_airport' => $val1['arrival_airport'],//到达机场代码
                    'departure_airport_city' => $airports[$val1['departure_airport']]['city_cn'],//出发城市
                    'arrival_airport_city' => $airports[$val1['arrival_airport']]['city_cn'],//到达城市
                    'departure_airport_cn' => $airports[$val1['departure_airport']]['airport_name_cn'],//出发机场名称
                    'arrival_airport_cn' => $airports[$val1['arrival_airport']]['airport_name_cn'],//到达机场名称
                    'departure_time' => $format_departure_time,//出发时间
                    'arrival_time' => $format_arrival_time,//到达时间
                    'departure_date' => $departure_date,//出发年月日
                    'cabins_price' => [//舱位价格分组列表
                        'economy_cabin' => $economy_cabin,
                        'business_first_cabin' => $business_first_cabin
                    ]
                ];
            }
            $data[] = [
                'departure_airport_city' => $airports[$departure_airport]['city_cn'],//出发城市
                'arrival_airport_city' => $airports[$arrival_airport]['city_cn'],//到达城市
                'flights' => $flights
            ];
        }
        success('获取成功', $data);
    }

    //按PNR查询国内改签价格
    public function domestic_reissue_price()
    {
        $validation = service('validation');
        $rules = [
            'order_id' => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'rebook_type' => ['label' => '改签原因', 'rules' => 'required|in_list[1,2]'],//改签类型：1自愿改签 2被动改签
            'ticket_ids' => ['label' => '票号ids', 'rules' => 'required|is_array'],
            'flight_segments' => ['label' => '航段信息', 'rules' => 'required|is_array'],
            'flight_segments.*.departure_date' => [
                'label' => '新出发日期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
            'flight_segments.*.flight_number' => [
                'label' => '航班编号',
                'rules' => 'required|min_length[6]|max_length[30]'
            ],
            'flight_segments.*.cabin_no' => [
                'label' => '舱位编号',
                'rules' => 'required|min_length[1]|max_length[30]'
            ],
            'flight_segments.*.differ_fare' => [
                'label' => '差价',
                'rules' => 'required|greater_than_equal_to[0]'
            ],
            'flight_segments.*.changed_fee' => [
                'label' => '改签费',
                'rules' => 'required|greater_than_equal_to[0]'
            ],
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id = intval($validData['order_id']);
        $rebook_type = intval($validData['rebook_type']);
        $ticket_ids = $validData['ticket_ids'];
        $flight_segments = $validData['flight_segments'];

        $pnr_ticket_model = model('PnrTicketModel');
        $flightModel = model('FlightModel');

        $order_model = model('TicketBookOrderModel');
        $order_detail_model = model('TicketBookOrderDetailModel');
        $order_passenger_model = model('TicketBookPaxModel');
        $ticket_book_order_ticket_price_model = model('TicketBookOrderTicketPriceModel');

        //校验参数有效性
        $order = $order_model->find($order_id);
        if (empty($order)) {
            error(0, 'order_id有误');
        }

        $flight_numbers = array_column($flight_segments, 'flight_number');
        $flights = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();
        $flights = array_column($flights, null, 'flight_number');

        $order_passengers = $order_passenger_model->whereIn('id', $ticket_ids)->where('order_id', $order_id)->findAll();
        $order_passengers = array_column($order_passengers, null, 'id');
        $order_passenger_ids = array_column($order_passengers, 'id');
        $order_detail = $order_detail_model->where('order_id', $order_id)->whereIn('order_passenger_id', $order_passenger_ids)->findAll();

        //航段信息
        $flights_params = [];
        $total_post_changed_fee = 0;//累加多航段成人改签费
        foreach ($flight_segments as $flight_segment) {
            $flight_number = $flight_segment['flight_number'];
            $flight = $flights[$flight_number];
            $departure_date = $flight_segment['departure_date'];//起飞时间
            //起飞/到达时间计算
            $arrival_date = $departure_date;//默认到达日期等于出发日期，如果到达时间小于出发时间默认加1天
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrival_date = strtotime($departure_date . ' +1 day');
                $arrival_date = date('Y-m-d', $arrival_date);
            }
            $departure_datetime = $departure_date . ' ' . $flight['departure_time'] . ':00'; //出发时间
            $arrival_datetime = $arrival_date . ' ' . $flight['arrival_time'] . ':00'; //到达时间
            $flights_params[] = [
                'departure_airport' => $flight['departure_airport'], // 出发机场
                'arrival_airport' => $flight['arrival_airport'], // 到达机场
                'air_equip_type' => $flight['air_equip_type'], // 机型
                'marketing_airline' => $flight['airline_code'], // 市场航空公司
                'operation_airline' => $flight['airline_code'], // 执飞航空公司
                'flight_number' => $flight['flight_number'], // 航班号
                'departure_time' => $departure_datetime, // 起飞时间
                'arrival_time' => $arrival_datetime, // 到达时间
                'type' => 'NORMAL', // 航段状态
                'cabin' => $flight_segment['cabin_no'], // 舱位
                'action_code' => 'HK', // 行动代码
            ];
            $total_post_changed_fee = bcadd($total_post_changed_fee, $flight_segment['changed_fee'],2);
        }
        //乘客信息
        $passenger_type_arr = [];
        foreach ($order_passengers as $passenger) {
            //获取每个类型的乘客信息
            //格式：乘客类型id => [乘客n]  ps：每个乘客类型只留一个，所以就取最后一个
            $passenger_type_arr[$passenger['passenger_type']] = [
                'ticket_number' => $passenger['ticket_number'],//票号
                'is_infant' => $passenger['passenger_type'] == 3 ? true : false,//是否婴儿
                'passenger_name' => $passenger['person_name'],//乘客姓名
                'passenger_type' => $passenger['passenger_type']
            ];
        }
        $interface_price = [];//各个乘客类型，对应接口返回的改签价格信息（可能会有多个）
        $pricing = new \App\Libraries\Api\IBE\Pricing();
        foreach ($passenger_type_arr as $value) {
            $params = [
                'airline' => $flights_params[0]['marketing_airline'],
                'ticket_number_before' => $value['ticket_number'],
                'is_infant' => $value['is_infant'],//是否婴儿
                'passenger_name' => $value['passenger_name'],
                'involuntary_identifier' =>  $rebook_type == 2 ? true : false,// 是否非自愿
                'pnr' => $order['pnr'],
                'flights' => $flights_params
            ];
            $res = $pricing->query_domestic_reissue_price_by_pnr($params);
            //计算接口返回的改签费用（多航段时候会有多条，需要累加）
            $total_changed_fee = 0;//接口返回的总改期费
            $total_fare_diffe = 0;//接口返回的总价差
            $total_tax_diff = 0;//接口返回的总税差
            foreach ($res as $index => $price) {
                $total_changed_fee = bcadd($total_changed_fee,$price['changed_fee'],2);
                $total_fare_diffe = bcadd($total_fare_diffe,$price['fare_diff'],2);
                $total_tax_diff = bcadd($total_tax_diff,$price['tax_diff'],2);
            }
            $interface_price[$value['passenger_type']] = [
                'changed_fee' => $total_changed_fee,//改期费
                'fare_diff' => $total_fare_diffe,//票价差
                'tax_diff' => $total_tax_diff,//税差
            ];
            //和用户提交上来的价格进行比较（默认只比较成人价，因为搜索改签航班的接口固定只查询成人价）
            if ($value['passenger_type'] == 1 && $total_post_changed_fee != $total_changed_fee) {
//                error(0, "提交上来的成人改签费与接口返回的不一致，提交上来的：{$total_post_changed_fee}, 接口返回的: {$total_changed_fee}");
                //TODO 因模拟数据对不上，暂时注释掉验证！
            }
        }
        $price_data = [
            'purchase' => [//采购
                'detail' => [],
                'total' => [
                    'total_customer_price' => 0.00,//销售价总计 （票面价，未含税）
                    'total_tax_cn' => 0.00,
                    'total_tax_yq' => 0.00,
                    'total_tax_xt' => 0.00,
                    'total_fare_diff' => 0.00,//票差总计
                    'total_tax_diff' => 0.00,//税差总计
                    'total_changed_fee' => 0.00,//改签费总计
                    'total_service_fee' => 0.00,//改签服务费总计
                    'total_original_supplier_agency_fee' => 0.00,//原代理费总计
                    'total_new_supplier_agency_fee' => 0.00,//新代理费总计
                    'total_changed_amount' => 0.00,//采购改签总计
                ]
            ],
            'sales' => [//销售
                'detail' => [],
                'total' => [
                    'total_customer_price' => 0.00,//销售价总计 （票面价，未含税）
                    'total_tax_cn' => 0.00,
                    'total_tax_yq' => 0.00,
                    'total_tax_xt' => 0.00,
                    'total_fare_diff' => 0.00,//票差总计
                    'total_tax_diff' => 0.00,//税差总计
                    'total_changed_fee' => 0.00,//改签费总计
                    'total_service_fee' => 0.00,//改签服务费总计
                    'total_original_supplier_agency_fee' => '-',//原代理费总计
                    'total_new_supplier_agency_fee' => '-',//新代理费总计
                    'total_changed_amount' => 0.00,//销售改签总计
                ]
            ]
        ];
        foreach ($order_detail as $key => $val) {
            $order_passenger = $order_passengers[$val['order_passenger_id']];
            $passenger_type = $order_passenger['passenger_type'];//乘客类型
            $pnr_passenger_id = $order_passenger['pnr_passenger_id'];//PNR乘客ID

            //接口返回的改签费用
            $price_info = $interface_price[$passenger_type];

            //采购改签总计=票差+税差+改签费+改签服务费-代理费差
            //代理费差=新代理费-原代理费
            $purchase_changed_amount = 0;//采购改签总计
            $purchase_changed_amount = bcadd($purchase_changed_amount,$price_info['fare_diff'],2);
            $purchase_changed_amount = bcadd($purchase_changed_amount,$price_info['tax_diff'],2);
            $purchase_changed_amount = bcadd($purchase_changed_amount,$price_info['changed_fee'],2);
            $purchase_changed_amount = bcadd($purchase_changed_amount,0,2);
            //代理费差
            $agency_fee_diff = bcsub(0,0,2);
            $purchase_changed_amount = bcsub($purchase_changed_amount,$agency_fee_diff,2);

            //采购
            $price_data['purchase']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'ticket_id' => $order_passenger['id'],
                'ticket_number' => $order_passenger['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'marketing_price' => $val['ticket_marketing_price'],//采购价(票价)
                'tax_cn' => $val['ticket_tax_cn'],//机建
                'tax_yq' => $val['ticket_tax_yq'],//燃油
                'tax_xt' => $val['ticket_tax_xt'],//其他
                'fare_diff' => $price_info['fare_diff'],//票价差
                'tax_diff' => $price_info['tax_diff'],//税差
                'changed_fee' => $price_info['changed_fee'],//改期费
                'service_fee' => '0.00',//改签服务费
                'original_supplier_agency_fee' => '0.00',//原代理费
                'new_supplier_agency_fee' => '0.00',//新代理费
                'changed_amount' => $purchase_changed_amount,//采购改签总计
            ];
            $price_data['purchase']['total']['total_customer_price'] = bcadd($price_data['purchase']['total']['total_customer_price'], $val['ticket_marketing_price'], 2);//总票价
            $price_data['purchase']['total']['total_tax_cn'] = bcadd($price_data['purchase']['total']['total_tax_cn'], $val['ticket_tax_cn'], 2);//总基建
            $price_data['purchase']['total']['total_tax_yq'] = bcadd($price_data['purchase']['total']['total_tax_yq'], $val['ticket_tax_yq'], 2);//总燃油
            $price_data['purchase']['total']['total_tax_xt'] = bcadd($price_data['purchase']['total']['total_tax_xt'], $val['ticket_tax_xt'], 2);//总其他税费
            $price_data['purchase']['total']['total_fare_diff'] = bcadd($price_data['purchase']['total']['total_fare_diff'], $price_info['fare_diff'], 2);//票差总计
            $price_data['purchase']['total']['total_tax_diff'] = bcadd($price_data['purchase']['total']['total_tax_diff'], $price_info['tax_diff'], 2);//税差总计
            $price_data['purchase']['total']['total_changed_fee'] = bcadd($price_data['purchase']['total']['total_changed_fee'], $price_info['changed_fee'], 2);//改签费总计
            $price_data['purchase']['total']['total_service_fee'] = bcadd($price_data['purchase']['total']['total_service_fee'], 0, 2);//改签服务费总计
            $price_data['purchase']['total']['total_original_supplier_agency_fee'] = bcadd($price_data['purchase']['total']['total_original_supplier_agency_fee'], 0, 2);//原代理费总计
            $price_data['purchase']['total']['total_new_supplier_agency_fee'] = bcadd($price_data['purchase']['total']['total_new_supplier_agency_fee'], 0, 2);//新代理费总计
            $price_data['purchase']['total']['total_changed_amount'] = bcadd($price_data['purchase']['total']['total_changed_amount'], $purchase_changed_amount, 2);//采购改签总计

            //销售
            //销售改签总计=票差+税差+改签费+改签服务费
            $sales_changed_amount = 0;//销售改签总计
            $sales_changed_amount = bcadd($sales_changed_amount,$price_info['fare_diff'],2);
            $sales_changed_amount = bcadd($sales_changed_amount,$price_info['tax_diff'],2);
            $sales_changed_amount = bcadd($sales_changed_amount,$price_info['changed_fee'],2);
            $sales_changed_amount = bcadd($sales_changed_amount,0,2);
            $price_data['sales']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'ticket_id' => $order_passenger['id'],
                'ticket_number' => $order_passenger['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'marketing_price' => $val['ticket_marketing_price'],//采购价(票价)
                'tax_cn' => $val['ticket_tax_cn'],//机建
                'tax_yq' => $val['ticket_tax_yq'],//燃油
                'tax_xt' => $val['ticket_tax_xt'],//其他
                'fare_diff' => $price_info['fare_diff'],//票价差
                'tax_diff' => $price_info['tax_diff'],//税差
                'changed_fee' => $price_info['changed_fee'],//改期费
                'service_fee' => '0.00',//改签服务费
                'original_supplier_agency_fee' => '-',//原代理费
                'new_supplier_agency_fee' => '-',//新代理费
                'changed_amount' => $sales_changed_amount,//采购改签总计
            ];
            $price_data['sales']['total']['total_customer_price'] = bcadd($price_data['sales']['total']['total_customer_price'], $val['ticket_marketing_price'], 2);//总票价
            $price_data['sales']['total']['total_tax_cn'] = bcadd($price_data['sales']['total']['total_tax_cn'], $val['ticket_tax_cn'], 2);//总基建
            $price_data['sales']['total']['total_tax_yq'] = bcadd($price_data['sales']['total']['total_tax_yq'], $val['ticket_tax_yq'], 2);//总燃油
            $price_data['sales']['total']['total_tax_xt'] = bcadd($price_data['sales']['total']['total_tax_xt'], $val['ticket_tax_xt'], 2);//总其他税费
            $price_data['sales']['total']['total_fare_diff'] = bcadd($price_data['sales']['total']['total_fare_diff'], $price_info['fare_diff'], 2);//票差总计
            $price_data['sales']['total']['total_tax_diff'] = bcadd($price_data['sales']['total']['total_tax_diff'], $price_info['tax_diff'], 2);//税差总计
            $price_data['sales']['total']['total_changed_fee'] = bcadd($price_data['sales']['total']['total_changed_fee'], $price_info['changed_fee'], 2);//改签费总计
            $price_data['sales']['total']['total_service_fee'] = bcadd($price_data['sales']['total']['total_service_fee'], 0, 2);//改签服务费总计
            $price_data['sales']['total']['total_original_supplier_agency_fee'] = '-';//原代理费总计
            $price_data['sales']['total']['total_new_supplier_agency_fee'] = '-';//新代理费总计
            $price_data['sales']['total']['total_changed_amount'] = bcadd($price_data['sales']['total']['total_changed_amount'], $sales_changed_amount, 2);//销售改签总计
        }

        success('获取成功', $price_data);
    }

    //按PNR查询国内改签价格
    public function domestic_reissue_price_old()
    {
        $validation = service('validation');
        $rules = [
            'order_id' => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'rebook_type' => ['label' => '改签原因', 'rules' => 'required|in_list[1,2]'],//改签类型：1自愿改签 2被动改签
            'ticket_ids' => ['label' => '票号ids', 'rules' => 'required|is_array'],
            'flight_segments' => ['label' => '航段信息', 'rules' => 'required|is_array'],
            'flight_segments.*.departure_date' => [
                'label' => '新出发日期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
            'flight_segments.*.flight_number' => [
                'label' => '航班编号',
                'rules' => 'required|min_length[6]|max_length[30]'
            ],
            'flight_segments.*.cabin_no' => [
                'label' => '舱位编号',
                'rules' => 'required|min_length[1]|max_length[30]'
            ],
            'flight_segments.*.differ_fare' => [
                'label' => '差价',
                'rules' => 'required|greater_than_equal_to[0]'
            ],
            'flight_segments.*.changed_fee' => [
                'label' => '改签费',
                'rules' => 'required|greater_than_equal_to[0]'
            ],
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id = intval($validData['order_id']);
        $rebook_type = intval($validData['rebook_type']);
        $ticket_ids = $validData['ticket_ids'];
        $flight_segments = $validData['flight_segments'];

        $pnr_ticket_model = model('PnrTicketModel');
        $flightModel = model('FlightModel');

        $order_model = model('TicketBookOrderModel');
        $order_detail_model = model('TicketBookOrderDetailModel');
        $order_passenger_model = model('TicketBookPaxModel');
        $ticket_book_order_ticket_price_model = model('TicketBookOrderTicketPriceModel');

        //校验参数有效性
        $order = $order_model->find($order_id);
        if (empty($order)) {
            error(0, 'order_id有误');
        }

        $flight_numbers = array_column($flight_segments, 'flight_number');
        $flights = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();
        $flights = array_column($flights, null, 'flight_number');

        $pnr_tickets = $pnr_ticket_model->whereIn('id', $ticket_ids)->findAll();
        $pnr_tickets = array_column($pnr_tickets, null,'pnr_passenger_id');
        $pnr_passenger_ids = array_column($pnr_tickets,'pnr_passenger_id');

        $order_passengers = $order_passenger_model->where('order_id', $order_id)->whereIn('pnr_passenger_id', $pnr_passenger_ids)->findAll();
        $order_passengers_pnr = array_column($order_passengers, null, 'pnr_passenger_id');
        $order_passengers = array_column($order_passengers, null, 'id');
        $order_passenger_ids = array_column($order_passengers, 'id');
        $order_detail = $order_detail_model->where('order_id', $order_id)->whereIn('order_passenger_id', $order_passenger_ids)->findAll();

        $ticket_book_order_ticket_prices = $ticket_book_order_ticket_price_model->whereIn('order_detail_id', array_column($order_detail, 'id'))->findAll();
        $ticket_book_order_ticket_prices = array_column($ticket_book_order_ticket_prices, null, 'order_detail_id');

        //航段信息
        $flights_params = [];
        $total_post_changed_fee = 0;//累加多航段成人改签费
        foreach ($flight_segments as $flight_segment) {
            $flight_number = $flight_segment['flight_number'];
            $flight = $flights[$flight_number];
            $departure_date = $flight_segment['departure_date'];//起飞时间
            //起飞/到达时间计算
            $arrival_date = $departure_date;//默认到达日期等于出发日期，如果到达时间小于出发时间默认加1天
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrival_date = strtotime($departure_date . ' +1 day');
                $arrival_date = date('Y-m-d', $arrival_date);
            }
            $departure_datetime = $departure_date . ' ' . $flight['departure_time'] . ':00'; //出发时间
            $arrival_datetime = $arrival_date . ' ' . $flight['arrival_time'] . ':00'; //到达时间
            $flights_params[] = [
                'departure_airport' => $flight['departure_airport'], // 出发机场
                'arrival_airport' => $flight['arrival_airport'], // 到达机场
                'air_equip_type' => $flight['air_equip_type'], // 机型
                'marketing_airline' => $flight['airline_code'], // 市场航空公司
                'operation_airline' => $flight['airline_code'], // 执飞航空公司
                'flight_number' => $flight['flight_number'], // 航班号
                'departure_time' => $departure_datetime, // 起飞时间
                'arrival_time' => $arrival_datetime, // 到达时间
                'type' => 'NORMAL', // 航段状态
                'cabin' => $flight_segment['cabin_no'], // 舱位
                'action_code' => 'HK', // 行动代码
            ];
            $total_post_changed_fee = bcadd($total_post_changed_fee, $flight_segment['changed_fee'],2);
        }
        //乘客信息
        $passenger_type_arr = [];
        foreach ($pnr_tickets as $pnr_ticket) {
            $pnr_passenger_id = $pnr_ticket['pnr_passenger_id'];
            $order_passenger = $order_passengers_pnr[$pnr_passenger_id];

            //获取每个类型的乘客信息
            //格式：乘客类型id => [乘客n]  ps：每个乘客类型只留一个，所以就取最后一个
            $passenger_type_arr[$order_passenger['passenger_type']] = [
                'ticket_number' => $order_passenger['ticket_number'],//票号
                'is_infant' => $order_passenger['passenger_type'] == 3 ? true : false,//是否婴儿
                'passenger_name' => $order_passenger['person_name'],//乘客姓名
                'passenger_type' => $order_passenger['passenger_type']
            ];
        }
        $interface_price = [];//各个乘客类型，对应接口返回的改签价格信息（可能会有多个）
        $pricing = new \App\Libraries\Api\IBE\Pricing();
        foreach ($passenger_type_arr as $value) {
            $params = [
                'airline' => $flights_params[0]['marketing_airline'],
                'ticket_number_before' => $value['ticket_number'],
                'is_infant' => $value['is_infant'],//是否婴儿
                'passenger_name' => $value['passenger_name'],
                'involuntary_identifier' =>  $rebook_type == 2 ? true : false,// 是否非自愿
                'pnr' => $order['pnr'],
                'flights' => $flights_params
            ];
            $res = $pricing->query_domestic_reissue_price_by_pnr($params);
            //计算接口返回的改签费用（多航段时候会有多条，需要累加）
            $total_changed_fee = 0;//接口返回的总改期费
            $total_fare_diffe = 0;//接口返回的总价差
            $total_tax_diff = 0;//接口返回的总税差
            foreach ($res as $index => $price) {
                $total_changed_fee = bcadd($total_changed_fee,$price['changed_fee'],2);
                $total_fare_diffe = bcadd($total_fare_diffe,$price['fare_diff'],2);
                $total_tax_diff = bcadd($total_tax_diff,$price['tax_diff'],2);
            }
            $interface_price[$value['passenger_type']] = [
                'changed_fee' => $total_changed_fee,//改期费
                'fare_diff' => $total_fare_diffe,//票价差
                'tax_diff' => $total_tax_diff,//税差
            ];
            //和用户提交上来的价格进行比较（默认只比较成人价，因为搜索改签航班的接口固定只查询成人价）
            if ($value['passenger_type'] == 1 && $total_post_changed_fee != $total_changed_fee) {
//                error(0, "提交上来的成人改签费与接口返回的不一致，提交上来的：{$total_post_changed_fee}, 接口返回的: {$total_changed_fee}");
                //TODO 因模拟数据对不上，暂时注释掉验证！
            }
        }
        $price_data = [
            'purchase' => [//采购
                'detail' => [],
                'total' => [
                    'total_customer_price' => 0.00,//销售价总计 （票面价，未含税）
                    'total_tax_cn' => 0.00,
                    'total_tax_yq' => 0.00,
                    'total_tax_xt' => 0.00,
                    'total_fare_diff' => 0.00,//票差总计
                    'total_tax_diff' => 0.00,//税差总计
                    'total_changed_fee' => 0.00,//改签费总计
                    'total_service_fee' => 0.00,//改签服务费总计
                    'total_original_supplier_agency_fee' => 0.00,//原代理费总计
                    'total_new_supplier_agency_fee' => 0.00,//新代理费总计
                    'total_changed_amount' => 0.00,//采购改签总计
                ]
            ],
            'sales' => [//销售
                'detail' => [],
                'total' => [
                    'total_customer_price' => 0.00,//销售价总计 （票面价，未含税）
                    'total_tax_cn' => 0.00,
                    'total_tax_yq' => 0.00,
                    'total_tax_xt' => 0.00,
                    'total_fare_diff' => 0.00,//票差总计
                    'total_tax_diff' => 0.00,//税差总计
                    'total_changed_fee' => 0.00,//改签费总计
                    'total_service_fee' => 0.00,//改签服务费总计
                    'total_original_supplier_agency_fee' => '-',//原代理费总计
                    'total_new_supplier_agency_fee' => '-',//新代理费总计
                    'total_changed_amount' => 0.00,//销售改签总计
                ]
            ]
        ];
        foreach ($order_detail as $key => $val) {
            $order_passenger = $order_passengers[$val['order_passenger_id']];
            $passenger_type = $order_passenger['passenger_type'];//乘客类型
            $pnr_passenger_id = $order_passenger['pnr_passenger_id'];//PNR乘客ID
            $pnr_ticket = $pnr_tickets[$pnr_passenger_id];

            $ticket_book_order_ticket_price = $ticket_book_order_ticket_prices[$val['id']];

            //接口返回的改签费用
            $price_info = $interface_price[$passenger_type];

            //采购改签总计=票差+税差+改签费+改签服务费-代理费差
            //代理费差=新代理费-原代理费
            $purchase_changed_amount = 0;//采购改签总计
            $purchase_changed_amount = bcadd($purchase_changed_amount,$price_info['fare_diff'],2);
            $purchase_changed_amount = bcadd($purchase_changed_amount,$price_info['tax_diff'],2);
            $purchase_changed_amount = bcadd($purchase_changed_amount,$price_info['changed_fee'],2);
            $purchase_changed_amount = bcadd($purchase_changed_amount,0,2);
            //代理费差
            $agency_fee_diff = bcsub(0,0,2);
            $purchase_changed_amount = bcsub($purchase_changed_amount,$agency_fee_diff,2);

            //采购
            $price_data['purchase']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'ticket_id' => $pnr_ticket['id'],
                'ticket_number' => $pnr_ticket['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'marketing_price' => $ticket_book_order_ticket_price['marketing_price'],//采购价(票价)
                'tax_cn' => $ticket_book_order_ticket_price['tax_cn'],//机建
                'tax_yq' => $ticket_book_order_ticket_price['tax_yq'],//燃油
                'tax_xt' => $ticket_book_order_ticket_price['tax_xt'],//其他
                'fare_diff' => $price_info['fare_diff'],//票价差
                'tax_diff' => $price_info['tax_diff'],//税差
                'changed_fee' => $price_info['changed_fee'],//改期费
                'service_fee' => '0.00',//改签服务费
                'original_supplier_agency_fee' => '0.00',//原代理费
                'new_supplier_agency_fee' => '0.00',//新代理费
                'changed_amount' => $purchase_changed_amount,//采购改签总计
            ];
            $price_data['purchase']['total']['total_customer_price'] = bcadd($price_data['purchase']['total']['total_customer_price'], $ticket_book_order_ticket_price['supplier_price'], 2);//总票价
            $price_data['purchase']['total']['total_tax_cn'] = bcadd($price_data['purchase']['total']['total_tax_cn'], $ticket_book_order_ticket_price['tax_cn'], 2);//总基建
            $price_data['purchase']['total']['total_tax_yq'] = bcadd($price_data['purchase']['total']['total_tax_yq'], $ticket_book_order_ticket_price['tax_yq'], 2);//总燃油
            $price_data['purchase']['total']['total_tax_xt'] = bcadd($price_data['purchase']['total']['total_tax_xt'], $ticket_book_order_ticket_price['tax_xt'], 2);//总其他税费
            $price_data['purchase']['total']['total_fare_diff'] = bcadd($price_data['purchase']['total']['total_fare_diff'], $price_info['fare_diff'], 2);//票差总计
            $price_data['purchase']['total']['total_tax_diff'] = bcadd($price_data['purchase']['total']['total_tax_diff'], $price_info['tax_diff'], 2);//税差总计
            $price_data['purchase']['total']['total_changed_fee'] = bcadd($price_data['purchase']['total']['total_changed_fee'], $price_info['changed_fee'], 2);//改签费总计
            $price_data['purchase']['total']['total_service_fee'] = bcadd($price_data['purchase']['total']['total_service_fee'], 0, 2);//改签服务费总计
            $price_data['purchase']['total']['total_original_supplier_agency_fee'] = bcadd($price_data['purchase']['total']['total_original_supplier_agency_fee'], 0, 2);//原代理费总计
            $price_data['purchase']['total']['total_new_supplier_agency_fee'] = bcadd($price_data['purchase']['total']['total_new_supplier_agency_fee'], 0, 2);//新代理费总计
            $price_data['purchase']['total']['total_changed_amount'] = bcadd($price_data['purchase']['total']['total_changed_amount'], $purchase_changed_amount, 2);//采购改签总计

            //销售
            //销售改签总计=票差+税差+改签费+改签服务费
            $sales_changed_amount = 0;//销售改签总计
            $sales_changed_amount = bcadd($sales_changed_amount,$price_info['fare_diff'],2);
            $sales_changed_amount = bcadd($sales_changed_amount,$price_info['tax_diff'],2);
            $sales_changed_amount = bcadd($sales_changed_amount,$price_info['changed_fee'],2);
            $sales_changed_amount = bcadd($sales_changed_amount,0,2);
            $price_data['sales']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'ticket_id' => $pnr_ticket['id'],
                'ticket_number' => $pnr_ticket['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'marketing_price' => $ticket_book_order_ticket_price['marketing_price'],//采购价(票价)
                'tax_cn' => $ticket_book_order_ticket_price['tax_cn'],//机建
                'tax_yq' => $ticket_book_order_ticket_price['tax_yq'],//燃油
                'tax_xt' => $ticket_book_order_ticket_price['tax_xt'],//其他
                'fare_diff' => $price_info['fare_diff'],//票价差
                'tax_diff' => $price_info['tax_diff'],//税差
                'changed_fee' => $price_info['changed_fee'],//改期费
                'service_fee' => '0.00',//改签服务费
                'original_supplier_agency_fee' => '-',//原代理费
                'new_supplier_agency_fee' => '-',//新代理费
                'changed_amount' => $sales_changed_amount,//采购改签总计
            ];
            $price_data['sales']['total']['total_customer_price'] = bcadd($price_data['sales']['total']['total_customer_price'], $ticket_book_order_ticket_price['supplier_price'], 2);//总票价
            $price_data['sales']['total']['total_tax_cn'] = bcadd($price_data['sales']['total']['total_tax_cn'], $ticket_book_order_ticket_price['tax_cn'], 2);//总基建
            $price_data['sales']['total']['total_tax_yq'] = bcadd($price_data['sales']['total']['total_tax_yq'], $ticket_book_order_ticket_price['tax_yq'], 2);//总燃油
            $price_data['sales']['total']['total_tax_xt'] = bcadd($price_data['sales']['total']['total_tax_xt'], $ticket_book_order_ticket_price['tax_xt'], 2);//总其他税费
            $price_data['sales']['total']['total_fare_diff'] = bcadd($price_data['sales']['total']['total_fare_diff'], $price_info['fare_diff'], 2);//票差总计
            $price_data['sales']['total']['total_tax_diff'] = bcadd($price_data['sales']['total']['total_tax_diff'], $price_info['tax_diff'], 2);//税差总计
            $price_data['sales']['total']['total_changed_fee'] = bcadd($price_data['sales']['total']['total_changed_fee'], $price_info['changed_fee'], 2);//改签费总计
            $price_data['sales']['total']['total_service_fee'] = bcadd($price_data['sales']['total']['total_service_fee'], 0, 2);//改签服务费总计
            $price_data['sales']['total']['total_original_supplier_agency_fee'] = '-';//原代理费总计
            $price_data['sales']['total']['total_new_supplier_agency_fee'] = '-';//新代理费总计
            $price_data['sales']['total']['total_changed_amount'] = bcadd($price_data['sales']['total']['total_changed_amount'], $sales_changed_amount, 2);//销售改签总计
        }

        success('获取成功', $price_data);
    }
}