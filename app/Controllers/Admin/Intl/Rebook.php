<?php

namespace App\Controllers\Admin\Intl;

use App\Controllers\AdminController;
use App\Services\Intl\Rebook as RebookService;

class Rebook extends AdminController
{
    public RebookService $service;

    public function __construct()
    {
        $this->service = load_service('Intl\Rebook');
    }

    /**
     * 改签申请
     *
     * @return void
     */
    public function apply(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $orderId   = intval($validData['order_id']);
        $data      = $this->service->rebookApply($orderId);

        success('成功', $data);
    }

    /**
     * 改签航班搜索
     *
     * @return void
     */
    public function reshop(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id'        => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'rebook_type'     => ['label' => '改签原因', 'rules' => 'required|in_list[1,2]'],//改签类型：1自愿改签 2被动改签
            'ticket_ids'      => ['label' => '票号ids', 'rules' => 'required|is_array'],
            'flight_segments' => ['label' => '航段信息', 'rules' => 'required|is_array'],

            'flight_segments.*.departure_date' => [
                'label' => '新出发日期',
                'rules' => 'required|valid_date[Y-m-d]',
            ],
            'flight_segments.*.segment_id'     => [
                'label' => '航段id',
                'rules' => 'required|greater_than[0]',
            ],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        try {
            $result = $this->service->getRebookFlight($validData);

            success('查询成功', $result);
        } catch (\Exception $e) {
            // 记录详细错误日志
            log_message('error', '[改签航班搜索] 处理失败' . json_encode([
                    'method'         => __METHOD__,
                    'line'           => __LINE__,
                    'error_message'  => $e->getMessage(),
                    'error_code'     => $e->getCode(),
                    'error_file'     => $e->getFile(),
                    'error_line'     => $e->getLine(),
                    'stack_trace'    => $e->getTraceAsString(),
                    'request_data'   => $validData,
                    'ip_address'     => $this->request->getIPAddress(),
                    'user_agent'     => $this->request->getUserAgent(),
                    'timestamp'      => date('Y-m-d H:i:s'),
                    'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
                ]));

            error(0, '改签搜索失败: ' . $e->getMessage());
        }
    }

    /**
     * 查询改签费用
     *
     * @return void
     */
    public function fee(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id'                         => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'rebook_type'                      => ['label' => '改签原因', 'rules' => 'required|in_list[1,2]'],//改签类型：1自愿改签 2被动改签
            'ticket_ids'                       => ['label' => '票号ids', 'rules' => 'required|is_array'],
            'flight_segments'                  => ['label' => '航段信息', 'rules' => 'required|is_array'],
            'flight_segments.*.departure_date' => [
                'label' => '新出发日期',
                'rules' => 'required|valid_date[Y-m-d]',
            ],
            'flight_segments.*.flight_number'  => [
                'label' => '航班编号',
                'rules' => 'required|min_length[6]|max_length[30]',
            ],
            'flight_segments.*.cabin_no'       => [
                'label' => '舱位编号',
                'rules' => 'required|min_length[1]|max_length[30]',
            ],
            'flight_segments.*.differ_fare'    => [
                'label' => '差价',
                'rules' => 'required|greater_than_equal_to[0]',
            ],
            'flight_segments.*.changed_fee'    => [
                'label' => '改签费',
                'rules' => 'required|greater_than_equal_to[0]',
            ],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        try {
            $result = $this->service->calculateReissuePrice($validData);
            success('查询成功', $result);
        } catch (\Exception $e) {
            error(0, '改签费用查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认改签
     *
     * @return void
     */
    public function confirm(): void
    {
        $validation = service('validation');
        $rules      = [
            'tickets'                              => ['label' => '票号信息', 'rules' => 'required|is_array'],
            'tickets.*.ticket_id'                  => [
                'label' => '票号id',
                'rules' => 'required|greater_than[0]',
            ],
            'tickets.*.differ_fare'                => [
                'label' => '差价',
                'rules' => 'required|greater_than_equal_to[0]',
            ],
            'tickets.*.changed_fee'                => [
                'label' => '改签费',
                'rules' => 'required|greater_than_equal_to[0]',
            ],
            'tickets.*.marketing_price'            => [
                'label' => '票面价',
                'rules' => 'required|greater_than[0]',
            ],
            'old_flight_segments'                  => ['label' => '旧航段信息', 'rules' => 'required|is_array'],
            'old_flight_segments.*.flight_number'  => [
                'label' => '航班编号',
                'rules' => 'required|min_length[6]|max_length[30]',
            ],
            'old_flight_segments.*.pnr_segment_id' => [
                'label' => 'PNR-航段ID',
                'rules' => 'required|greater_than[0]',
            ],
            'new_flight_segments'                  => ['label' => '新航段信息', 'rules' => 'required|is_array'],
            'new_flight_segments.*.departure_date' => [
                'label' => '新出发日期',
                'rules' => 'required|valid_date[Y-m-d]',
            ],
            'new_flight_segments.*.flight_number'  => [
                'label' => '航班编号',
                'rules' => 'required|min_length[6]|max_length[30]',
            ],
            'new_flight_segments.*.cabin_no'       => [
                'label' => '舱位编号',
                'rules' => 'required|min_length[1]|max_length[30]',
            ],

            'order_id'          => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'order_type'        => ['label' => '订单类型', 'rules' => 'required|in_list[1,2]'],//订单类型：1出票订单 2改签订单
            'rebook_type'       => ['label' => '改签原因', 'rules' => 'required|in_list[1,2]'],//改签类型：1自愿改签 2非自愿改签
            'rebook_purpose'    => ['label' => '改签目的', 'rules' => 'required|in_list[1,2]'],//改签目的：1改期 2升舱
            'contact_name'      => ['label' => '联系人', 'rules' => 'required|max_length[6]|min_length[2]'],
            'contact_telephone' => ['label' => '联系电话', 'rules' => 'required|max_length[20]|min_length[11]'],
            'contact_email'     => ['label' => '联系邮箱', 'rules' => 'required|max_length[30]|min_length[6]'],
            'is_send_sms'       => ['label' => '通知类型', 'rules' => 'required|in_list[0,1]'],//发送短信：0否 1是
            'remark'            => ['label' => '备注', 'rules' => 'required|max_length[50]|min_length[2]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        try {
            $rebookOrderId = $this->service->confirmRebook($validData);
            success('改签成功', ['rebook_order_id' => $rebookOrderId]);
        } catch (\Exception $e) {
            error(0, '改签确认失败: ' . $e->getMessage());
        }
    }

    /**
     * 改签订单列表
     *
     * @return void
     */
    public function list(): void
    {
        $validation = service('validation');
        $rules      = [
            'page'            => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page'        => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'pnr'             => ['label' => '新/原PNR编码', 'rules' => 'permit_empty|max_length[6]|min_length[3]'],
            'order_no'        => ['label' => '改签单号', 'rules' => 'permit_empty|max_length[20]|min_length[3]'],
            'person_name'     => ['label' => '乘客姓名', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_status'    => ['label' => '订单状态', 'rules' => 'permit_empty|greater_than_equal_to[0]'],//小于0则失败
            'origin_order_no' => ['label' => '原订单编号', 'rules' => 'permit_empty|max_length[20]|min_length[13]'],
            'airline'         => ['label' => '航空公司', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'operator_name'   => ['label' => '订票员', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_date'      => ['label' => '改签日期', 'rules' => 'permit_empty|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $data      = $this->service->getOrderList($validData);

        success('成功', $data);
    }

    /**
     * 获取改签订单详情
     *
     * @return void
     */
    public function detail(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $orderId = intval($validData['order_id']);

        try {
            $data = $this->service->getRebookDetail($orderId);
            success('获取成功', $data);
        } catch (\Exception $e) {
            error(0, '获取改签详情失败: ' . $e->getMessage());
        }
    }
}