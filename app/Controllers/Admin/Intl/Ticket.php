<?php

namespace App\Controllers\Admin\Intl;

use App\Controllers\AdminController;
use App\Models\OrderModel;
use App\Models\PnrModel;
use App\Models\PnrPassengerModel;
use App\Models\PnrPriceModel;
use App\Models\TicketRefundOrderModel;

class Ticket extends AdminController
{
    /**
     * 出票
     *
     * @return void
     */
    public function issueTicket(): void
    {
        $validation = service('validation');
        $rules      = [
            'order_id'     => ['label' => '订单ID', 'rules' => 'required|greater_than[0]'],
            'payment_type' => ['label' => '支付方式', 'rules' => 'required|in_list[1,2]'],//支付方式: 1:BSP支付出票，2:BOP支付出票
        ];

        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData   = $validation->getValidated();
        $orderId     = intval($validData['order_id']);
        $paymentType = intval($validData['payment_type']);

        $ticketSegmentModel       = model('TicketBookSegModel');
        $ticketBookOrderModel     = model('TicketBookOrderModel');
        $tboPassengerModel        = model('TicketBookPaxModel');
        $tboPassengerSegmentModel = model('TicketBookPaxSegModel');
        $pnrModel                 = model('PnrModel');

        //订单信息
        $order = $ticketBookOrderModel->find($orderId);
        if (empty($order)) {
            error(0, 'order_id错误');
        }
        if (!in_array($order['status'], [OrderModel::ORDER_STATUS_ORDERED, OrderModel::ORDER_STATUS_PART_TICKET])) {
            error(0, '只有【已订座】【部分出票】状态才可出票');
        }

        /**
         * @var \App\Services\Intl\Ticket $ticketService
         */
        $ticketService = load_service('Intl\Ticket');
        $pnrInfos      = $ticketService->createPnrPassengerPriceData($orderId);
        $airline       = $ticketSegmentModel->where(['order_id' => $orderId])->findColumn('airline');
        $airline       = $airline[0];
        $ticket        = new \App\Libraries\Api\IBE\Intl\Ticket();
        $ei            = '不得退改签';
        $params        = [
            'pnr_infos'    => $pnrInfos,
            'airline'      => $airline,
            'pnr'          => $order['pnr'],
            'ei'           => $ei,
            'payment_type' => 'CASH', // 支付方式
        ];
        if ($paymentType == 1) {
            //国内BSP出票
            $ticketRes = $ticket->issuingTicket($params);
        } else {
            //BOP出票
            $ticketRes = $ticket->issuingBOPTicket($params);
        }
        if (empty($ticketRes)) {
            error(0, '接口返回出票信息格式有误');
        }

        $orderPassengerMap = [];
        //PNR乘客
        $orderPassengers = $tboPassengerModel->where(['order_id' => $orderId])->findAll();
        foreach ($orderPassengers as $pp) {
            $tmpKey = PnrPassengerModel::INTL_PASSENGER_MAP[$pp['passenger_type']] . $pp['person_name'];

            $orderPassengerMap[$tmpKey] = $pp;
        }

        try {
            $db = \Config\Database::connect();
            $db->transStart();

            //添加pnr_tickets
            foreach ($ticketRes['tickets'] as $val) {
                $tmpKey           = $val['passenger_type'] . $val['passenger_surname'];
                $ticketNumber     = $val['ticket_number'];
                $orderPassengerId = $orderPassengerMap[$tmpKey]['id'] ?? 0;
                $tboPassengerModel->update($orderPassengerId, [
                    'ticket_number' => $ticketNumber,
                    'total_amount'  => $val['total_amount'],
                    'ei'            => $ei,
                    'currency_code' => $val['currency_code'],
                    'payment_type'  => $val['payment_type'],
                    'status'        => 1,
                ]);

                //添加order_ticket_passenger_segments
                foreach ($val['flight_segments'] as $val2) {
                    //起飞时间
                    $tboPassengerSegmentModel->insert([
                        'passenger_id'       => $orderPassengerId,
                        'ticket_number'      => $ticketNumber,
                        'flight_number'      => $val2['flight_number'],
                        'departure_datetime' => $val2['departure_datetime'],
                        'departure_airport'  => $val2['departure_airport'],
                        'arrival_airport'    => $val2['arrival_airport'],
                        'marketing_airline'  => $val2['airline'],
                        'ticket_status'      => $val2['ticket_status'],
                        'status'             => 1,
                    ]);
                }
            }
            //修改订单状态
            $ticketBookOrderModel->where('id', $orderId)->set(['bsp_payment_type' => $paymentType, 'status' => 2])->update();
            //修改pnr表
            $pnrModel->where('id', $order['pnr_id'])->set('status', 1)->update();

            $db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $db->transRollback();
            error(0, $e->getMessage());
        }
        success('出票成功');
    }
}