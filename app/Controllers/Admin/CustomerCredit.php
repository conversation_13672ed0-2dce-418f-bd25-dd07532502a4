<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
class CustomerCredit extends AdminController
{
    /**
     * @desc 授信交易明细列表
     * @throws \Exception
     *
     * <AUTHOR> 2025-06-13
     */
    public function list()
    {
        $validation = service('validation');
        $rules = [
            'page' => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page' => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'created_date' => ['label' => '交易日期', 'rules' => 'permit_empty|is_array'],
            'subject_id' => ['label' => '交易类型ID', 'rules' => 'permit_empty|greater_than[0]'],
            'transaction_type' => ['label' => '收支类型', 'rules' => 'permit_empty|in_list[1,2]'],//收支类型：1使用额度 2恢复额度
            'customer_id' => ['label' => 'customer_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Credit $creditService */
        $creditService = load_service('Customer\Credit');
        $data = $creditService->list($validData);

        success('获取成功', $data);
    }

    /**
     * @desc 调整授信额度
     *
     * <AUTHOR> 2025-06-23
     */
    public function adjustAmount()
    {
        $validation = service('validation');
        $rules = [
            'customer_id' => ['label' => '会员id', 'rules' => 'required|greater_than[0]'],
            'amount' => ['label' => '调整额度', 'rules' => 'required|greater_than[0]'],
            'remark' => ['label' => '备注', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\Customer\Credit $creditService */
        $creditService  = load_service('Customer\Credit');
        try {
            $data = $creditService->adjustAmount($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('调整成功', $data);
    }
}