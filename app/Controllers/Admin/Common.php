<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
use App\Models\CustomerModel;

class Common extends AdminController
{
    //公司列表
    public function listCompany(): string 
    {
        $companyModel = model('CompanyModel');
        $companys = $companyModel->select('id, name')->findAll();

        success('获取成功', $companys);
    }

    //部门列表
    public function listDepartment(): string 
    {
        $departments = model('DepartmentModel')->select('id, department_name')->findAll();

        success('获取成功', $departments);
    }

    //组织架构列表
    public function listOrg(): string 
    {
         $companyModel = model('CompanyModel');
         $departmentModel = model('DepartmentModel');
         $org = [];
         $companys = $companyModel->select('id,name')->findAll();
         if (!empty($companys)) {
             $companyIds = array_column($companys, 'id');
             $companys = array_column($companys, null, 'id');
             $departments = $departmentModel->whereIn('company_id', $companyIds)->findAll();
             if (!empty($departments)) {
                  $tnp = [];
                 foreach ($departments as $department) {
                     $key = $department['company_id'] . '-' . $department['id'];
                     $tmp[$department['company_id']][] = ['key' => $key, 'title' => $department['department_name'], 'department_id' => $department['id']];
                 }
                 foreach ($tmp as $companyId => $departmentlist) {
                     $org[] = [
                         'key' => $companyId,
                         'title' => $companys[$companyId]['name'],
                         'children' => $departmentlist
                     ];
                 }
             }
         }
         success('获取成功', $org);
    }

    //角色列表
    public function listRole(): string 
    {
        $roleModel = model('RoleModel');
        $roles = $roleModel->select('id, name')->findAll();

        success('获取成功', $roles);
    }

    //出发/到达城市列表
    public function listAirportCity()
    {
        $validation = service('validation');
        $rules = [
            'type' => ['label' => '机场类型', 'rules' => 'required|in_list[1,2]']
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $type = intval($validData['type']);
        $airportModel = model('AirportModel');
        $airports = $airportModel->select('id,airport_code,city_cn')->where('type', $type)->findAll();
        $data = [];
        foreach ($airports as $airport) {
            $data[] = [
                'code' => $airport['airport_code'],
                'name' => $airport['city_cn'] . '-' . $airport['airport_code']
            ];
        }
        success('获取成功', $data);
    }

    //出发/到达城市列表（国际）
    public function listAirportIntl()
    {
        $validation = service('validation');
        $rules = [
            'name' => ['label' => '名称', 'rules' => 'required']
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $name = trim($validData['name']);
        $airportModel = model('AirportModel');
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->like('city_cn', $name)->orLike('airport_name_cn', $name)->orLike('airport_code', $name)->findAll();
        $data = [];
        foreach ($airports as $airport) {
            $data[] = [
                'code' => $airport['airport_code'],
                'name' => $airport['city_cn'] . '-' . $airport['airport_name_cn'] . '-' . $airport['airport_code']
            ];
        }
        success('获取成功', $data);
    }

    //航空公司列表
    public function listAirline()
    {
        $validation = service('validation');
        $rules = [
            'area_type' => ['label' => '地区类型', 'rules' => 'required|in_list[1,2]']
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $areaType = intval($validData['area_type']);
        $airlineModel = model('AirlineModel');
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->where('area_type', $areaType)->findAll();
        $data = [];
        foreach ($airlines as $airline) {
            $data[] = [
                'code' => $airline['airline_code'],
                'name' => $airline['airline_cn'] . '-' . $airline['airline_code']
            ];
        }
        success('获取成功', $data);
    }

    //用户列表
    public function listUser(): string
    {
        $userModel = model('UserModel');
        $users = $userModel->select('id, name')->findAll();

        success('获取成功', $users);
    }

    /**
     * @desc 国家列表
     * @return string
     *
     * <AUTHOR> 2025-06-24
     */
    public function listCountryCode(): string
    {
        $countryCodeModel = model('CountryCodeModel');
        $countryCodes = $countryCodeModel->select('code,name,name_en')->findAll();

        success('获取成功', $countryCodes);
    }

    //预存金交易类型列表
    public function listCustomerCashSubject(): string
    {
        $customerCashSubjectModel = model('CustomerCashSubjectModel');
        $customerCashSubjects = $customerCashSubjectModel->select('subject_id,subject_name')->findAll();

        success('获取成功', $customerCashSubjects);
    }

    /**
     * @desc 支付渠道列表
     * @return string
     *
     * <AUTHOR> 2025-06-24
     */
    public function listPaymentChannel(): string
    {
        $paymentChannelModel = model('PaymentChannelModel');
        $paymentChannels = $paymentChannelModel->select('id,payment_type,channel_name')->findAll();

        success('获取成功', $paymentChannels);
    }

    /**
     * @desc 图片上传
     * @return string
     *
     * <AUTHOR> 2025-06-12
     */
    public function uploadImages(): string
    {
        $validation = service('validation');
        $rules = [
            'images' => [
                'label' => 'Image Files',
                'rules' => 'uploaded[images]'
                    . '|is_image[images]'
                    . '|mime_in[images,image/jpg,image/jpeg,image/png,image/gif]'
                    . '|max_size[images,2048]', // 2MB
            ],
            'upload_type' => ['label' => '文件上传类型', 'rules' => 'required|in_list[1,2,3,4,5]'],//文件上传类型：1充值 2会员证件信息 3头像 4企业logo 5营业执照
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $files = $this->request->getFiles();
        $uploadType = intval($validData['upload_type']);
        try {
            /** @var \App\Services\Common\ImageUpload $imgUploadService */
            $imgUploadService  = load_service('Common\ImageUpload');
            $result = $imgUploadService->uploads($files, $uploadType);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('上传成功', $result);
    }

    /**
     * @desc 一级会员列表
     * @return string
     *
     * <AUTHOR> 2025-06-24
     */
    public function listParentCustomer(): string
    {
        $customerModel = model('CustomerModel');
        $customers = $customerModel->select('id, customer_name')->where(['parent_id' => 0, 'customer_type' => CustomerModel::CUSTOMER_TYPE_OFFICIAL])->findAll();

        success('获取成功', $customers);
    }

    /**
     * @desc 银行列表
     *
     * <AUTHOR> 2025-06-24
     */
    public function listBank()
    {
        $bankTextMap = CustomerModel::bankTextMap();
        $data = [];
        foreach ($bankTextMap as $code => $bankText) {
            $data[] = [
                'code' => $code,
                'name' => $bankText,
            ];
        }
        success('获取成功', $data);
    }

}