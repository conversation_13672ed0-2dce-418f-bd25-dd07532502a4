<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;

class CustomerCash extends AdminController
{
    public function list()
    {
        $validation = service('validation');
        $rules = [
            'page' => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page' => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'customer_id' => ['label' => 'customer_id', 'rules' => 'required|greater_than[0]'],
            'created_date' => ['label' => '交易日期', 'rules' => 'permit_empty|is_array'],
            'subject_id' => ['label' => '交易类型ID', 'rules' => 'permit_empty|greater_than[0]'],
            'transaction_type' => ['label' => '收支类型', 'rules' => 'permit_empty|in_list[1,2]'],//收支类型：1收入 2支出
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Cash $cashService */
        $cashService = load_service('Customer\Cash');
        $data = $cashService->list($validData);

        success('获取成功', $data);
    }

    /**
     * @desc 预存金申请列表
     *
     * <AUTHOR> 2025-06-23
     */
    public function listApply()
    {

    }

    //线下充值申请
    public function rechargeApply(): string
    {
        $validation = service('validation');
        $rules = [
            'customer_id' => ['label' => 'customer_id', 'rules' => 'required|greater_than[0]'],
            'amount' => ['label' => '充值金额', 'rules' => 'required|greater_than[0]'],
            'remark' => ['label' => '备注', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'payment_type' => ['label' => '支付方式', 'rules' => 'required|in_list[1,2,3]'],//支付方式：1线下转账 2线下二维码收款 3线下POS机收款
            'channel_id' => ['label' => '支付渠道ID', 'rules' => 'required|greater_than[0]'],
            'payment_date' => ['label' => '汇款日期', 'rules' => 'required|max_length[10]|min_length[10]'],
            'transfer_account_number' => ['label' => '汇款银行账号', 'rules' => 'permit_empty|max_length[50]|min_length[2]'], //汇款账号
            'transfer_account_name' => ['label' => '汇款银行账号', 'rules' => 'permit_empty|max_length[50]|min_length[2]'], //汇款户名
            'payment_images' => ['label' => '凭证图片', 'rules' => 'required|is_array'],
            'qrcode_payment_channel' => ['label' => '支付方式', 'rules' => 'permit_empty|in_list[1,2,3,4]'],//付款渠道：1支付宝 2微信 3云闪付 4其他
            'qrcode_payment_nickname' => ['label' => '付款账号/付款人名称', 'rules' => 'permit_empty|max_length[50]|min_length[2]'], //付款人名称
            'pos_bank_name' =>  ['label' => '刷卡银行', 'rules' => 'permit_empty|max_length[50]|min_length[2]'], //刷卡银行
            'pos_account_number' =>  ['label' => '刷卡卡号', 'rules' => 'permit_empty|max_length[50]|min_length[2]'], //刷卡卡号
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\Customer\Cash $cashService */
        $cashService  = load_service('Customer\Cash');
        try {
            $data = $cashService->rechargeApply($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('添加成功', $data);
    }

    //线下充值审核
    public function rechargeAudit()
    {
        $validation = service('validation');
        $rules = [
            'payment_addfund_id' => ['label' => '充值申请记录id', 'rules' => 'required|greater_than[0]'],
            'status' => ['label' => '审核状态', 'rules' => 'required|in_list[1,2]'],//1审核通过 2审核不通过
            'remark' => ['label' => '备注', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\Customer\Cash $cashService */
        $cashService  = load_service('Customer\Cash');
        try {
            $res = $cashService->rechargeAudit($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('审核成功', $res);
    }


}