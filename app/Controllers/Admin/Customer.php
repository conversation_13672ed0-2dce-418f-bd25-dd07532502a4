<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
use App\Models\CustomerModel;

class Customer extends AdminController
{
    /**
     * @desc 会员列表
     *
     * <AUTHOR> 2025-06-09
     */
    public function list()
    {
        $validation = service('validation');
        $rules = [
            'page' => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page' => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'customer_grade' => ['label' => '会员等级', 'rules' => 'permit_empty|in_list[1,2,3,4]'],//会员等级：1初始会员 2中级会员 3高级会员 4VIP会员
            'card_no' => ['label' => '会员卡号', 'rules' => 'permit_empty|max_length[20]|min_length[4]'],
            'customer_name' => ['label' => '会员名称', 'rules' => 'permit_empty|max_length[20]|min_length[2]'],
            'contact_mobile' => ['label' => '会员手机号', 'rules' => 'permit_empty|max_length[20]|min_length[4]'],
            'bd_user_id' => ['label' => '业务拓展员ID', 'rules' => 'permit_empty|greater_than[0]'],//业务拓展员ID
            'source' => ['label' => '会员来源', 'rules' => 'permit_empty|in_list[1,2,3]'],//来源：1后台添加 2后台导入 3其他
            'status' => ['label' => '会员状态', 'rules' => 'permit_empty|in_list[1,0]'],//状态：0禁用 1启用
            'related_company' => ['label' => '所在公司', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Type\Individual $customerService */
        $customerService = load_service('Customer\Type\Individual');
        $data = $customerService->list($validData);

        success('获取成功', $data);
    }

    /**
     * @desc 新增会员
     *
     * <AUTHOR> 2025-06-09
     */
    public function create()
    {
        $validation = service('validation');
        $rules = [
//            'customer_type' => ['label' => '客户类型', 'rules' => 'required|in_list[1,2,3,4]'],//客户类型：1散客客户 2企业客户 3公务客户 4分销客户
            'contact_mobile' => ['label' => '会员手机号', 'rules' => 'required|max_length[20]|min_length[4]'],
            'customer_name' => ['label' => '会员名称', 'rules' => 'required|max_length[20]|min_length[2]'],
            'customer_grade' => ['label' => '会员等级', 'rules' => 'required|in_list[1,2,3,4]'],//会员等级：1初始会员 2中级会员 3高级会员 4VIP会员
            'customer_name_en' => ['label' => '英文名称', 'rules' => 'permit_empty|max_length[30]|min_length[2]'],
            'gender' => ['label' => '会员性别', 'rules' => 'required|in_list[1,2]'],//性别：1男 2女
            'email' => ['label' => '电子邮箱', 'rules' => 'permit_empty|max_length[20]|min_length[4]'],
            'weixin' => ['label' => '微信账号', 'rules' => 'permit_empty|max_length[20]|min_length[2]'],
            'country' => ['label' => '会员国籍', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'cash_flag' => ['label' => '预存金账户', 'rules' => 'required|in_list[1,0]'],//开通预存金账户：0未开通 1开通
            'credit_flag' => ['label' => '授信账户', 'rules' => 'required|in_list[1,0]'],//开通授信账户：0未开通 1开通
            'temp_credit_flag' => ['label' => '临时授信账户', 'rules' => 'required|in_list[1,0]'],//开通临时授信账户：0未开通 1开通
            'bd_user_id' => ['label' => '业务拓展员ID', 'rules' => 'permit_empty|greater_than[0]'],//业务拓展员ID
            'source' => ['label' => '会员来源', 'rules' => 'permit_empty|in_list[1,2,3]'],//来源：1后台添加 2后台导入 3其他
            'province' => ['label' => '所在省份', 'rules' => 'permit_empty|max_length[10]|min_length[2]'],
            'city' => ['label' => '所在城市', 'rules' => 'permit_empty|max_length[10]|min_length[2]'],
            'contact_address' => ['label' => '联系地址', 'rules' => 'permit_empty|max_length[100]|min_length[2]'],
            'related_company' => ['label' => '所在公司', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'client_manager' => ['label' => '客户经理', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'related_company_telephone' => ['label' => '所在公司电话', 'rules' => 'permit_empty|max_length[50]|min_length[5]'],
            'related_compnay_address' => ['label' => '所在公司地址', 'rules' => 'permit_empty|max_length[50]|min_length[5]'],
            'remark' => ['label' => '备注', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'avatar_img' => ['label' => '头像', 'rules' => 'permit_empty|max_length[70]|min_length[5]'],
            'idcard_images' => ['label' => '身份证图片', 'rules' => 'permit_empty|is_array'],
            'idcards' => ['label' => '证件信息', 'rules' => 'required|is_array'],
            'idcards.*.card_type' => [
                'label' => '证件类型',
                'rules' => 'required|in_list[1,2,3,4,5,6,7]'
            ],
            'idcards.*.card_no' => [
                'label' => '证件号码',
                'rules' => 'required|max_length[20]|min_length[6]'
            ],
            'idcards.*.issuing_country' => [
                'label' => '证件签发国',
                'rules' => 'required|max_length[2]|min_length[2]'
            ],
            'idcards.*.expiration_date' => [
                'label' => '证件有效期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Type\Individual $customerService */
        $customerService = load_service('Customer\Type\Individual');
        try {
            $insertCustomerId = $customerService->create($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
        success('创建成功', ['customer_id' => $insertCustomerId]);
    }

    /**
     * @desc 修改会员
     *
     * <AUTHOR> 2025-06-09
     */
    public function update()
    {
        $validation = service('validation');
        $rules = [
            'customer_id' => ['label' => 'customer_id', 'rules' => 'required|greater_than[0]'],
            'contact_mobile' => ['label' => '会员手机号', 'rules' => 'required|max_length[20]|min_length[4]'],
            'customer_name' => ['label' => '会员名称', 'rules' => 'required|max_length[20]|min_length[2]'],
            'customer_grade' => ['label' => '会员等级', 'rules' => 'required|in_list[1,2,3,4]'],//会员等级：1初始会员 2中级会员 3高级会员 4VIP会员
            'customer_name_en' => ['label' => '英文名称', 'rules' => 'permit_empty|max_length[30]|min_length[2]'],
            'gender' => ['label' => '会员性别', 'rules' => 'required|in_list[1,2]'],//性别：1男 2女
            'email' => ['label' => '电子邮箱', 'rules' => 'permit_empty|max_length[20]|min_length[4]'],
            'weixin' => ['label' => '微信账号', 'rules' => 'permit_empty|max_length[20]|min_length[2]'],
            'country' => ['label' => '会员国籍', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'cash_flag' => ['label' => '预存金账户', 'rules' => 'required|in_list[1,0]'],//开通预存金账户：0未开通 1开通
            'credit_flag' => ['label' => '授信账户', 'rules' => 'required|in_list[1,0]'],//开通授信账户：0未开通 1开通
            'temp_credit_flag' => ['label' => '临时授信账户', 'rules' => 'required|in_list[1,0]'],//开通临时授信账户：0未开通 1开通
            'bd_user_id' => ['label' => '业务拓展员ID', 'rules' => 'permit_empty|greater_than[0]'],//业务拓展员ID
            'source' => ['label' => '会员来源', 'rules' => 'permit_empty|in_list[1,2,3]'],//来源：1后台添加 2后台导入 3其他
            'province' => ['label' => '所在省份', 'rules' => 'permit_empty|max_length[10]|min_length[2]'],
            'city' => ['label' => '所在城市', 'rules' => 'permit_empty|max_length[10]|min_length[2]'],
            'contact_address' => ['label' => '联系地址', 'rules' => 'permit_empty|max_length[100]|min_length[2]'],
            'related_company' => ['label' => '所在公司', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'client_manager' => ['label' => '客户经理', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'related_company_telephone' => ['label' => '所在公司电话', 'rules' => 'permit_empty|max_length[50]|min_length[5]'],
            'related_compnay_address' => ['label' => '所在公司地址', 'rules' => 'permit_empty|max_length[50]|min_length[5]'],
            'remark' => ['label' => '备注', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'avatar_img' => ['label' => '头像', 'rules' => 'permit_empty|max_length[70]|min_length[5]'],
            'idcard_images' => ['label' => '身份证图片', 'rules' => 'permit_empty|is_array'],
            'idcards' => ['label' => '证件信息', 'rules' => 'required|is_array'],
            'idcards.*.card_type' => [
                'label' => '证件类型',
                'rules' => 'required|in_list[1,2,3,4,5,6,7]'
            ],
            'idcards.*.card_no' => [
                'label' => '证件号码',
                'rules' => 'required|max_length[20]|min_length[6]'
            ],
            'idcards.*.issuing_country' => [
                'label' => '证件签发国',
                'rules' => 'required|max_length[2]|min_length[2]'
            ],
            'idcards.*.expiration_date' => [
                'label' => '证件有效期',
                'rules' => 'required|valid_date[Y-m-d]'
            ],
        ];
        $validation->setRules($rules);

        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Type\Individual $customerService */
        $customerService = load_service('Customer\Type\Individual');
        try {
            $customerId = $customerService->update($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('修改成功', ['customer_id' => $customerId]);
    }

    /**
     * @desc 会员明细
     *
     * <AUTHOR> 2025-06-13
     */
    public function detail()
    {
        $validation = service('validation');
        $rules = [
            'customer_id' => ['label' => 'customer_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\Customer\Type\Individual $customerService */
        $customerService = load_service('Customer\Type\Individual');
        try {
            $data = $customerService->detail($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('获取成功', $data);
    }

    /**
     * @desc 导入会员
     *
     * <AUTHOR> 2025-06-13
     */
    public function import()
    {
        $validationRule = [
            'customer_file' => [
                'label' => 'Excel File',
                'rules' => [
                    'uploaded[customer_file]',
                    'mime_in[customer_file,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel]',
                    'max_size[customer_file,10240]', // 限制10MB
                    'ext_in[customer_file,xlsx,xls]',
                ],
            ],
        ];
        if (!$this->validate($validationRule)) {
            error(0, $this->validator->getErrors());
        }

        $file = $this->request->getFile('customer_file');
        if (!$file) {
            error(0,'请上传文件,文件KEY名【customer_file】');
        }
        if (!$file->isValid()) {
            error(0,$file->getErrorString());
        }
        /** @var \App\Services\Customer\Type\Individual $customerService */
        $customerService = load_service('Customer\Type\Individual');
        try {
            $result = $customerService->import($file);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
        success('导入成功', $result);
    }

    public function export()
    {
        $validation = service('validation');
        $rules = [
            'customer_ids' => ['label' => '会员ids', 'rules' => 'required|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Type\Individual $customerService */
        $customerService = load_service('Customer\Type\Individual');
        try {
            $customerService->export($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
        success('导出成功');
    }

    /**
     * @desc 修改会员状态
     * @return string
     *
     * <AUTHOR> 2025-06-23
     */
    public function editStatus(): string
    {
        $validation = service('validation');
        $rules = [
            'id' => ['label' => 'ID', 'rules' => 'required|greater_than[0]'],
            'status' => ['label' => '状态', 'rules' => 'required|in_list[0,1]']
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\Customer\Customer $customerService */
        $customerService = load_service('Customer\Customer');
        try {
            $customerService->editStatus($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
        success('修改成功');
    }

    /**
     * @desc 根据类型获取会员列表
     *
     * <AUTHOR> 2025-06-24
     */
    public function getListByType()
    {
        $validation = service('validation');
        $rules = [
            'customer_type' => ['label' => '会员类型', 'rules' => 'required|in_list[1,2,3,4]'],//会员类型：1散客客户 2企业客户 3公务客户 4分销客户
        ];
        $validation->setRules($rules);
        if (!$validation->run(json_decode($this->request->getBody(), true))) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Customer\Customer $customerService */
        $customerService = load_service('Customer\Customer');
        $data = $customerService->list($validData, ['id', 'customer_name', 'card_no', 'contact_mobile']);

        success('获取成功', $data);
    }


}