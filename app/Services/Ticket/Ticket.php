<?php

namespace App\Services\Ticket;

use App\Models\FlightModel;
use App\Models\TicketRefundOrderModel;
use App\Models\PnrPassengerModel;
use App\Services\BaseService;
use App\Helpers\Tools;

class Ticket extends BaseService
{
    public function __constructor()
    {
    }

    //检查改签费
    public function check_change_price($params)
    {
        $order_id = intval($params['order_id']);
        $rebook_type = intval($params['rebook_type']);
        $order_type = intval($params['order_type']);
        $tickets = $params['tickets'];
        $flight_segments = $params['new_flight_segments'];

        $tickets = array_column($tickets, null,'ticket_id');
        $ticket_ids = array_column($tickets, 'ticket_id');

        $pnr_ticket_model = model('PnrTicketModel');
        $flightModel = model('FlightModel');
        $ticket_book_order_model = model('TicketBookOrderModel');
        $ticket_rebook_order_model = model('TicketRebookOrderModel');
        $ticket_book_pax_model = model('TicketBookPaxModel');
        $ticket_rebook_pax_model = model('TicketRebookPaxModel');

        //校验参数有效性
        if ($order_type == 1) {//订单类型：1出票订单 2改签订单
            $order = $ticket_book_order_model->find($order_id);
        } else {
            $order = $ticket_rebook_order_model->find($order_id);
        }
        if (empty($order)) {
            error(0, 'order_id有误');
        }
        //TODO 校验订单的那些状态下，才允许改签

        $flight_numbers = array_column($flight_segments, 'flight_number');
        $flights = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();
        $flights = array_column($flights, null, 'flight_number');

        if ($order_type == 1) {
            $order_passengers = $ticket_book_pax_model->whereIn('id', $ticket_ids)->findAll();
        } else {
            $order_passengers = $ticket_rebook_pax_model->whereIn('id', $ticket_ids)->findAll();
        }

        //航段信息
        $flights_params = [];
        foreach ($flight_segments as $flight_segment) {
            $flight_number = $flight_segment['flight_number'];
            $flight = $flights[$flight_number];
            $departure_date = $flight_segment['departure_date'];//起飞时间
            //起飞/到达时间计算
            $arrival_date = $departure_date;//默认到达日期等于出发日期，如果到达时间小于出发时间默认加1天
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrival_date = strtotime($departure_date . ' +1 day');
                $arrival_date = date('Y-m-d', $arrival_date);
            }
            $departure_datetime = $departure_date . ' ' . $flight['departure_time'] . ':00'; //出发时间
            $arrival_datetime = $arrival_date . ' ' . $flight['arrival_time'] . ':00'; //到达时间
            $flights_params[] = [
                'departure_airport' => $flight['departure_airport'], // 出发机场
                'arrival_airport' => $flight['arrival_airport'], // 到达机场
                'air_equip_type' => $flight['air_equip_type'], // 机型
                'marketing_airline' => $flight['airline_code'], // 市场航空公司
                'operation_airline' => $flight['airline_code'], // 执飞航空公司
                'flight_number' => $flight['flight_number'], // 航班号
                'departure_time' => $departure_datetime, // 起飞时间
                'arrival_time' => $arrival_datetime, // 到达时间
                'type' => 'NORMAL', // 航段状态
                'cabin' => $flight_segment['cabin_no'], // 舱位
                'action_code' => 'HK', // 行动代码
            ];
        }
        //乘客信息
        $passenger_type_arr = [];
        foreach ($order_passengers as $passenger) {
            $price_info = $tickets[$passenger['id']];

            //获取每个类型的乘客信息
            //格式：乘客类型id => [[乘客1],[乘客2]]
            $passenger_type_arr[$passenger['passenger_type']][] = [
                'ticket_id' => $passenger['id'],
                'ticket_number' => $passenger['ticket_number'],//票号
                'is_infant' => $passenger['passenger_type'] == 3 ? true : false,//是否婴儿
                'passenger_name' => $passenger['person_name'],//乘客姓名
                'passenger_type' => $passenger['passenger_type'],
                'differ_fare' => $price_info['differ_fare'],//差价
                'changed_fee' => $price_info['changed_fee'],//改签费
                'marketing_price' => $price_info['marketing_price'],//票面价
                'airline' => $flights_params[0]['marketing_airline'],
            ];
        }
        $price_data = [];//每个乘客的价格信息
        $pricing = new \App\Libraries\Api\IBE\Pricing();
        foreach ($passenger_type_arr as $value) {
            $params = [
                'airline' => $flights_params[0]['marketing_airline'],
                'ticket_number_before' => $value[0]['ticket_number'],
                'is_infant' => $value[0]['is_infant'],//是否婴儿
                'passenger_name' => $value[0]['passenger_name'],
                'involuntary_identifier' =>  $rebook_type == 2 ? true : false,// 是否非自愿
                'pnr' => $order['pnr'],
                'flights' => $flights_params
            ];
            $res = $pricing->query_domestic_reissue_price_by_pnr($params);
            //计算接口返回的改签费用（多航段时候会有多条，需要累加）
            $total_changed_fee = 0;//接口返回的总改期费
            $total_fare_diffe = 0;//接口返回的总票价差
            $total_tax_diff = 0;//接口返回的总税差
            $total_total_fare = 0;//接口返回的总总费用
            $total_exchange_fee = 0;//接口返回的总实际收取改期费
            foreach ($res as $index => $price) {
                $exchange_type = $price['exchange_type'];
                $total_changed_fee = bcadd($total_changed_fee,$price['changed_fee'],2);
                $total_fare_diffe = bcadd($total_fare_diffe,$price['fare_diff'],2);
                $total_tax_diff = bcadd($total_tax_diff,$price['tax_diff'],2);
                $total_total_fare = bcadd($total_total_fare,$price['total_fare'],2);
                $total_exchange_fee = bcadd($total_exchange_fee,$price['exchange_fee'],2);
            }
            foreach ($value as $index => $value2) {
                if ($value2['differ_fare'] != $total_fare_diffe) {
                    error(0, "乘客：{$value2['passenger_name']},提交上来的差价：{$value2['differ_fare']},与接口返回的差价{$total_fare_diffe},不符，请检查");
                }
                if ($value2['changed_fee'] != $total_changed_fee) {
                    error(0, "乘客：{$value2['passenger_name']},提交上来的改签费：{$value2['changed_fee']},与接口返回的改签费{$total_changed_fee},不符，请检查");
                }
                $value2['tax_diff'] = $total_tax_diff;//税差
                $value2['total_fare'] = $total_total_fare;//总费用
                $value2['exchange_fee'] = $total_exchange_fee;//实际收取改期费
                $value2['exchange_type'] = $exchange_type;//OI方式标识 1出新票 0不出新票

                $price_data[] = $value2;
            }
        }
        return $price_data;
    }

    /**
     * @desc 改签
     * <AUTHOR> 2025-06-06
     */
    public function change_ticket($params)
    {
        //比对改签费价格（调用查询改签费接口）
        $check_price_data = $this->check_change_price($params);

        $order_id = intval($params['order_id']);
        $rebook_type = intval($params['rebook_type']);
        $order_type = intval($params['order_type']);
        $rebook_purpose = intval($params['rebook_purpose']);
        $is_send_sms = intval($params['is_send_sms']);
        $contact_name = trim($params['contact_name']);
        $contact_email = trim($params['contact_email']);
        $contact_telephone = trim($params['contact_telephone']);
        $remark = trim($params['remark']);

        $post_tickets = $params['tickets'];
        $post_tickets = array_column($post_tickets, null, 'ticket_id');
        $ticket_ids   = array_column($post_tickets, 'ticket_id');

        $pnr_ticket_segments_model = model('PnrTicketSegmentModel');
        $flight_model = model('FlightModel');

        $ticket_book_order_model = model('TicketBookOrderModel');
        $ticket_book_order_detail_model = model('TicketBookOrderDetailModel');
        $ticket_book_pax_model = model('TicketBookPaxModel');
        $ticket_book_seg_model = model('TicketBookSegModel');
        $ticket_book_pax_seg_model = model('TicketBookPaxSegModel');

        $ticket_rebook_order_model = model('TicketRebookOrderModel');
        $ticket_rebook_order_detail_model = model('TicketRebookOrderDetailModel');
        $ticket_rebook_pax_model = model('TicketRebookPaxModel');
        $ticket_rebook_seg_model = model('TicketRebookSegModel');
        $ticket_rebook_order_passenger_segment_model = model('TicketRebookPaxSegModel');
        $ticket_rebook_order_ticket_price_model = model('TicketRebookOrderTicketPriceModel');

        $flightModel = model('FlightModel');
        $pnr_ticket_model = model('PnrTicketModel');
        $pnr_ticket_segment_model = model('PnrTicketSegmentModel');


        $post_old_flights = $params['old_flight_segments'];
        $post_new_flights = $params['new_flight_segments'];

        if (count($post_old_flights) != count($post_new_flights)) {
            error(0, '新旧航段数量不匹配');
        }

        //校验参数有效性
        if ($order_type == 1) {//订单类型：1出票订单 2改签订单
            $order = $ticket_book_order_model->find($order_id);
        } else {
            $order = $ticket_rebook_order_model->find($order_id);
        }
        if (empty($order)) {
            error(0, 'order_id有误');
        }
        //校验对应航段的票号状态
        foreach ($post_old_flights as $post_old_flight) {
            $flight_number = $post_old_flight['flight_number'];
            foreach ($ticket_ids as $ticket_id) {
                if ($order_type == 1) {
                    $order_passenger_segments = $ticket_book_pax_seg_model->where(['passenger_id' => $ticket_id,'status' => 1, 'flight_number' => $flight_number])->findAll();
                } else {
                    $order_passenger_segments = $ticket_rebook_order_passenger_segment_model->where(['passenger_id' => $ticket_id,'status' => 1, 'flight_number' => $flight_number])->findAll();
                }
                if (empty($order_passenger_segments)) {
                    error(0, "票号id：{$ticket_id},航班号：{$flight_number}，状态有变动，票号状态是【可使用(OPEN FOR USE)】才允许改签");
                }
            }
        }

        //TODO 不知道查哪一张表？
        if ($order_type == 1) {//订单类型：1出票订单 2改签订单
            $order_passengers = $ticket_book_pax_model->whereIn('id', $ticket_ids)->findAll();
        } else {
            $order_passengers = $ticket_rebook_pax_model->whereIn('id', $ticket_ids)->findAll();
        }

        $order_passengers = array_column($order_passengers,null, 'ticket_number');

//        $order_passenger_ids = array_column($order_passenger,'id');
//
//        if ($order_type == 1) {//订单类型：1出票订单 2改签订单
//            $order_detail = $ticket_book_order_detail_model->whereIn('order_passenger_id', $order_passenger_ids)->findAll();
//        } else {
//            $order_detail = $ticket_rebook_order_detail_model->whereIn('order_passenger_id', $order_passenger_ids)->findAll();
//        }

        $lib_ticket = new \App\Libraries\Api\IBE\Ticket();

        $total_marketing_amount = 0;//票面价总额

        $request_tickets = [];
        $request_new_flights = [];
        foreach ($check_price_data as $val) {
            $request_tickets[] = [
                'ticket_number_before' => $val['ticket_number'], // 原有电子票号
                'is_infant' => $val['is_infant'], // 是否婴儿
                'passenger_name' => $val['passenger_name'], //乘客姓名
                'involuntary_identifier' => $rebook_type ? true : false, // 是否非自愿改签
                'airline' => $val['airline'], // 航空公司
                'ei' => '不得退改签', // EI
                'exchange_type' => $val['exchange_type'], // 退改类型 0不出新票 1出新票
                'exchange_fee' => $val['exchange_fee'], // 退改签费
                'change_fee' => $val['changed_fee'], // 改签费
                'ticket_fare' => $val['marketing_price'], // 票面价
                'fare_diff' => $val['differ_fare'], // 票面差价
                'tax_diff' => $val['tax_diff'], // 税费差价
                'total_fare' => $val['total_fare'], // 总运价
            ];
            $total_marketing_amount = bcadd($total_marketing_amount, $val['marketing_price'], 2);
        }

        $flight_numbers = array_column($post_new_flights, 'flight_number');
        $flights = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();
        $flights = array_column($flights, null, 'flight_number');

        foreach ($post_new_flights as $val) {
            $departure_date = $val['departure_date'];
            $flight_number = $val['flight_number'];
            $cabin_no = $val['cabin_no'];
            $flight = $flights[$flight_number];
            $arrival_date = $departure_date;//默认到达时间等于出发时间，如果到达时间小于出发时间默认加1天
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrival_date = strtotime($departure_date . ' +1 day');
                $arrival_date = date('Y-m-d', $arrival_date);
            }
            $request_new_flights[] = array(
                'departure_airport' => $flight['departure_airport'], // 出发机场
                'arrival_airport' => $flight['arrival_airport'], // 到达机场
                'air_equip_type' => $flight['air_equip_type'], // 机型
                'marketing_airline' => $flight['airline_code'], // 市场航空公司
                'operation_airline' => $flight['airline_code'], // 执飞航空公司
                'flight_number' => $flight_number, // 航班号
                'departure_time' => $departure_date . 'T' . $flight['departure_time'] . ':00', //起飞时间
                'arrival_time' => $arrival_date . 'T' . $flight['arrival_time'] . ':00', //到达时间
                'type' => 'NORMAL', // 航段类型 NORMAL普通航班
                'cabin' => $cabin_no, // 舱位
                'action_code' => 'HK', // 行动代码
            );
        }
        $request_params = [
            'pnr' => $order['pnr'],
            'tickets' => $request_tickets,
            'flights' => $request_new_flights
        ];

        // 创建新的PNR
        $new_pnr_info = $this->create_pnr_by_rebook($order_id, $order_type, $post_old_flights, $post_new_flights, $post_tickets, $order['pnr']);

        $reissue_res = $lib_ticket->reissue_domestic_ticket($request_params);
        if (empty($reissue_res)) {
            error(0, '改签接口请求失败');
        }

        $check_price_data = array_column($check_price_data, null,'ticket_number');//['旧票号' => [价格信息arr]]

        //记录数据库
        try {
            $db = \Config\Database::connect();
            $db->transStart();
           
            $old_new_ticket_number_map = [];//新旧票号的对应关系 ps: ['旧票号'=>'新票号']
            $journey_info = '';

            foreach ($reissue_res as $val) {
                $ticket_number = $val['ticket_number'];
                $total_amount = $val['total_amount'];
                $currency = $val['currency'];
                $payment_type = $val['payment_type'];
                $ei = $val['ei'];
                $passenger_type = $val['passenger_type'];
                $passenger_name = $val['passenger_name'];
                $flights = $val['flights'];

                //旅客姓名
                $passenger_name_arr[] = $passenger_name;

                foreach ($request_tickets as $request_ticket) {
                    //姓名相同，就认为一组对应关系
                    if ($request_ticket['passenger_name'] == $passenger_name) {
                        $old_new_ticket_number_map[$request_ticket['ticket_number_before']] = $ticket_number;
                    }
                }
                foreach ($flights as $val2) {
                    $journey_info .= $val2['departure_airport'] . $val2['arrival_airport'];
                }
            }

            //3.添加ticket_rebook_orders
            $passenger_names = implode(',',array_column($passenger_name_arr, 'surname'));
            $order_no = $ticket_book_order_model->generate_order_no('C');//订单号
            $insert_order_id = $ticket_rebook_order_model->insert([
                'order_no'  => $order_no,
                'origin_order_type' => $order_type,
                'origin_order_id'  => $order['id'],
                'origin_order_no' => $order['order_no'],
                'ticket_type'  => 1,
                'order_source'  => 1,
                'area_type'  => 1,
                'customer_type'  => 1,
                'customer_id'  => 0,//客户ID，包括直销会员和分销会员
                'rebook_type' => $rebook_type,//改签类型：1自愿改签 2非自愿改签
                'rebook_purpose' => $rebook_purpose,//改签目的：1改期 2升舱
                'pnr' => $new_pnr_info['pnr'],
                'pnr_id' =>  $new_pnr_info['pnr_id'],//TODO 后续需要改成新的pnr_id
                'origin_pnr' => $order['pnr'],
                'origin_pnr_id' => $order['pnr_id'],
                'journey_info' => $journey_info,//航程
                'passenger_number' => count($post_tickets),//旅客人数
                'passenger_names' => $passenger_names,//旅客姓名
                'contact_name' => $contact_name,//联系人
                'contact_telephone' => $contact_telephone,//联系电话
                'contact_email' => $contact_email,
                'is_send_sms' => $is_send_sms,
                'remark' => $remark,
                'total_supplier_amount' => $total_marketing_amount,//总采购金额
                'total_customer_amount' => $total_marketing_amount,//总销售金额
                'office' => config('IBE')->office,//OFFICE号
                'status' => 1,//已订座
                'operator_id' => 1,//TODO 因登录前端还没接，先暂时写死
                'operator_name' => '测试账号'//TODO 因登录前端还没接，先暂时写死
            ]);
            //4.添加order_passenger表
            $passenger_ticket_map = [];

            foreach ($order_passengers as $val) {
                $old_ticket_number = $val['ticket_number'];
                $insert_order_passenger_id = $ticket_rebook_pax_model->insert([
                    'order_id' => $insert_order_id,
                    'passenger_type' => $val['passenger_type'],
                    'ticket_number' => $old_new_ticket_number_map[$old_ticket_number],//新电子票号
                    'origin_ticket_number' => $val['ticket_number'],//旧电子票号
                    'doc_type' => $val['doc_type'],
                    'doc_type_detail' => $val['doc_type_detail'],
                    'doc_id' => $val['doc_id'],
                    'person_name' => $val['person_name'],
                    'name_prn' => $val['name_prn'],
                    'passenger_age' => $val['passenger_age'],
                    'language_type' => $val['language_type'],
                    'doc_issue_country' => $val['doc_issue_country'],
                    'doc_holder_nationality' => $val['doc_holder_nationality'],
                    'doc_holder_ind' => $val['doc_holder_ind'],
                    'birthday' => $val['birthday'],
                    'gender' => $val['gender'],
                    'expire_date' => $val['expire_date'],
                    'given_name' => $val['given_name'],
                    'surname' => $val['surname'],
                    'telephone' => $val['telephone'],
                    'status' => 1,
                ]);
                $passenger_ticket_map[$old_new_ticket_number_map[$old_ticket_number]] = $insert_order_passenger_id;
                $price_info = $check_price_data[$old_ticket_number];
                //总采购金额 = 采购票价差 + 采购税差 + 采购改签费
                $supplier_amount = 0;
                $supplier_amount = bcadd($supplier_amount, $price_info['differ_fare'], 2);
                $supplier_amount = bcadd($supplier_amount, $price_info['tax_diff'], 2);
                $supplier_amount = bcadd($supplier_amount, $price_info['exchange_fee'], 2);

                //5.添加order_detail表
                $add_order_detail = [
                    'order_id' => $insert_order_id,
                    'order_passenger_id' => $insert_order_passenger_id,
                    'product_type' => 1,
                    'product_id' => 0,
                    'supplier_id' => 0,
                    'customer_id' => 0,
                    'supplier_amount' => $supplier_amount,//总采购金额
                    'customer_amount' => $supplier_amount,//总销售金额
                    'ticket_supplier_price_diff' => $price_info['differ_fare'],//采购票价差
                    'ticket_supplier_tax_diff' => $price_info['tax_diff'],//采购税差
                    'ticket_supplier_change_fee' => $price_info['exchange_fee'],//采购改签费
                    'ticket_supplier_service_fee' => 0,//TODO 采购服务费暂时写0
                    'ticket_supplier_amount' => $supplier_amount,//总采购金额
                    'ticket_customer_price_diff' => $price_info['differ_fare'],//销售票价差
                    'ticket_customer_tax_diff' => $price_info['tax_diff'],//销售税差
                    'ticket_customer_change_fee' => $price_info['exchange_fee'],//销售改签费
                    'ticket_customer_service_fee' => 0,//TODO 销售服务费暂时写0
                    'ticket_customer_amount' => $supplier_amount,//总销售金额
                    'origin_price_diff' => $price_info['differ_fare'],//原始价格：票价差
                    'origin_tax_diff' => $price_info['tax_diff'],//原始价格：税差
                    'origin_change_fee' => $price_info['exchange_fee'],//原始价格：改签费
                ];
                $insert_order_detail_id = $ticket_rebook_order_detail_model->insert($add_order_detail);
            }
            //7.添加order_segments表
            $add_order_segments = [];
            $old_flight_numbers = array_column($post_old_flights, 'flight_number');
            $new_flight_numbers = array_column($post_new_flights, 'flight_number');

            //查询旧的所有单航段信息
            if ($order_type == 1) {//订单类型：1出票订单 2改签订单
                $order_segments = $ticket_book_seg_model->where('order_id', $order_id)->findAll();
            } else {
                $order_segments = $ticket_rebook_seg_model->where('order_id', $order_id)->findAll();
            }
            foreach ($order_segments as $val) {
                $flight_number = $val['flight_number'];
                if (in_array($flight_number, $old_flight_numbers)) {
                    continue;
                }
                $add_order_segments[] = [
                    'order_id' => $insert_order_id,
                    'change_type' => 0,
                    'departure_datetime' => $val['departure_datetime'],
                    'arrival_datetime' => $val['arrival_datetime'],
                    'code_share_ind' => $val['code_share_ind'],
                    'airline' => $val['airline'],
                    'flight_number' => $val['flight_number'],
                    'operating_airline' => $val['operating_airline'],
                    'operating_flight_number' => $val['operating_flight_number'],
                    'cabin' => $val['cabin'],
                    'sub_cabin' => $val['sub_cabin'],
                    'fbc' => $val['fbc'],
                    'passenger_number' => $val['passenger_number'],
                    'action_code' => $val['action_code'],
                ];
            }
            $flights = $flight_model->whereIn('flight_number', $new_flight_numbers)->findAll();
            $flights = array_column($flights,null,'flight_number');
            foreach ($post_new_flights as $pf) {
                $flight_number = $pf['flight_number'];
                $cabin_no = $pf['cabin_no'];
                $departure_date = $pf['departure_date'];
                $flight = $flights[$flight_number];
                //起飞/到达时间计算
                $arrival_date = $departure_date;//默认到达日期等于出发日期，如果到达时间小于出发时间默认加1天
                if ($flight['departure_time'] > $flight['arrival_time']) {
                    $arrival_date = strtotime($departure_date . ' +1 day');
                    $arrival_date = date('Y-m-d', $arrival_date);
                }
                $departure_datetime = $departure_date . ' ' . $flight['departure_time'] . ':00'; //出发时间
                $arrival_datetime = $arrival_date . ' ' . $flight['arrival_time'] . ':00'; //到达时间
                $add_order_segments[] = [
                    'order_id' => $insert_order_id,
                    'change_type' => 1,
                    'departure_datetime' => $departure_datetime,
                    'arrival_datetime' => $arrival_datetime,
                    'code_share_ind' => 0,
                    'airline' => $flight['airline_code'],
                    'flight_number' => $flight_number,
                    'operating_airline' => $flight['airline_code'],
                    'operating_flight_number' => $flight_number,
                    'cabin' => $cabin_no,
                    'sub_cabin' => $cabin_no,
                    'fbc' => $order_segments[0]['fbc'],
                    'passenger_number' => count($ticket_ids),
                    'action_code' => $order_segments[0]['action_code'],
                ];
            }
            //foreach ($add_order_segments as $as) {
                $ticket_rebook_seg_model->insertBatch($add_order_segments);
            //}

            foreach ($reissue_res as $val) {
                //2.添加pnr_ticket_segments
                //航程(格式是多个航段的出发到达机场代码拼接，如CANWUH)
                $journey_info = '';
                foreach ($flights as $val2) {
                    $ticket_rebook_order_passenger_segment_model->insert([
                        'passenger_id' => $passenger_ticket_map[$val['ticket_number']],
                        'ticket_number' => $val['ticket_number'],
                        'flight_number' => $val2['flight_number'],
                        'departure_datetime' => date('Y-m-d H:i:s', strtotime($val2['departure_time'])),
                        'departure_airport' => $val2['departure_airport'],
                        'arrival_airport' => $val2['arrival_airport'],
                        'marketing_airline' => $val2['airline_code'],
                        'ticket_status' => 'OPEN FOR USE',
                        'status' => 1
                    ]);
                    $journey_info .= $val2['departure_airport'] . $val2['arrival_airport'];
                }
            }

            //8.修改旧票号对应航班的状态
            foreach ($ticket_ids as $ticket_id) {
                foreach ($old_flight_numbers as $flight_number) {
                    if ($order_type == 1) {
                        $ticket_book_pax_seg_model->where(['passenger_id' => $ticket_id, 'flight_number' => $flight_number])->set(['ticket_status' => 'EXCHANGED', 'ticket_status' => 4])->update();
                    } else {
                        $ticket_rebook_order_passenger_segment_model->where(['passenger_id' => $ticket_id, 'flight_number' => $flight_number])->set(['ticket_status' => 'EXCHANGED', 'ticket_status' => 4])->update();
                    }
                }
            }

            $db->transComplete();
        } catch (\Exception $e) {
            $db->transRollback();
            error(0, $e->getMessage());
        }

        return $insert_order_id;
    }

    /**
     * 查询退款费用
     *
     * @param $ticket_ids
     * @param  string  $ticketType
     *
     * @return array|array[]
     */
    public function query_ticket_price($ticket_ids, $ticketType = TicketRefundOrderModel::TICKET_TYPE_DOMESTIC)
    {
        $order_passenger_model = model('TicketBookPaxModel');
        $order_detail_model = model('TicketBookOrderDetailModel');

        $order_passengers = $order_passenger_model->whereIn('id', $ticket_ids)->findAll();
        $order_passengers = array_column($order_passengers, null, 'id');
        $order_passenger_ids = array_column($order_passengers, 'id');

        $order_detail = $order_detail_model->whereIn('order_passenger_id', $order_passenger_ids)->findAll();

        //查询退票费用
        $refund_fee = $this->query_refund_fee($ticket_ids, $ticketType);
        $refund_fee = array_column($refund_fee, null,'ticket_id');

        $price_data = [
            'purchase' => [//采购
                'detail' => [],
                'total' => [
                    'total_customer_price' => 0.00,
                    'total_tax_cn' => 0.00,
                    'total_tax_yq' => 0.00,
                    'total_tax_xt' => 0.00,
                    'total_supplier_agency_fee' => 0.00,//总代理费
                    'total_customer_amount' => 0.00,
                    'total_purchase_amount' => 0.00,
                    'total_supplier_refund_fee_percent' => 0.00,//采购退票费率
                    'total_service_fee' => 0.00,//总手续费
                    'total_deduction' => 0.00,//总退票费
                    'total_refunded_amount' => 0.00//总应退总额
                ]
            ],
            'sales' => [//销售
                'detail' => [],
                'total' => [
                    'total_customer_price' => 0.00,
                    'total_tax_cn' => 0.00,
                    'total_tax_yq' => 0.00,
                    'total_tax_xt' => 0.00,
                    'total_customer_amount' => 0.00,
                    'total_customer_refund_fee_percent' => 0.00,//采购退票费率
                    'total_service_fee' => 0.00,//总手续费
                    'total_deduction' => 0.00,//总退票费
                    'total_customer_reward' => 0.00,//总让利价
                    'total_total_receivable' => 0.00,//总应收总额
                    'total_refunded_amount' => 0.00//总应退总额
                ]
            ]
        ];
        foreach ($order_detail as $key => $val) {
            $order_passenger = $order_passengers[$val['order_passenger_id']];
            //采购机票应退总额=采购总价-退票费
            $total_refunded_amount = bcsub($val['supplier_amount'], $refund_fee[$val['order_passenger_id']]['deduction'], 2);
            //采购
            $price_data['purchase']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'ticket_id' => $val['order_passenger_id'],
                'ticket_number' => $order_passenger['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'supplier_price' => $val['ticket_supplier_price'],//采购价(票价)
                'tax_cn' => $val['ticket_tax_cn'],//机建
                'tax_yq' => $val['ticket_tax_yq'],//燃油
                'tax_xt' => $val['ticket_tax_xt'],//其他
                'customer_amount' => $val['customer_amount'],//总销售金额（票面总价）
                'supplier_agency_fee' => $val['ticket_supplier_agency_fee'],//代理费
                'supplier_amount' => $val['supplier_amount'],//采购总价（采购价）
                'customer_reward' => '-',//让利价
                'total_receivable' => '-',//机票应收总额
                'supplier_refund_fee_percent' => 0,//采购退票费率
                'service_fee' => 0,//退票手续费
                'deduction' => $refund_fee[$val['order_passenger_id']]['deduction'],//退票费
                'total_refunded_amount' => $total_refunded_amount//机票应退总额
            ];
            $price_data['purchase']['total']['total_customer_price'] = bcadd($price_data['purchase']['total']['total_customer_price'], $val['ticket_marketing_price'], 2);//总票价
            $price_data['purchase']['total']['total_tax_cn'] = bcadd($price_data['purchase']['total']['total_tax_cn'], $val['ticket_tax_cn'], 2);//总基建
            $price_data['purchase']['total']['total_tax_yq'] = bcadd($price_data['purchase']['total']['total_tax_yq'], $val['ticket_tax_yq'], 2);//总燃油
            $price_data['purchase']['total']['total_tax_xt'] = bcadd($price_data['purchase']['total']['total_tax_xt'], $val['ticket_tax_xt'], 2);//总其他税费
            $price_data['purchase']['total']['total_customer_amount'] = bcadd($price_data['purchase']['total']['total_customer_amount'], $val['customer_amount'], 2);//总票面总价
            $price_data['purchase']['total']['total_supplier_agency_fee'] = bcadd($price_data['purchase']['total']['total_supplier_agency_fee'], $val['ticket_supplier_agency_fee'], 2);//总代理费
            $price_data['purchase']['total']['total_purchase_amount'] = bcadd($price_data['purchase']['total']['total_purchase_amount'], $val['ticket_supplier_price'], 2);//总采购价
            $price_data['purchase']['total']['total_supplier_refund_fee_percent'] = 0;//退票费率
            $price_data['purchase']['total']['total_service_fee'] = bcadd($price_data['purchase']['total']['total_service_fee'],0,2);//总手续费
            $price_data['purchase']['total']['total_deduction'] = bcadd($price_data['purchase']['total']['total_deduction'],$refund_fee[$val['order_passenger_id']]['deduction'],2);//总退票费
            $price_data['purchase']['total']['total_refunded_amount'] = bcadd($price_data['purchase']['total']['total_refunded_amount'],$total_refunded_amount);//总应退总额
            //销售
            $total_receivable = bcsub($val['customer_amount'], $val['ticket_customer_adjust_fee'], 2);//应收总额 = 总销售金额-让利价
            //销售机票应退总额=机票应收总额-退票费-退票手续费（默认0）
            $total_refunded_amount = bcsub($total_receivable, $refund_fee[$val['order_passenger_id']]['deduction'], 2);
            $total_refunded_amount = bcsub($total_refunded_amount, 0, 2);
            $price_data['sales']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'ticket_id' => $val['order_passenger_id'],
                'ticket_number' => $order_passenger['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'customer_price' => $val['ticket_marketing_price'],//销售价（票价）
                'tax_cn' => $val['ticket_tax_cn'],//机建
                'tax_yq' => $val['ticket_tax_yq'],//燃油
                'tax_xt' => $val['ticket_tax_xt'],//其他
                'customer_amount' => $val['customer_amount'],//总销售金额（票面总价）
                'supplier_agency_fee' => '-',//代理费
                'supplier_price' => '-',//采购价
                'customer_reward' => $val['ticket_customer_adjust_fee'],//让利价
                'total_receivable' => $total_receivable,//应收总额
                'customer_refund_fee_percent' => 0,//销售退票费率
                'service_fee' => 0,//退票手续费
                'deduction' => $refund_fee[$val['order_passenger_id']]['deduction'],//退票费
                'total_refunded_amount' => $total_refunded_amount//应退总额
            ];
            $price_data['sales']['total']['total_customer_price'] = bcadd($price_data['sales']['total']['total_customer_price'], $val['ticket_marketing_price'], 2);//总票价
            $price_data['sales']['total']['total_tax_cn'] = bcadd($price_data['sales']['total']['total_tax_cn'], $val['ticket_tax_cn'], 2);//总基建
            $price_data['sales']['total']['total_tax_yq'] = bcadd($price_data['sales']['total']['total_tax_yq'], $val['ticket_tax_yq'], 2);//总燃油
            $price_data['sales']['total']['total_tax_xt'] = bcadd($price_data['sales']['total']['total_tax_xt'], $val['ticket_tax_xt'], 2);//总其他税费
            $price_data['sales']['total']['total_customer_amount'] = bcadd($price_data['sales']['total']['total_customer_amount'], $val['customer_amount'], 2);//总票面总价
            $price_data['sales']['total']['total_customer_reward'] = bcadd($price_data['sales']['total']['total_customer_reward'], $val['ticket_customer_adjust_fee'], 2);//总让利价
            $price_data['sales']['total']['total_customer_refund_fee_percent'] = 0;
            $price_data['sales']['total']['total_service_fee'] = bcadd($price_data['sales']['total']['total_service_fee'],0,2);//总手续费
            $price_data['sales']['total']['total_deduction'] = bcadd($price_data['sales']['total']['total_deduction'],$refund_fee[$val['order_passenger_id']]['deduction'],2);//总退票费
            $price_data['sales']['total']['total_total_receivable'] = bcadd($price_data['sales']['total']['total_total_receivable'], $total_receivable, 2);//总应收总额
            $price_data['sales']['total']['total_refunded_amount'] = bcadd($price_data['sales']['total']['total_refunded_amount'],$total_refunded_amount);//总应退总额
        }

        return $price_data;
    }

    //查询退票费用
    public function query_refund_fee($ticket_ids, $eticket_type)
    {
        $order_passenger_model = model('TicketBookPaxModel');
        $order_passengers      = $order_passenger_model->whereIn('id', $ticket_ids)->findAll();
        $order_passengers      = array_column($order_passengers, null, 'id');

        $ticketObj = new \App\Libraries\Api\IBE\Ticket();
        $data      = [];
        foreach ($order_passengers as $passenger) {
            $ticket_id        = $passenger['id'];
            $ticket_number    = $passenger['ticket_number'];
            $pnr_passenger_id = $passenger['id'];
            $passenger_type   = $passenger['passenger_type'];
            $is_infant        = false;
            if ($passenger_type == 3) {
                $is_infant = true;
            }
            $params     = [
                'ticket_number' => $ticket_number,
                'eticket_type'  => $eticket_type,//电子票类型：DOMESTIC国内票 INTERNATIONAL国际票
                'is_infant'     => $is_infant,// 是否婴儿
            ];
            $ticket_res = $ticketObj->query_refund_fee_no_cancel_pnr($params);
            $data[]     = [
                'ticket_id'        => $ticket_id,//票号id
                'pnr_passenger_id' => $pnr_passenger_id,//PNR乘客ID
                'ticket_number'    => $ticket_number,
                'deduction'        => $ticket_res['deduction'],//退票手续费
                'airline_refund'   => $ticket_res['airline_refund'],//退票费
                'eticket_type'     => $eticket_type,//电子票类型：DOMESTIC国内票 INTERNATIONAL国际票
            ];
        }

        return $data;
    }

    //退票
    public function refund_ticket($refund_data)
    {
        $ticketObj = new \App\Libraries\Api\IBE\Ticket();
        $data      = [];
        foreach ($refund_data as $val) {
            $eticket_type            = $val['eticket_type'] == 'DOMESTIC' ? 1 : 2;
            $params                  = [
                'ticket_number' => $val['ticket_number'],
                'type'          => $eticket_type, // 1国内票 2国际票
            ];
            $res                     = $ticketObj->refund_ticket($params);
            $res['pnr_passenger_id'] = $val['pnr_passenger_id'];
            $res['ticket_id']        = $val['ticket_id'];
            $data[]                  = $res;
        }

        return $data;
    }

    public function create_pnr_by_rebook($order_id, $order_type, $post_old_flights, $post_new_flights, $post_tickets, $old_pnr) {
        $book_seg_model = model('TicketBookSegModel');
        $rebook_seg_model = model('TicketRebookSegModel');
        $book_order_passenger_model = model('TicketBookPaxModel');
        $rebook_pax_model = model('TicketBookPaxModel');
        $pnr_model = model('PnrModel');
        $flight_model = model('FlightModel');

        if ($order_type == 1) {
            $old_order_segments = $book_seg_model->where('order_id', $order_id)->findAll();
        } else {
            $old_order_segments = $rebook_seg_model->where('order_id', $order_id)->findAll();
        }

        $flight_numbers = array_column($old_order_segments, 'flight_number');
        $new_flight_numbers = array_column($post_new_flights, 'flight_number');
        $flight_numbers = array_merge($flight_numbers, $new_flight_numbers);
        $flights = $flight_model->whereIn('flight_number', $flight_numbers)->findAll();
        $flights = array_column($flights, null, 'flight_number');

        $post_old_flights = array_column($post_old_flights, 'pnr_segment_id');
        $flight_segment_arr = [];
        $i = 1;
        foreach ($old_order_segments as $segment) {
            if (!in_array($segment['id'], $post_old_flights)) {
                $flight = $flights[$segment['flight_number']];

                //第一个航段的起飞时间
                if (!isset($first_departure_datetime)) {
                    $first_departure_datetime = str_replace(' ', 'T', $segment['departure_datetime']);
                }

                $flight_segment_arr[] = [
                    'rph' => $i,//航段编号
                    'departure_datetime' => str_replace(' ', 'T', $segment['departure_datetime']), //出发时间
                    'arrival_datetime' => str_replace(' ', 'T', $segment['arrival_datetime']), //到达时间
                    'code_share_ind' => false, // 是否共享航班
                    'flight_number' => $segment['flight_number'], //航班号
                    'status' => 'NN', // 行动代码
                    'segment_type' => 'NORMAL', // 航段类型，默认为NORMAL
                    'departure_airport' => $flight['departure_airport'], //出发机场
                    'arrival_airport' => $flight['arrival_airport'], //到达机场
                    'air_equip_type' => $flight['air_equip_type'], //机型
                    'marketing_airline' => $flight['airline_code'],//航空公司字母代码
                    'booking_class_avail' => $segment['cabin'], // 预订舱位
                    'product_no' => $segment['fbc'],
                ];
                $i++;
            }
        }



        foreach ($post_new_flights as $fs) {
            $departure_date = $fs['departure_date'];
            $flight_number = $fs['flight_number'];
            $rph = $i;
            $cabin = $fs['cabin_no'];
            $product_no = $fs['cabin_no'];

            $flight = $flights[$flight_number];

            //到达时间
            $arrival_date = $departure_date;//TODO:默认到达时间等于出发时间，如果到达时间小于出发时间默认加1天，后续要改成读取配置获取
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrival_date = strtotime($departure_date . ' +1 day');
                $arrival_date = date('Y-m-d', $arrival_date);
            }

            //第一个航段的起飞时间
            if (!isset($first_departure_datetime)) {
                $first_departure_datetime = $departure_date . ' ' . $flight['departure_time'] . ':00';
            }

            $flight_segment_arr[] = [
                'rph' => $rph,//航段编号
                'departure_datetime' => $departure_date . 'T' . $flight['departure_time'] . ':00', //出发时间
                'arrival_datetime' => $arrival_date . 'T' . $flight['arrival_time'] . ':00', //到达时间
                'code_share_ind' => false, // 是否共享航班
                'flight_number' => $flight_number, //航班号
                'status' => 'NN', // 行动代码
                'segment_type' => 'NORMAL', // 航段类型，默认为NORMAL
                'departure_airport' => $flight['departure_airport'], //出发机场
                'arrival_airport' => $flight['arrival_airport'], //到达机场
                'air_equip_type' => $flight['air_equip_type'], //机型
                'marketing_airline' => $flight['airline_code'],//航空公司字母代码
                'booking_class_avail' => $cabin, // 预订舱位
                'product_no' => $product_no
            ];
        }

        //乘客信息组装
        $inf_rphs = [];//婴儿rphs
        $passenger_arr = [];
        $i = 1;
        $ticket_ids = array_column($post_tickets, 'ticket_id');
        if ($order_type == 1) {
            $order_passengers = $book_order_passenger_model->where('id', $ticket_ids)->findAll();
        } else {
            $order_passengers = $rebook_pax_model->where('id', $ticket_ids)->findAll();
        }
        foreach ($order_passengers as $pg) {
            $rph = $i;
            $passenger_type_code = intval($pg['passenger_type']);//乘客类型：1成人 2儿童 3婴儿  对应接口：ADT成人 CHD儿童 INF婴儿
            $certificate_type = intval($pg['doc_type']);//证件类型：1身份证 2护照  对应接口：NI 身份证 PP 护照
            $certificate_number = trim($pg['doc_id']);//证件号码
            $contact_phone = trim($pg['telephone']);//乘客联系电话
            $infant_traveler_rph = 0;//婴儿关联的成人乘客ID
            $name = trim($pg['person_name']);//乘客姓名

            $gender = Tools\Idcard::get_sex($certificate_number) == 1 ? 'MALE' : 'FEMALE';//1（男）或 0（女）

            //校验年龄
            $age = Tools\Idcard::get_age($certificate_number);
            switch ($passenger_type_code) {
                case 3://婴儿
                    if (!($age < 2)) {
                        error(0, "乘客序号:{$rph},乘客类型为婴儿和身份证年龄不符");
                    }
                    $inf_rphs[] = $rph;
                    break;
            }

            $passenger_arr[] = [
                'rph' => $rph,
                'passenger_type_code' => PnrPassengerModel::PASSENGER_TYPE_MAP[$passenger_type_code], //乘客类型：ADT成人 CHD儿童 INF婴儿
                'gender' => $gender,
                'language_type' => 'ZH',
                'surname' => $name,
                'doc' => [//证件
                    'doc_type' => $certificate_type,
                    'doc_id' => $certificate_number
                ],
                'ctcm' => $contact_phone,//乘客联系电话
                'infant_traveler_rph' => $infant_traveler_rph, // 可选，关联婴儿乘客ID，如果没有关联，则可以提供该字段
                'tc' => ''//TC组，可选，可不填或留空
            ];
            $i++;
        }

        //设置携带者rph（成人绑定婴儿rph）
        foreach ($passenger_arr as $k => $p) {
            if ($p['passenger_type_code'] == 'ADT' && !empty($inf_rphs)) {
                $p['infant_traveler_rph'] = array_shift($inf_rphs);
            }
            $passenger_arr[$k] = $p;
        }

        //留座时间，默认为起飞前两小时，用起飞时间计算
        $subtract_hours = strtotime('-2 hours', strtotime($first_departure_datetime));
        $subtract_hours = date('Y-m-d H:i:s', $subtract_hours);
        $ticket_time_limit_db = $subtract_hours;
        $subtract_hours = explode(' ', $subtract_hours);
        $ticket_time_limit = $subtract_hours[0] . 'T' . $subtract_hours[1];

        $params = [
            'flight_segments' => $flight_segment_arr,//航段信息
            'passengers' => $passenger_arr,//乘客信息
            'airline' => '',// 航空公司CZ
            'ctct' => '',//预订人联系方式
            'ticket_time_limit' => $ticket_time_limit,//留座时间
            'tc' => '', // TC组，可选，可不填或留空
        ];

        $booking = new \App\Libraries\Api\IBE\Booking();
        $pnr = $booking->create_pnr($params);
        if (!empty($pnr_model->where('pnr', $pnr)->first())) {
            error(0, 'PNR已存在，请勿重复生成');
        }

        $count_passenger = count($passenger_arr);//旅客人数
        $old_pnr_info = $pnr_model->where('pnr', $old_pnr)->first();
        $insert_pnr_id = $pnr_model->insert([
            'pnr'  => $old_pnr_info['pnr'],
            'ticket_time_limit'  => $ticket_time_limit_db,
            'passenger_number' => $count_passenger,
            'contact_name' => '',
            'contact_telephone' => ''
        ]);
        if (empty($insert_pnr_id)) {
            error(0, "添加PNR表失败");
        }

        return [
                'pnr' => $pnr, 
                'pnr_id' => $insert_pnr_id
            ];
    }
}