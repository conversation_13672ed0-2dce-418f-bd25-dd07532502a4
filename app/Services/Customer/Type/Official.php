<?php
namespace App\Services\Customer\Type;

use App\Helpers\Tools;
use App\Models\CustomerAccountModel;
use App\Models\CustomerIdcardModel;
use App\Models\CustomerModel;
use App\Models\FileUploadModel;
use App\Services\BaseService;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Official extends BaseService
{
    public function __constructor()
    {
    }

    /**
     * @desc 公务会员列表
     * @param $params
     * @return array
     *
     * <AUTHOR> 2025-06-18
     */
    public function list($params)
    {
        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 10;
        $customerGrade = intval($params['customer_grade']);//会员等级
        $cardNo = trim($params['card_no']);
        $customerName = trim($params['customer_name']);
        $contactMobile = trim($params['contact_mobile']);
        $bdUserId = intval($params['bd_user_id']);
        $source = intval($params['source']);
        $status = $params['status'] ?? '';
        $parentId = intval($params['parent_id']);

        $customerModel = model('CustomerModel');

        $customer_where = [];
        if ($customerGrade) {
            $customer_where['customer_grade'] = $customerGrade;
        }
        if ($cardNo) {
            $customer_where['card_no'] = $cardNo;
        }
        if ($customerName) {
            $customer_where['customer_name'] = $customerName;
        }
        if ($contactMobile) {
            $customer_where['contact_mobile'] = $contactMobile;
        }
        if ($bdUserId) {
            $customer_where['bd_user_id'] = $bdUserId;
        }
        if ($source) {
            $customer_where['source'] = $source;
        }
        if ($source) {
            $customer_where['source'] = $source;
        }
        if ($status !== '') {
            $customer_where['status'] = $status;
        }
        if ($parentId) {
            $customer_where['parent_id'] = $parentId;
        }
        $customer_where['customer_type'] = CustomerModel::CUSTOMER_TYPE_OFFICIAL;
        //分页
        $list = $customerModel->paginate_list($customer_where, $page, $perPage);
        $pager = $customerModel->pager;
        $data = [
            'list' => $list,
            'total' => $pager->getTotal(),//总条数
            'perPage' => $pager->getPerPage(),//每页显示条数
            'pageCount' => $pager->getPageCount(),//总页数
            'currentPage' => $pager->getCurrentPage(),//当前页数
        ];
        return $data;
    }

    /**
     * @desc 添加公务会员
     * @param $params
     * @return bool|int|string
     * @throws \Exception
     *
     * <AUTHOR> 2025-06-11
     */
    public function create($params)
    {
        $customerModel = model('CustomerModel');
        $customerAccountModel = model('CustomerAccountModel');
        $customerIdcardModel = model('CustomerIdcardModel');
        $fileUploadModel = model('FileUploadModel');

        $contactMobile = trim($params['contact_mobile']); //会员手机号
        $customerLevel = intval($params['customer_level']);//客户级别
        $parentId = intval($params['parent_id']);//上级会员
        $customerName = trim($params['customer_name']); //会员名称
        $customerGrade = intval($params['customer_grade']); //会员等级
        $customerNameEn = trim($params['customer_name_en']); //英文名称
        $gender = intval($params['gender']); //会员性别
        $email = trim($params['email']); //电子邮箱
        $weixin = trim($params['weixin']); //微信账号
        $country = trim($params['country']); //会员国籍
        $cash_flag = intval($params['cash_flag']);//开通预存金账户：0未开通 1开通
        $credit_flag = intval($params['credit_flag']);//开通授信账户：0未开通 1开通
        $temp_credit_flag = intval($params['temp_credit_flag']);//开通临时授信账户：0未开通 1开通
        $bankCardNo = trim($params['bank_card_no']);//公务卡号
        $issuingBank = intval($params['issuing_bank']);//发卡行
        $bd_user_id = intval($params['bd_user_id']);//业务拓展员ID
        $source = intval($params['source']);//来源：1后台添加 2后台导入 3其他
        $province = trim($params['province']);//所在省份
        $city = trim($params['city']);//所在城市
        $contact_address = trim($params['contact_address']);
        $related_company = trim($params['related_company']);//所在公司
        $client_manager = trim($params['client_manager']);//客户经理
        $related_company_telephone = trim($params['related_company_telephone']);//所在公司电话
        $related_compnay_address = trim($params['related_compnay_address']);//所在公司地址
        $remark = trim($params['remark']);//备注
        $avatarImg = trim($params['avatar_img']);//头像
        $idcardImages = array_filter($params['idcard_images']);//身份证图片
        $idcards = $params['idcards'];

        if (!Tools\Mobile::validPhone($contactMobile)) {
            throw new \Exception("会员手机号码有误_{$contactMobile}");
        }
        if ($customerLevel == CustomerModel::CUSTOMER_LEVEL_SECOND) {
            if (!$parentId) {
                throw new \Exception("请选择上级会员");
            }
            $isExist = $customerModel->where(['id' => $parentId, 'parent_id' => 0,'customer_type' => CustomerModel::CUSTOMER_TYPE_OFFICIAL])->first();
            if (!$isExist) {
                throw new \Exception("上级会员id错误");
            }
        }
        if ($customerLevel == CustomerModel::CUSTOMER_LEVEL_FIRST && $parentId) {
            throw new \Exception("一级会员无需选择上级会员");
        }
        $cardNo = $customerModel->generateCardNo(CustomerModel::CUSTOMER_TYPE_OFFICIAL);//会员卡号
        try {
            $db = \Config\Database::connect();
            $db->transStart();
            $insertCustomerId = $customerModel->insert([
                'customer_type'  => CustomerModel::CUSTOMER_TYPE_OFFICIAL,
                'card_no'  => $cardNo,
                'contact_mobile'  => $contactMobile,
                'customer_level' => $customerLevel,
                'parent_id' => $parentId,
                'customer_name'  => $customerName,
                'customer_grade'  => $customerGrade,
                'customer_name_en'  => $customerNameEn,
                'gender'  => $gender,
                'email'  => $email,
                'weixin'  => $weixin,
                'country'  => $country,
                'status' => CustomerModel::STATUS_ENABLE,
                'bd_user_id'  => $bd_user_id,
                'source'  => $source,
                'related_company'  => $related_company,
                'related_company_telephone'  => $related_company_telephone,
                'related_compnay_address'  => $related_compnay_address,
                'remark' => $remark,
                'user_id' => 0, //TODO 需要获取对应的用户id
                'province' => $province,
                'city' => $city,
                'address' => $contact_address,
                'client_manager' => $client_manager,
                'bank_card_no' => $bankCardNo,
                'issuing_bank' => $issuingBank,
            ]);
            if (empty($insertCustomerId)) {
                throw new \Exception("添加会员表失败");
            }
            //添加会员账户
            $customerAccountModel->insert([
                'customer_id' => $insertCustomerId,
                'cash_flag' => $cash_flag,
                'credit_flag' => $credit_flag,
                'temp_credit_flag' => $temp_credit_flag,
                'cash_balance' => 0.00,
                'credit_line' => 0.00,
                'credit_used' => 0.00,
                'temp_credit_line' => 0.00,
                'temp_credit_used' => 0.00,
                'status' => CustomerAccountModel::STATUS_ENABLE,
                'user_id' => 0 //TODO 需要获取对应的用户id
            ]);
            //添加会员证件
            if (!empty($idcards)) {
                foreach ($idcards as $idcard) {
                    $customerIdcardModel->insert([
                        'customer_id' => $insertCustomerId,
                        'card_type' => $idcard['card_type'],
                        'card_no' => $idcard['card_no'],
                        'issuing_country' => $idcard['issuing_country'],
                        'expiration_date' => $idcard['expiration_date'],
                        'status' => CustomerIdcardModel::STATUS_ENABLE,
                    ]);
                }
            }
            //添加头像图片
            if (!empty($avatarImg)) {
                $fileUploadModel->insert([
                    'upload_type' => FileUploadModel::UPLOAD_TYPE_AVATAR,
                    'related_id' => $insertCustomerId,
                    'file_type' => FileUploadModel::FILE_TYPE_IMG,
                    'file_path' => FileUploadModel::FILE_PATH_AVATAR,
                    'file_name' => $avatarImg,
                    'thumbnail' => 'thumb_' . $avatarImg,
                    'status' => FileUploadModel::STATUS_ENABLE,
                ]);
            }
            //添加身份证图片
            if (!empty($idcardImages)) {
                foreach ($idcardImages as $idcardImage) {
                    $fileUploadModel->insert([
                        'upload_type' => FileUploadModel::UPLOAD_TYPE_CERTIFICATE,
                        'related_id' => $insertCustomerId,
                        'file_type' => FileUploadModel::FILE_TYPE_IMG,
                        'file_path' => FileUploadModel::FILE_PATH_IDCARD,
                        'file_name' => $idcardImage,
                        'thumbnail' => 'thumb_' . $idcardImage,
                        'status' => FileUploadModel::STATUS_ENABLE,
                    ]);
                }
            }
            $db->transComplete();
        } catch (\Exception $e) {
            $db->transRollback();
            throw new \Exception($e->getMessage());
        }

        return $insertCustomerId;
    }

    /**
     * @desc 修改公务会员
     * @param $params
     * @return int
     * @throws \Exception
     *
     * <AUTHOR> 2025-06-11
     */
    public function update($params)
    {
        $customerModel = model('CustomerModel');
        $customerAccountModel = model('CustomerAccountModel');
        $customerIdcardModel = model('CustomerIdcardModel');
        $fileUploadModel = model('FileUploadModel');

        $customerId = intval($params['customer_id']);
        $contactMobile = trim($params['contact_mobile']); //会员手机号
        $customerLevel = intval($params['customer_level']);//客户级别
        $parentId = intval($params['parent_id']);//上级会员
        $customerName = trim($params['customer_name']); //会员名称
        $customerGrade = intval($params['customer_grade']); //会员等级
        $customerNameEn = trim($params['customer_name_en']); //英文名称
        $gender = intval($params['gender']); //会员性别
        $email = trim($params['email']); //电子邮箱
        $weixin = trim($params['weixin']); //微信账号
        $country = trim($params['country']); //会员国籍
        $cash_flag = intval($params['cash_flag']);//开通预存金账户：0未开通 1开通
        $credit_flag = intval($params['credit_flag']);//开通授信账户：0未开通 1开通
        $temp_credit_flag = intval($params['temp_credit_flag']);//开通临时授信账户：0未开通 1开通
        $bankCardNo = trim($params['bank_card_no']);//公务卡号
        $issuingBank = intval($params['issuing_bank']);//发卡行
        $bd_user_id = intval($params['bd_user_id']);//业务拓展员ID
        $source = intval($params['source']);//来源：1后台添加 2后台导入 3其他
        $province = trim($params['province']);//所在省份
        $city = trim($params['city']);//所在城市
        $contact_address = trim($params['contact_address']);
        $related_company = trim($params['related_company']);//所在公司
        $client_manager = trim($params['client_manager']);//客户经理
        $related_company_telephone = trim($params['related_company_telephone']);//所在公司电话
        $related_compnay_address = trim($params['related_compnay_address']);//所在公司地址
        $remark = trim($params['remark']);//备注
        $avatarImg = trim($params['avatar_img']);//头像
        $idcardImages = array_filter($params['idcard_images']);//身份证图片

        $idcards = $params['idcards'];

        $customer = $customerModel->find($customerId);
        if (empty($customer)) {
            throw new \Exception("非法id");
        }
        if ($customer['customer_type'] != CustomerModel::CUSTOMER_TYPE_OFFICIAL) {
            throw new \Exception("非法id,仅支持公务会员");
        }
        if (!Tools\Mobile::validPhone($contactMobile)) {
            throw new \Exception("会员手机号码有误_{$contactMobile}");
        }
        if ($customerLevel == CustomerModel::CUSTOMER_LEVEL_SECOND) {
            if (!$parentId) {
                throw new \Exception("请选择上级会员");
            }
            $isExist = $customerModel->where(['id' => $parentId, 'parent_id' => 0,'customer_type' => CustomerModel::CUSTOMER_TYPE_OFFICIAL])->first();
            if (!$isExist) {
                throw new \Exception("上级会员id错误");
            }
        }
        if ($customerLevel == CustomerModel::CUSTOMER_LEVEL_FIRST && $parentId) {
            throw new \Exception("一级会员无需选择上级会员");
        }
        try {
            $db = \Config\Database::connect();
            $db->transStart();
            //修改会员
            $customerUpdata = [
                'contact_mobile'  => $contactMobile,
                'customer_level' => $customerLevel,
                'parent_id' => $parentId,
                'customer_name'  => $customerName,
                'customer_grade'  => $customerGrade,
                'customer_name_en'  => $customerNameEn,
                'gender'  => $gender,
                'email'  => $email,
                'weixin'  => $weixin,
                'country'  => $country,
                'status' => CustomerModel::STATUS_ENABLE,
                'bd_user_id'  => $bd_user_id,
                'source'  => $source,
                'related_company'  => $related_company,
                'related_company_telephone'  => $related_company_telephone,
                'related_compnay_address'  => $related_compnay_address,
                'remark' => $remark,
                'user_id' => 0, //TODO 需要获取对应的用户id
                'province' => $province,
                'city' => $city,
                'address' => $contact_address,
                'client_manager' => $client_manager,
                'bank_card_no' => $bankCardNo,
                'issuing_bank' => $issuingBank,
            ];
            $customerModel->where(['id' => $customerId])->set($customerUpdata)->update();
            //修改会员账户
            $customerAccountModel->where(['customer_id' => $customerId])->set([
                'cash_flag' => $cash_flag,
                'credit_flag' => $credit_flag,
                'temp_credit_flag' => $temp_credit_flag,
            ])->update();
            //修改会员证件
            $customerIdcardModel->where('customer_id', $customerId)->delete();
            if (!empty($idcards)) {
                foreach ($idcards as $idcard) {
                    $addIdcards[] = [
                        'customer_id' => $customerId,
                        'card_type' => $idcard['card_type'],
                        'card_no' => $idcard['card_no'],
                        'issuing_country' => $idcard['issuing_country'],
                        'expiration_date' => $idcard['expiration_date'],
                        'status' => CustomerIdcardModel::STATUS_ENABLE,
                    ];
                }
                $customerIdcardModel->insertBatch($addIdcards);
            }
            //修改头像图片
            $fileUploadModel->where(['related_id' => $customerId, 'upload_type' => FileUploadModel::UPLOAD_TYPE_AVATAR])->delete();
            if (!empty($avatarImg)) {
                $fileUploadModel->insert([
                    'upload_type' => FileUploadModel::UPLOAD_TYPE_AVATAR,
                    'related_id' => $customerId,
                    'file_type' => FileUploadModel::FILE_TYPE_IMG,
                    'file_path' => FileUploadModel::FILE_PATH_AVATAR,
                    'file_name' => $avatarImg,
                    'thumbnail' => 'thumb_' . $avatarImg,
                    'status' => FileUploadModel::STATUS_ENABLE,
                ]);
            }
            //修改身份证图片
            $fileUploadModel->where(['related_id' => $customerId, 'upload_type' => FileUploadModel::UPLOAD_TYPE_CERTIFICATE])->delete();
            if (!empty($idcardImages)) {
                foreach ($idcardImages as $idcardImage) {
                    if (!empty($idcardImage)) {
                        $fileUploadModel->insert([
                            'upload_type' => FileUploadModel::UPLOAD_TYPE_CERTIFICATE,
                            'related_id' => $customerId,
                            'file_type' => FileUploadModel::FILE_TYPE_IMG,
                            'file_path' => FileUploadModel::FILE_PATH_IDCARD,
                            'file_name' => $idcardImage,
                            'thumbnail' => 'thumb_' . $idcardImage,
                            'status' => FileUploadModel::STATUS_ENABLE,
                        ]);
                    }
                }
            }
            $db->transComplete();
        } catch (\Exception $e) {
            $db->transRollback();
            throw new \Exception($e->getMessage());
        }

        return $customerId;
    }

    public function detail($params)
    {
        $customerModel = model('CustomerModel');
        $customerAccountModel = model('CustomerAccountModel');
        $customerIdcardModel = model('CustomerIdcardModel');
        $countryCodeModel = model('CountryCodeModel');
        $userModel = model('UserModel');
        $fileUploadModel = model('FileUploadModel');

        $customerId = intval($params['customer_id']);
        $customer = $customerModel->find($customerId);
        if (empty($customer)) {
            error(0, 'customer_id错误');
        }
        if ($customer['customer_type'] != CustomerModel::CUSTOMER_TYPE_OFFICIAL) {
            throw new \Exception("非法id,仅支持公务会员");
        }
        $customerAccount = $customerAccountModel->where('customer_id', $customerId)->first();
        $customerIdcards = $customerIdcardModel->where('customer_id', $customerId)->findAll();

        $countryCodes = $countryCodeModel->select('code,name,name_en')->findAll();
        $countryCodes = array_column($countryCodes, null, 'code');
        $users = $userModel->whereIn('id', [$customer['bd_user_id'], $customer['user_id']])->findAll();
        $users = array_column($users, null, 'id');

        //获取头像、身份证图
        $avatar = $fileUploadModel->where(['related_id' => $customerId, 'upload_type' => FileUploadModel::UPLOAD_TYPE_AVATAR])->first();
        $idcardImgs = $fileUploadModel->where(['related_id' => $customerId, 'upload_type' => FileUploadModel::UPLOAD_TYPE_CERTIFICATE])->findAll();

        //上级会员
        if (!empty($customer['parent_id'])) {
            $parentCustomer = $customerModel->find($customer['parent_id']);
        }

        //基本信息
        $baseData = [
            'contact_mobile' => $customer['contact_mobile'],//会员手机号
            'customer_grade' => $customer['customer_grade'],//会员等级
            'customer_grade_text' => CustomerModel::customerGrade($customer['customer_grade']),//会员等级
            'customer_name_en' => $customer['customer_name_en'],
            'gender' => $customer['gender'],
            'gender_text' => CustomerModel::gender($customer['gender']),
            'email' => $customer['email'],
            'weixin' => $customer['weixin'],
            'country' => $customer['country'],
            'country_text' => $countryCodes[$customer['country']]['name'] ?? '',
            'status' => $customer['status'],
            'status_text' => CustomerModel::status($customer['status']),
            'customer_level' => $customer['customer_level'],//会员级别
            'customer_level_text' => CustomerModel::customerLevel($customer['customer_level']),//会员级别
            'parent_id' => $customer['parent_id'],//上级会员
            'parent_name' => $parentCustomer['customer_name'] ?? '',//上级会员名称
            'avatar' => $avatar ?? [],//头像
        ];
        //证件信息
        $IdcardData = [];
        foreach ($customerIdcards as $idcard) {
            $IdcardData[] = [
                'card_type' => $idcard['card_type'],//证件类型
                'card_type_text' => CustomerIdcardModel::cardType($idcard['card_type']),//证件类型
                'card_no' => $idcard['card_no'],//证件号码
                'issuing_country' => $idcard['issuing_country'],//证件签发国
                'issuing_country_text' => $countryCodes[$idcard['issuing_country']]['name'] ?? '',//证件签发国
                'expiration_date' => $idcard['expiration_date'],//有效期
            ];
        }
        //账户信息
        $accountData = [];
        if (!empty($customerAccount)) {
            $accountData = [
                'cash_flag' => $customerAccount['cash_flag'],//预存金账户
                'cash_flag_text' => CustomerAccountModel::cashFlag($customerAccount['cash_flag']),//预存金账户
                'credit_flag' => $customerAccount['credit_flag'],//授信账户
                'credit_flag_text' => CustomerAccountModel::creditFlag($customerAccount['credit_flag']),//授信账户
                'cash_balance' => $customerAccount['cash_balance'],//预存金余额
                'credit_balance' => bcsub($customerAccount['credit_line'], $customerAccount['credit_used'], 2),//授信余额
                'bank_card_no' => $customer['bank_card_no'],//公务卡号
                'issuing_bank' => $customer['issuing_bank'] ? $customer['issuing_bank'] : '',//开卡银行
                'issuing_bank_text' => CustomerModel::bank($customer['issuing_bank']),//开卡银行（文案）
                'idcard_imgs' => $idcardImgs ?? [],
            ];
        }
        $serviceData = [
            'bd_user_id' =>  $customer['bd_user_id'] ? $customer['bd_user_id'] : '',//扩展业务员
            'bd_user_name' =>  $users[$customer['bd_user_id']]['name'] ?? '',//扩展业务员
            'source' => $customer['source'] ? $customer['source'] : '',//会员来源
            'source_text' => CustomerModel::source($customer['source']),//会员来源
            'client_manager' => $customer['client_manager'],//客户经理
            'province' => $customer['province'],//所在省份
            'city' => $customer['city'],//所在城市
            'address' => $customer['address'],//联系地址
            'related_company' => $customer['related_company'],//所在公司
            'related_company_telephone' => $customer['related_company_telephone'],//公司电话
            'related_compnay_address' => $customer['related_compnay_address'],//公司地址
            'perator_name' => $users[$customer['bd_user_id']]['name'] ?? '',//操作员
            'created_at' => date('Y-m-d H:i:s', $customer['created_at']),//注册时间
            'remark' => $customer['remark'],//备注
        ];
        $data = [
            'customer_card_no' => $customer['card_no'],//会员卡号
            'customer_name' => $customer['customer_name'],//会员名称
            'base_data' => $baseData,
            'idcard_data' => $IdcardData,
            'account_data' => $accountData,
            'service_data' => $serviceData,
        ];

        return $data;
    }

    /**
     * @desc 导入公务会员
     * @param $file
     * @return true
     * @throws \Exception
     *
     * <AUTHOR> 2025-06-11
     */
    public function import($file)
    {
        $customerModel = model('CustomerModel');
        $customerIdcardModel = model('CustomerIdcardModel');
        $customerAccountModel = model('CustomerAccountModel');

        $tmpPath = $file->store('excel/');
        $fullPath  = WRITEPATH . 'uploads/' . $tmpPath;

        $spreadsheet = IOFactory::load($fullPath);
        unlink($fullPath);
        $sheet = $spreadsheet->getActiveSheet();
        $rows = $sheet->toArray();
        array_shift($rows);
        array_shift($rows);

        //TODO 检查每一列字段的格式

        try {
            $db = \Config\Database::connect();
            $db->transStart();
            foreach ($rows as $row) {
                // 检查是否为空行：将整行的值连接成一个字符串，并去除空白，如果为空则跳过
                $rowString = implode('', $row);
                if (trim($rowString) === '') {
                    continue;
                }
                $cardNo = $customerModel->generateCardNo(CustomerModel::CUSTOMER_TYPE_OFFICIAL);//会员卡号
                $customerName = trim($row[0]);//会员名称
                $customerGrade = intval($row[1]);//会员等级
                $contactMobile = trim($row[2]);//会员手机号码
                $customerNameEn = $row[3];//英文名称
                $gender = intval($row[4]);//性别
                $email = trim($row[5]);//电子邮箱
                $weixin = trim($row[6]);//微信账号
                $country = trim($row[7]);//会员国籍
                $cardType = intval($row[8]);//证件类型
                $IDCardNo = trim($row[9]);//证件号码
                $cardType2 = intval($row[10]);//证件类型
                $IDCardNo2 = trim($row[11]);//证件号码
                $bankCardNo = trim($row[12]);//公务卡号
                $issuingBank = intval($row[13]);//开卡银行
                $bdUserId = $row[14];//扩展业务员id
                $source = CustomerModel::SOURCE_IMPORT_BACKGROUND;//会员来源
                $customerManager = trim($row[16]);//客户经理
                $province = trim($row[17]);//所在省份
                $city = trim($row[18]);//所在城市
                $contactAddress = trim($row[19]);//联系地址
                $relatedCompany = trim($row[20]);//所在公司
                $relatedCompanyTelephone = trim($row[21]);//公司电话
                $relatedCompnayAddress = trim($row[22]);//公司地址
                $remark = trim($row[23]);//备注
                
                if (empty($customerName)) {
                    throw new \Exception("会员名称必填");
                }
                if (empty($contactMobile)) {
                    throw new \Exception("会员手机号码必填");
                }
                if (empty($customerGrade)) {
                    throw new \Exception("会员等级必填");
                }
                if (empty($cardType)) {
                    throw new \Exception("证件类型必填必填");
                }
                if (empty($IDCardNo)) {
                    throw new \Exception("证件号码必填必填");
                }
                if (!Tools\Mobile::validPhone($contactMobile)) {
                    throw new \Exception("会员手机号码有误_{$contactMobile}");
                }
                //新增会员
                $addCustomerData = [
                    'customer_type' => CustomerModel::CUSTOMER_TYPE_OFFICIAL,
                    'customer_level' => CustomerModel::CUSTOMER_LEVEL_FIRST,
                    'card_no' => $cardNo,
                    'customer_name' => $customerName,
                    'customer_grade' => $customerGrade,
                    'contact_mobile' => $contactMobile,
                    'customer_name_en' => $customerNameEn,
                    'gender' => $gender,
                    'email' => $email,
                    'weixin' => $weixin,
                    'country' => $country,
                    'bank_card_no' => $bankCardNo,
                    'issuing_bank' => $issuingBank,
                    'source' => $source,
                    'client_manager' => $customerManager,
                    'province' => $province,
                    'city' => $city,
                    'address' => $contactAddress,
                    'related_company' => $relatedCompany,
                    'related_company_telephone' => $relatedCompanyTelephone,
                    'related_compnay_address' => $relatedCompnayAddress,
                    'remark' => $remark,
                    'status' => CustomerModel::STATUS_ENABLE,
                ];
                $insertCustomerId = $customerModel->insert($addCustomerData);
                //新增会员账户
                $customerAccountModel->insert([
                    'customer_id' => $insertCustomerId,
                    'cash_flag' => CustomerAccountModel::CASH_FLAG_UNOPEN,
                    'credit_flag' => CustomerAccountModel::CREDIT_FLAG_UNOPEN,
                    'temp_credit_flag' => CustomerAccountModel::TEMP_CREDIT_FLAG_UNOPEN,
                    'cash_balance' => 0.00,
                    'credit_line' => 0.00,
                    'credit_used' => 0.00,
                    'temp_credit_line' => 0.00,
                    'temp_credit_used' => 0.00,
                    'status' => CustomerAccountModel::STATUS_ENABLE,
                    'user_id' => 0 //TODO 需要获取对应的用户id
                ]);
                //新增会员证件
                $addCustomerIdcardData = [];
                $addCustomerIdcardData[] = [
                    'customer_id' => $insertCustomerId,
                    'card_type' => $cardType,
                    'card_no' => $IDCardNo,
                    'status' => CustomerIdcardModel::STATUS_ENABLE,
                ];
                if (!empty($cardType2) && !empty($IDCardNo2)) {
                    $addCustomerIdcardData[] = [
                        'customer_id' => $insertCustomerId,
                        'card_type' => $cardType2,
                        'card_no' => $IDCardNo2,
                        'status' => CustomerIdcardModel::STATUS_ENABLE,
                    ];
                }
                $customerIdcardModel->insertBatch($addCustomerIdcardData);
            }
            $db->transComplete();
        } catch (\Exception $e) {
            $db->transRollback();
            throw new \Exception($e->getMessage());
        }

        return true;
    }

    public function export($params)
    {
        $customerModel = model('CustomerModel');
        $customerAccountModel = model('CustomerAccountModel');
        $userModel = model('UserModel');
        $countryCodeModel = model('CountryCodeModel');
        $customerIdcardModel = model('CustomerIdcardModel');

        //1.数据
        $data = [
            [
                '序号',
                '会员卡号',
                '会员手机号',
                '会员名称',
                '会员等级',
                "所属类型",
                "上级会员",
                '预存金余额',
                '授信类型/可用额度',
                '会员状态',
                '操作员',
                '注册时间',
                '英文名称',
                '性别',
                '电子邮箱',
                '微信账号',
                '会员国籍',
                '证件类型',
                '证件号码',
                '证件类型',
                '证件号码',
                "公务卡号",
                "开卡银行",
                '扩展业务员',
                '会员来源',
                '客户经理',
                '所在省份',
                '所在城市',
                '联系地址',
                '所在公司',
                '公司电话',
                '公司地址',
                '备注'
            ],
        ];
        $customerIds = $params['customer_ids'];
        $customerIds = array_filter($customerIds);
        $customers = $customerModel->whereIn('id', $customerIds)->where('customer_type', CustomerModel::CUSTOMER_TYPE_OFFICIAL)->findAll();
        if (empty($customers)) {
            throw new \Exception('数据有误');
        }
        $customerAccounts = $customerAccountModel->whereIn('customer_id', $customerIds)->findAll();
        $customerAccounts = array_column($customerAccounts, null,'customer_id');
        //上级会员
        $parentIds = array_column($customers, 'parent_id');
        $customerParents = $customerModel->whereIn('id', $parentIds)->findAll();
        $customerParents = array_column($customerParents, null,'id');

        //用户
        $userIds = array_column($customers, 'bd_user_id');
        $users = $userModel->whereIn('id', $userIds)->findAll();
        $users = array_column($users, null,'id');
        //国家
        $codes = array_column($customers, 'country');
        $countryCodes = $countryCodeModel->whereIn('code', $codes)->findAll();
        $countryCodes = array_column($countryCodes, null,'code');
        //证件
        $idcards = [];
        $customerIdcards = $customerIdcardModel->whereIn('customer_id', $customerIds)->findAll();
        foreach ($customerIdcards as $customerIdcard) {
            $idcards[$customerIdcard['customer_id']][] = $customerIdcard;
        }
        $i = 1;
        foreach ($customers as $customer) {
            $customerId = $customer['id'];
            $credit_type_text = '';//授信类型
            $credit_balance = 0.00;//授信余额
            $cash_balance = 0.00;//预存金余额
            $customerAccount = $customerAccounts[$customerId];
            if (!empty($customerAccount)) {
                if ($customerAccount['credit_flag'] == CustomerAccountModel::CREDIT_FLAG_OPEN) {
                    $credit_type_text = '协议欠款';
                    $credit_balance = bcsub($customerAccount['credit_line'], $customerAccount['credit_used']);
                    $credit_type_text .= $credit_balance;
                } elseif ($customerAccount['credit_flag'] == CustomerAccountModel::CREDIT_FLAG_UNOPEN) {
                    $credit_type_text = '未开通';
                } elseif ($customerAccount['status'] == CustomerAccountModel::STATUS_DISABLE) {
                    $credit_type_text = '已冻结';
                }
                $cash_balance = $customerAccount['cash_balance'];
            }
            //操作员
            $userName = $users[$customer['user_id']]['name'] ?? '';
            //国家
            $countryName = $countryCodes[$customer['country']]['name'] ?? '';
            //证件
            $cardType = '';//证件类型
            $IDCardNo = '';//证件号码
            $cardType2 = '';//证件类型
            $IDCardNo2 = '';//证件号码
            $idcard = $idcards[$customerId] ?? [];
            if (!empty($idcard)) {
                $cardType = CustomerIdcardModel::cardType($idcard[0]['card_type']);
                $IDCardNo = $idcard[0]['card_no'];
                if (count($idcard) >= 2) {
                    $cardType2 = CustomerIdcardModel::cardType($idcard[1]['card_type']);
                    $IDCardNo2 = $idcard[1]['card_no'];
                }
            }
            //扩展业务员
            $bdUserName = $users[$customer['bd_user_id']]['name'] ?? '';
            $data[] = [
                $i,
                $customer['card_no'],
                $customer['contact_mobile'],
                $customer['customer_name'],
                CustomerModel::customerGrade($customer['customer_grade']),
                CustomerModel::customerLevel($customer['customer_level']),
                $customer['parent_id'] ? $customerParents[$customer['parent_id']]['customer_name'] : '',
                $cash_balance,//预存金余额
                $credit_type_text,
                CustomerModel::status($customer['status']),//会员状态
                $userName,//操作员
                date('Y-m-d H:i:s', $customer['created_at']),
                $customer['customer_name_en'],
                CustomerModel::gender($customer['gender']),
                $customer['email'],
                $customer['weixin'],
                $countryName,
                $cardType,
                $IDCardNo,
                $cardType2,
                $IDCardNo2,
                $customer['bank_card_no'],
                $customer['issuing_bank'],
                $bdUserName,
                CustomerModel::source($customer['source']),
                $customer['client_manager'],
                $customer['province'],
                $customer['city'],
                $customer['address'],
                $customer['related_company'],
                $customer['related_company_telephone'],
                $customer['related_compnay_address'],
                $customer['remark']
            ];
            $i++;
        }

        // 2. 创建Spreadsheet对象
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 3. 填充数据
        $sheet->fromArray($data, null, 'A1');

        // 4. 设置响应头
        $filename = 'export_'.date('YmdHis').'.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.$filename.'"');
        header('Cache-Control: max-age=0');

        // 5. 输出Excel文件
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }
}