<?php
namespace App\Services\Order;

use App\Models\PnrPassengerModel;
use App\Services\BaseService;
use App\Helpers\Tools;

class Order extends BaseService {
    public function __constructor(){}

    public function create_order($params)
    {
        $flight_model = model('FlightModel');
        $cabin_model = model('CabinModel');
        $pnr_model = model('PnrModel');
        $pnr_passenger_model = model('PnrPassengerModel');
        $pnr_price_model = model('PnrPriceModel');
        $ticket_book_order_model = model('TicketBookOrderModel');
        $ticket_book_order_detail_model = model('TicketBookOrderDetailModel');
        $ticket_book_pax_model = model('TicketBookPaxModel');
        $ticket_book_seg_model = model('TicketBookSegModel');
        $ticket_book_order_ticket_price_model = model('TicketBookOrderTicketPriceModel');

        //预订人姓名
        $contact_name = trim($params['contact_name']);
        //预订人联系电话
        $contact_telephone = trim($params['contact_telephone']);
        //行程类型
        $flight_type = intval($params['flight_type']);
        //乘客信息
        $passengers = $params['passengers'];
        //航段信息
        $flight_segments = $params['flight_segments'];
        //航段数量
        $flight_segment_count = count($flight_segments);
        switch ($flight_type) {
            case 1://单程
                if ($flight_segment_count != 1) {
                    error(0, '单程类型，只能有1个行程');
                }
                break;
            case 2://往返
            case 3://联程-两航段
                if ($flight_segment_count != 2) {
                    error(0, '往返/多程类型，必须有2个行程');
                }
                $first_departure_date = $flight_segments[0]['departure_date'];//第一行程出发时间
                $second_departure_date = $flight_segments[1]['departure_date'];//第二行程出发时间
                if ($first_departure_date > $second_departure_date) {
                    error(0, '第一行程出发时间不能大于第二行程出发时间');
                }
                break;
        }
        //校验预订人手机号
        if (!Tools\Mobile::validPhone($contact_telephone)) {
            error(0, "预订人手机号码有误_{$contact_telephone}");
        }
        //校验航班信息
        $flight_numbers = array_unique(array_column($flight_segments, 'flight_number'));
        $flights = $flight_model->whereIn('flight_number', $flight_numbers)->findAll();
        $flights = array_column($flights, null, 'flight_number');
        if (count($flight_numbers) != count($flights)) {
            error(0, '航班号有误,请检查');
        }
        //校验舱位信息
        $airline_codes = array_unique(array_column($flights, 'airline_code'));
        $cabin_list = $cabin_model->whereIn('airline', $airline_codes)->findAll();
        $cabinsArr = [];
        foreach ($cabin_list as $val) {
            $tmp_key = $val['airline'] . $val['cabin'];
            $cabinsArr[$tmp_key] = $val;
        }
        $flight_segment_arr = [];//航段信息
        $passenger_arr = [];//乘客信息
        $cabin_price_total = [];//舱位对应的各类型乘客票价总额（防止2个航道同一个舱位编号）

        //产品的key是多个航段的产品编号用+号进行拼接
        $cabin_key = implode('+', array_column($flight_segments,'product_no'));

        //航段信息组装
        $i = 1;
        foreach ($flight_segments as $fs) {
            $departure_date = $fs['departure_date'];
            $flight_number = $fs['flight_number'];
            $rph = $i;
            $cabin = $fs['cabin'];
            $product_no = $fs['product_no'];

            //校验舱位
            $airline_code = $flights[$flight_number]['airline_code'];
            $tmp_key = $airline_code . $cabin;
            if (!isset($cabinsArr[$tmp_key])) {
                error(0, "未知航司舱位{$tmp_key}");
            }
            $flight = $flights[$flight_number];
            //校验出发时间
            if ($departure_date < date('Y-m-d')) {
                error(0, "出发时间不允许小于当前日期");
            }
            //到达时间
            $arrival_date = $departure_date;//TODO:默认到达时间等于出发时间，如果到达时间小于出发时间默认加1天，后续要改成读取配置获取
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrival_date = strtotime($departure_date . ' +1 day');
                $arrival_date = date('Y-m-d', $arrival_date);
            }
            //第一个航段的起飞时间
            if (!isset($first_departure_datetime)) {
                $first_departure_datetime = $departure_date . ' ' . $flight['departure_time'] . ':00';
            }
            $flight_segment_arr[] = [
                'rph' => $rph,//航段编号
                'departure_datetime' => $departure_date . 'T' . $flight['departure_time'] . ':00', //出发时间
                'arrival_datetime' => $arrival_date . 'T' . $flight['arrival_time'] . ':00', //到达时间
                'code_share_ind' => false, // 是否共享航班
                'flight_number' => $flight_number, //航班号
                'status' => 'NN', // 行动代码
                'segment_type' => 'NORMAL', // 航段类型，默认为NORMAL
                'departure_airport' => $flight['departure_airport'], //出发机场
                'arrival_airport' => $flight['arrival_airport'], //到达机场
                'air_equip_type' => $flight['air_equip_type'], //机型
                'marketing_airline' => $flight['airline_code'],//航空公司字母代码
                'booking_class_avail' => $cabin, // 预订舱位
                'product_no' => $product_no
            ];
            //各舱位对应的各类型乘客票价总额
            if (isset($cabin_price_total[$cabin_key])) {
                $cabin_price_total[$cabin_key]['ADT'] += $fs['adult_price'];//成人价
                $cabin_price_total[$cabin_key]['CHD'] += $fs['children_price'];//儿童价
                $cabin_price_total[$cabin_key]['INF'] += $fs['baby_price'];//婴儿价
            } else {
                $cabin_price_total[$cabin_key]['ADT'] = $fs['adult_price'];//成人价
                $cabin_price_total[$cabin_key]['CHD'] = $fs['children_price'];//儿童价
                $cabin_price_total[$cabin_key]['INF'] = $fs['baby_price'];//婴儿价
            }
            $i++;
        }

        //乘客信息组装
        $inf_rphs = [];//婴儿rphs
        $i = 1;
        foreach ($passengers as $pg) {
            $rph = $i;
            $passenger_type_code = intval($pg['passenger_type_code']);//乘客类型：1成人 2儿童 3婴儿  对应接口：ADT成人 CHD儿童 INF婴儿
            $certificate_type = intval($pg['certificate_type']);//证件类型：1身份证 2护照  对应接口：NI 身份证 PP 护照
            $certificate_number = trim($pg['certificate_number']);//证件号码
            $contact_phone = trim($pg['contact_phone']);//乘客联系电话
            $infant_traveler_rph = 0;//婴儿关联的成人乘客ID
            $name = trim($pg['name']);//乘客姓名

            //验证手机号
            if (!Tools\Mobile::validPhone($contact_phone)) {
                error(0, "乘客{$name}手机号码有误有误_{$contact_phone}");
            }
            // 验证身份证
            if ($certificate_type != 1) {
                error(0, "国内机票暂时只支持身份证预定");
            }
            if (!Tools\Idcard::validateIdcard($certificate_number)) {
                error(0, "乘客{$name}身份证号码有误_{$certificate_number}");
            }
            $gender = Tools\Idcard::get_sex($certificate_number) == 1 ? 'MALE' : 'FEMALE';//1（男）或 0（女）

            //校验年龄
            $age = Tools\Idcard::get_age($certificate_number);
            switch ($passenger_type_code) {
                case 1://成人
                    if ($age < 18) {
                        error(0, "乘客序号:{$rph},乘客类型为成人和身份证年龄不符");
                    }
                    break;
                case 2://儿童
                    if (!($age >= 2 and $age <= 17)) {
                        error(0, "乘客序号:{$rph},乘客类型为儿童和身份证年龄不符");
                    }
                    break;
                case 3://婴儿
                    if (!($age < 2)) {
                        error(0, "乘客序号:{$rph},乘客类型为婴儿和身份证年龄不符");
                    }
                    $inf_rphs[] = $rph;
                    break;
            }

            $passenger_arr[] = [
                'rph' => $rph,
                'passenger_type_code' => PnrPassengerModel::PASSENGER_TYPE_MAP[$passenger_type_code], //乘客类型：ADT成人 CHD儿童 INF婴儿
                'gender' => $gender,
                'language_type' => 'ZH',
                'surname' => $name,
                'doc' => [//证件
                    'doc_type' => $certificate_type,
                    'doc_id' => $certificate_number
                ],
                'ctcm' => $contact_phone,//乘客联系电话
                'infant_traveler_rph' => $infant_traveler_rph, // 可选，关联婴儿乘客ID，如果没有关联，则可以提供该字段
                'tc' => ''//TC组，可选，可不填或留空
            ];
            $i++;
        }

        //设置携带者rph（成人绑定婴儿rph）
        foreach ($passenger_arr as $k => $p) {
            if ($p['passenger_type_code'] == 'ADT' && !empty($inf_rphs)) {
                $p['infant_traveler_rph'] = array_shift($inf_rphs);
            }
            $passenger_arr[$k] = $p;
        }

        //留座时间，默认为起飞前两小时，用起飞时间计算
        $subtract_hours = strtotime('-2 hours', strtotime($first_departure_datetime));
        $subtract_hours = date('Y-m-d H:i:s', $subtract_hours);
        $ticket_time_limit_db = $subtract_hours;
        $subtract_hours = explode(' ', $subtract_hours);
        $ticket_time_limit = $subtract_hours[0] . 'T' . $subtract_hours[1];

        $params = [
            'flight_segments' => $flight_segment_arr,//航段信息
            'passengers' => $passenger_arr,//乘客信息
            'airline' => $airline_code,// 航空公司CZ
            'ctct' => $contact_telephone,//预订人联系方式
            'ticket_time_limit' => $ticket_time_limit,//留座时间
            'tc' => '', // TC组，可选，可不填或留空
        ];

        $pricing = new \App\Libraries\Api\IBE\Pricing();
        $booking = new \App\Libraries\Api\IBE\Booking();

        //出票时限
        $count_passenger = count($passenger_arr);//旅客人数

        try {
            //创建PNR
            $pnr = $booking->create_pnr($params);
            if (!empty($pnr_model->where('pnr', $pnr)->first())) {
                error(0, 'PNR已存在，请勿重复生成');
            }

            // 开始数据库事务
            $this->db->transException(true)->transStart();

            //1.添加pnr
            $insert_pnr_id = $pnr_model->insert([
                'pnr'  => $pnr,
                'ticket_time_limit'  => $ticket_time_limit_db,
                'passenger_number' => $count_passenger,
                'contact_name' => $contact_name,
                'contact_telephone' => $contact_telephone
            ]);
            if (empty($insert_pnr_id)) {
                throw new \Exception("添加PNR表失败");
            }

            //2.根据PNR编号查询国内机票价格
            //几种乘客类型就调几次, 从返回值找到对应的产品进行比对价格
            $passenger_type_codes = array_unique(array_column($passenger_arr, 'passenger_type_code'));
            $store_prices = [];//各类型旅客全程的单价
            $fare_basis_codes = [];//接口返回的所有产品id
            foreach ($passenger_type_codes as $code) {
                $params = [
                    'passenger_type' => $code, // 旅客类型：ADT成人 CHD儿童 INF婴儿
                    'passenger_rph' => 0, // 旅客编号：0为查全部旅客的价格，1...9为查某个游客的价格        //TODO 此处是否需要给每个游客查询他对应的价格？？
                    'payment_type' => 'CASH', // 付款类型：CASH 现金 CREDIT_CARD 信用卡
                    'pnr' => $pnr // PNR
                ];
                $pnr_prices = $pricing->query_domestic_price_by_pnr($params);
                foreach ($pnr_prices as $p) {
                    $fare_basis_code = $p['fare_basis_code']; //产品编号
                    $fare_basis_codes[] = $fare_basis_code;
                    if (isset($cabin_price_total[$fare_basis_code])) {
                        $page_total_price = $cabin_price_total[$fare_basis_code][$code];
                        if ($page_total_price < $p['amount']) {
                            throw new \Exception("PNR_{$pnr},产品编号:{$fare_basis_code},乘客类型:{$code},页面价格小于接口返回的价格：页面价格:{$page_total_price},接口返回的价格:{$p['amount']}");
                        }
                        $store_prices[] = [
                            'passenger_type' => $code,
                            'passenger_rph' => 0,
                            'payment_type' => 'CASH',
                            'pnr' => $pnr,
                            'fare_ref_rph' => $p['rph'],
                            'price' => $p['amount'],//价格
                            'taxes' => $p['taxes'],//税费
                            'fare_basis_code' => $fare_basis_code,//产品编号
                        ];
                    }
                }
            }
            if (empty($store_prices)) {
                throw new \Exception('根据PNR编号查询国内机票价格失败, 提交上来的产品：' . implode(',',array_keys($cabin_price_total)) . '接口返回的产品：' . implode(',',$fare_basis_codes));
            }
            //3.把价格存储到PNR
            foreach ($store_prices as $sp) {
                $pricing->store_domestic_price_by_pnr($sp);
            }

            //4.添加机票订单表
            //航程(格式是多个航段的出发到达机场代码拼接，如CANWUH)
            $journey_info = '';
            foreach ($flight_segment_arr as $fs) {
                // TODO
                $journey_info .= $fs['departure_airport'] . $fs['arrival_airport'];
            }
            //旅客姓名(格式是旅客姓名用逗号拼接,如张三,李四)
            $passenger_names = implode(',',array_column($passenger_arr, 'surname'));
            //采购价总额(各类型乘客的价格总额)
            $total_supplier_price = 0;
            //总采购金额(采购价总额+税费总额)
            $total_supplier_amount = 0;
            //各税费总额
            $total_tax = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];
            $store_prices = array_column($store_prices, null, 'passenger_type');
            foreach ($passenger_arr as $pa) {
                $passenger_type_code = $pa['passenger_type_code'];
                $unit_price = $store_prices[$passenger_type_code]['price'];
                $taxes = $store_prices[$passenger_type_code]['taxes'];
                $total_supplier_price = bcadd($total_supplier_price, $unit_price, 2);
                foreach ($taxes as $key => $val) {
                    $total_tax[$key] = bcadd($total_tax[$key], $val['amount'], 2);
                    $total_supplier_amount = bcadd($total_supplier_amount, $val['amount'], 2);
                }
                $total_supplier_amount = bcadd($total_supplier_amount, $total_supplier_price, 2);
            }
            $order_no = $ticket_book_order_model->generate_order_no('T');//订单号
            $insert_order_id = $ticket_book_order_model->insert([
                'order_no'  => $order_no,
                'ticket_type'  => 1,
                'order_source'  => 1,
                'area_type'  => 1,
                'customer_type'  => 1,
                'customer_id'  => 0,//客户ID，包括直销会员和分销会员
                'pnr' => $pnr,
                'pnr_id' => $insert_pnr_id,
                'journey_type' => $flight_type,//航程类型
                'journey_info' => $journey_info,//航程
                'passenger_number' => $count_passenger,//旅客人数
                'passenger_names' => $passenger_names,//旅客姓名
                'contact_name' => $contact_name,//联系人
                'contact_telephone' => $contact_telephone,//联系电话
                'total_supplier_amount' => $total_supplier_amount,//总采购金额
                'total_customer_amount' => $total_supplier_amount,//总销售金额
                'office' => config('IBE')->office,//OFFICE号
                'status' => 1,//已订座
                'operator_id' => 1,//TODO 因登录前端还没接，先暂时写死
                'operator_name' => '测试账号'//TODO 因登录前端还没接，先暂时写死
            ]);
            if (empty($insert_order_id)) {
                throw new \Exception('添加订单失败');
            }

            //5.添加order_segments
            $add_order_segments = [];//订单航段表数据
            foreach ($flight_segment_arr as $fs) {
                //出发时间
                $departure_datetime = str_replace('T', ' ', $fs['departure_datetime']);
                //到达时间
                $arrival_datetime = str_replace('T', ' ', $fs['arrival_datetime']);
                //代码共享
                $code_share_ind = $fs['code_share_ind'] ? 1 : 0;

                $insert_order_segment_id = $ticket_book_seg_model->insert([
                    'order_id' => $insert_order_id,
                    'rph' => $fs['rph'],
                    'departure_datetime' => $departure_datetime,//出发时间
                    'arrival_datetime' => $arrival_datetime,//到达时间
                    'code_share_ind' => $code_share_ind,//代码共享
                    'airline' => $fs['marketing_airline'],//市场方航空公司
                    'flight_number' => $fs['flight_number'],//市场方航班号
                    'operating_airline' => $fs['marketing_airline'],//承运方航空公司
                    'operating_flight_number' => $fs['flight_number'],//承运方航班号
                    'cabin' => $fs['booking_class_avail'],//舱位
                    'sub_cabin' => $product_no,//产品
                    'fbc' => $product_no,//产品
                    'passenger_number' => $count_passenger,//旅客人数
                    'action_code' => $fs['status'],//行动代码
                    'segment_type' => $fs['segment_type'],//航段类型
                ]);
            }

            //6.添加order_passengers
            $add_order_passengers = [];//订单乘客表数据
            $add_order_detail = [];//订单明细表数据
            foreach ($passenger_arr as $p) {
                $insert_order_passenger_id = $ticket_book_pax_model->insert([
                    'order_id' => $insert_order_id,
                    'rph' => $p['rph'],
                    'passenger_type' => PnrPassengerModel::PASSENGER_TYPE_MAP_2[$p['passenger_type_code']],
                    'doc_type' => $p['doc']['doc_type'],
                    'doc_id' => $p['doc']['doc_id'],
                    'person_name' => $p['surname'],
                    'passenger_age' => Tools\Idcard::get_age($p['doc']['doc_id']),
                    'language_type' => 'ZH',
                    'telephone' => $p['ctcm'],
                    'status' => 0,
                    'gender' => PnrPassengerModel::GENDER_MAP[$p['gender']]
                ]);

                $add_order_detail[] = [
                    'order_passenger_id' => $insert_order_passenger_id,
                    'passenger_type' => PnrPassengerModel::PASSENGER_TYPE_MAP_2[$p['passenger_type_code']],
                ];
            }

            //7.添加订单明细表
            $add_order_ticket_price = [];//出票价格表数据
            foreach ($add_order_detail as $od) {
                $passenger_type = PnrPassengerModel::PASSENGER_TYPE_MAP[$od['passenger_type']];//旅客类型
                $order_passenger_id = $od['order_passenger_id'];//订单乘客ID
                foreach ($store_prices as $sp) {
                    if ($sp['passenger_type'] == $passenger_type) {
                        //采购总金额(采购价+税费)
                        $total_tax = 0;
                        $total_tax_detail = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];
                        foreach ($sp['taxes'] as $key => $val) {
                            $total_tax = bcadd($total_tax, $val['amount'], 2);
                            $total_tax_detail[$key] = $val['amount'];
                        }
                        $supplier_amount = bcadd($sp['price'], $total_tax, 2);//总采购金额
                        $insert_order_detail_id = $ticket_book_order_detail_model->insert([
                            'order_id' => $insert_order_id,
                            'order_passenger_id' => $order_passenger_id,
                            'product_type' => 1,
                            'product_id' => $insert_pnr_id,
                            'supplier_id' => 0,
                            'customer_id' => 0,
                            'supplier_amount' => $supplier_amount,//总采购金额
                            'customer_amount' => $supplier_amount,//总销售金额

                            'ticket_marketing_price' => $sp['price'],//市场价/账单价/票面价
                            'ticket_tax_cn' => $total_tax_detail['CN'],//机场建设费
                            'ticket_tax_yq' => $total_tax_detail['YQ'],//燃油附加费
                            'ticket_tax_xt' => $total_tax_detail['XT'],//其他税费
                            'ticket_total_price' => $supplier_amount,//票面总价
                            'ticket_supplier_price' => $sp['price'],//采购价
                            'origin_marketing_price' => $sp['price'],
                            'origin_tax_cn' => $total_tax_detail['CN'],
                            'origin_tax_yq' => $total_tax_detail['YQ'],
                            'origin_tax_xt' => $total_tax_detail['XT'],
                        ]);
                    }
                }
            }

            //8.回写PNR系列表的order_id
            $pnr_model->where('id', $insert_pnr_id)->set('order_id', $insert_order_id)->update();
            $this->db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $this->db->transRollback();
            error(0, $e->getMessage());
        }
        return $insert_order_id;
    }

    public function create_order_old($params)
    {
        $flight_model = model('FlightModel');
        $cabin_model = model('CabinModel');
        $pnr_model = model('PnrModel');
        $pnr_passenger_model = model('PnrPassengerModel');
        $pnr_segment_model = model('PnrSegmentModel');
        $pnr_price_model = model('PnrPriceModel');
        $ticket_book_order_model = model('TicketBookOrderModel');
        $ticket_book_order_detail_model = model('TicketBookOrderDetailModel');
        $ticket_book_pax_model = model('TicketBookPaxModel');
        $ticket_book_seg_model = model('TicketBookSegModel');
        $ticket_book_order_ticket_price_model = model('TicketBookOrderTicketPriceModel');

        //预订人姓名
        $contact_name = trim($params['contact_name']);
        //预订人联系电话
        $contact_telephone = trim($params['contact_telephone']);
        //行程类型
        $flight_type = intval($params['flight_type']);
        //乘客信息
        $passengers = $params['passengers'];
        //航段信息
        $flight_segments = $params['flight_segments'];
        //航段数量
        $flight_segment_count = count($flight_segments);
        switch ($flight_type) {
            case 1://单程
                if ($flight_segment_count != 1) {
                    error(0, '单程类型，只能有1个行程');
                }
                break;
            case 2://往返
            case 3://联程-两航段
                if ($flight_segment_count != 2) {
                    error(0, '往返/多程类型，必须有2个行程');
                }
                $first_departure_date = $flight_segments[0]['departure_date'];//第一行程出发时间
                $second_departure_date = $flight_segments[1]['departure_date'];//第二行程出发时间
                if ($first_departure_date > $second_departure_date) {
                    error(0, '第一行程出发时间不能大于第二行程出发时间');
                }
                break;
        }
        //校验预订人手机号
        if (!Tools\Mobile::validPhone($contact_telephone)) {
            error(0, "预订人手机号码有误有误_{$contact_telephone}");
        }
        //校验航班信息
        $flight_numbers = array_unique(array_column($flight_segments, 'flight_number'));
        $flights = $flight_model->whereIn('flight_number', $flight_numbers)->findAll();
        $flights = array_column($flights, null, 'flight_number');
        if (count($flight_numbers) != count($flights)) {
            error(0, '航班号有误,请检查');
        }
        //校验舱位信息
        $airline_codes = array_unique(array_column($flights, 'airline_code'));
        $cabin_list = $cabin_model->whereIn('airline', $airline_codes)->findAll();
        $cabinsArr = [];
        foreach ($cabin_list as $val) {
            $tmp_key = $val['airline'] . $val['cabin'];
            $cabinsArr[$tmp_key] = $val;
        }
        $flight_segment_arr = [];//航段信息
        $passenger_arr = [];//乘客信息
        $cabin_price_total = [];//舱位对应的各类型乘客票价总额（防止2个航道同一个舱位编号）

        //产品的key是多个航段的产品编号用+号进行拼接
        $cabin_key = implode('+', array_column($flight_segments,'product_no'));

        //航段信息组装
        $i = 1;
        foreach ($flight_segments as $fs) {
            $departure_date = $fs['departure_date'];
            $flight_number = $fs['flight_number'];
            $rph = $i;
            $cabin = $fs['cabin'];
            $product_no = $fs['product_no'];

            //校验舱位
            $airline_code = $flights[$flight_number]['airline_code'];
            $tmp_key = $airline_code . $cabin;
            if (!isset($cabinsArr[$tmp_key])) {
                error(0, "未知航司舱位{$tmp_key}");
            }
            $flight = $flights[$flight_number];
            //校验出发时间
            if ($departure_date < date('Y-m-d')) {
                error(0, "出发时间不允许小于当前日期");
            }
            //到达时间
            $arrival_date = $departure_date;//TODO:默认到达时间等于出发时间，如果到达时间小于出发时间默认加1天，后续要改成读取配置获取
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrival_date = strtotime($departure_date . ' +1 day');
                $arrival_date = date('Y-m-d', $arrival_date);
            }
            //第一个航段的起飞时间
            $first_departure_datetime = $departure_date . ' ' . $flight['departure_time'] . ':00';
            $flight_segment_arr[] = [
                'rph' => $rph,//航段编号
                'departure_datetime' => $departure_date . 'T' . $flight['departure_time'] . ':00', //出发时间
                'arrival_datetime' => $arrival_date . 'T' . $flight['arrival_time'] . ':00', //到达时间
                'code_share_ind' => false, // 是否共享航班
                'flight_number' => $flight_number, //航班号
                'status' => 'NN', // 行动代码
                'segment_type' => 'NORMAL', // 航段类型，默认为NORMAL
                'departure_airport' => $flight['departure_airport'], //出发机场
                'arrival_airport' => $flight['arrival_airport'], //到达机场
                'air_equip_type' => $flight['air_equip_type'], //机型
                'marketing_airline' => $flight['airline_code'],//航空公司字母代码
                'booking_class_avail' => $cabin, // 预订舱位
                'product_no' => $product_no
            ];
            //各舱位对应的各类型乘客票价总额
            if (isset($cabin_price_total[$cabin_key])) {
                $cabin_price_total[$cabin_key]['ADT'] += $fs['adult_price'];//成人价
                $cabin_price_total[$cabin_key]['CHD'] += $fs['children_price'];//儿童价
                $cabin_price_total[$cabin_key]['INF'] += $fs['baby_price'];//婴儿价
            } else {
                $cabin_price_total[$cabin_key]['ADT'] = $fs['adult_price'];//成人价
                $cabin_price_total[$cabin_key]['CHD'] = $fs['children_price'];//儿童价
                $cabin_price_total[$cabin_key]['INF'] = $fs['baby_price'];//婴儿价
            }
            $i++;
        }

        //乘客信息组装
        $inf_rphs = [];//婴儿rphs
        $i = 1;
        foreach ($passengers as $pg) {
            $rph = $i;
            $passenger_type_code = intval($pg['passenger_type_code']);//乘客类型：1成人 2儿童 3婴儿  对应接口：ADT成人 CHD儿童 INF婴儿
            $certificate_type = intval($pg['certificate_type']);//证件类型：1身份证 2护照  对应接口：NI 身份证 PP 护照
            $certificate_number = trim($pg['certificate_number']);//证件号码
            $contact_phone = trim($pg['contact_phone']);//乘客联系电话
            $infant_traveler_rph = 0;//婴儿关联的成人乘客ID
            $name = trim($pg['name']);//乘客姓名

            //验证手机号
            if (!Tools\Mobile::validPhone($contact_phone)) {
                error(0, "乘客{$name}手机号码有误有误_{$contact_phone}");
            }
            // 验证身份证
            if ($certificate_type != 1) {
                error(0, "国内机票暂时只支持身份证预定");
            }
            if (!Tools\Idcard::validateIdcard($certificate_number)) {
                error(0, "乘客{$name}身份证号码有误_{$certificate_number}");
            }
            $gender = Tools\Idcard::get_sex($certificate_number) == 1 ? 'MALE' : 'FEMALE';//1（男）或 0（女）

            //校验年龄
            $age = Tools\Idcard::get_age($certificate_number);
            switch ($passenger_type_code) {
                case 1://成人
                    if ($age < 18) {
                        error(0, "乘客序号:{$rph},乘客类型为成人和身份证年龄不符");
                    }
                    break;
                case 2://儿童
                    if (!($age >= 2 and $age <= 17)) {
                        error(0, "乘客序号:{$rph},乘客类型为儿童和身份证年龄不符");
                    }
                    break;
                case 3://婴儿
                    if (!($age < 2)) {
                        error(0, "乘客序号:{$rph},乘客类型为婴儿和身份证年龄不符");
                    }
                    $inf_rphs[] = $rph;
                    break;
            }

            $passenger_arr[] = [
                'rph' => $rph,
                'passenger_type_code' => PnrPassengerModel::PASSENGER_TYPE_MAP[$passenger_type_code], //乘客类型：ADT成人 CHD儿童 INF婴儿
                'gender' => $gender,
                'language_type' => 'ZH',
                'surname' => $name,
                'doc' => [//证件
                    'doc_type' => $certificate_type,
                    'doc_id' => $certificate_number
                ],
                'ctcm' => $contact_phone,//乘客联系电话
                'infant_traveler_rph' => $infant_traveler_rph, // 可选，关联婴儿乘客ID，如果没有关联，则可以提供该字段
                'tc' => ''//TC组，可选，可不填或留空
            ];
            $i++;
        }

        //设置携带者rph（成人绑定婴儿rph）
        foreach ($passenger_arr as $k => $p) {
            if ($p['passenger_type_code'] == 'ADT' && !empty($inf_rphs)) {
                $p['infant_traveler_rph'] = array_shift($inf_rphs);
            }
            $passenger_arr[$k] = $p;
        }

        //留座时间，默认为起飞前两小时，用起飞时间计算
        $subtract_hours = strtotime('-2 hours', strtotime($first_departure_datetime));
        $subtract_hours = date('Y-m-d H:i:s', $subtract_hours);
        $subtract_hours = explode(' ', $subtract_hours);
        $ticket_time_limit = $subtract_hours[0] . 'T' . $subtract_hours[1];

        $params = [
            'flight_segments' => $flight_segment_arr,//航段信息
            'passengers' => $passenger_arr,//乘客信息
            'airline' => $airline_code,// 航空公司CZ
            'ctct' => $contact_telephone,//预订人联系方式
            'ticket_time_limit' => $ticket_time_limit,//留座时间
            'tc' => '', // TC组，可选，可不填或留空
        ];

        $pricing = new \App\Libraries\Api\IBE\Pricing();
        $booking = new \App\Libraries\Api\IBE\Booking();

        //出票时限
        $ticket_time_limits = explode('T',$ticket_time_limit);
        $ticket_time_limit = $ticket_time_limits[0] . ' ' . $ticket_time_limits[1];
        $count_passenger = count($passenger_arr);//旅客人数

        try {
            //创建PNR
            $pnr = $booking->create_pnr($params);
            if (!empty($pnr_model->where('pnr', $pnr)->first())) {
                error(0, 'PNR已存在，请勿重复生成');
            }
            //1.添加pnr
            $insert_pnr_id = $pnr_model->insert([
                'order_id'  => 0,
                'pnr'  => $pnr,
                'origin_pnr'  => '',
                'ticket_time_limit'  => $ticket_time_limit,
                'ticket_time_limit_remark'  => '',
                'is_issued'  => 0,
                'is_split'  => 0,
                'passenger_number' => $count_passenger,
                'status' => 0,
                'contact_name' => $contact_name,
                'contact_telephone' => $contact_telephone
            ]);
            if (empty($insert_pnr_id)) {
                error(0, '添加PNR表失败');
            }
            $db = \Config\Database::connect();
            $db->transStart();

            //2.添加pnr_segments
            $add_order_segments = [];//订单航段表数据
            foreach ($flight_segment_arr as $fs) {
                //出发时间
                $departure_datetimes = explode('T',$fs['departure_datetime']);
                $departure_datetime = $departure_datetimes[0] . ' ' . $departure_datetimes[1];
                //到达时间
                $arrival_datetimes = explode('T',$fs['arrival_datetime']);
                $arrival_datetime = $arrival_datetimes[0] . ' ' . $arrival_datetimes[1];
                //代码共享
                $code_share_ind = $fs['code_share_ind'] ? 1 : 0;

                $insert_pnr_segment_id = $pnr_segment_model->insert([
                    'order_id' => 0,
                    'pnr_id' => $insert_pnr_id,
                    'rph' => $fs['rph'],
                    'departure_datetime' => $departure_datetime,//出发时间
                    'arrival_datetime' => $arrival_datetime,//到达时间
                    'code_share_ind' => $code_share_ind,//代码共享
                    'airline' => $fs['marketing_airline'],//市场方航空公司
                    'flight_number' => $fs['flight_number'],//市场方航班号
                    'operating_airline' => $fs['marketing_airline'],//承运方航空公司
                    'operating_flight_number' => $fs['flight_number'],//承运方航班号
                    'cabin' => $fs['booking_class_avail'],//舱位
                    'sub_cabin' => $product_no,//产品
                    'passenger_number' => $count_passenger,//旅客人数
                    'status' => $fs['status'],//行动代码
                    'flag' => 0,
                    'segment_type' => $fs['segment_type'],//航段类型
                ]);
                $add_order_segments[] = [
                    'order_id' => 0,
                    'departure_datetime' => $departure_datetime,//出发时间
                    'arrival_datetime' => $arrival_datetime,//到达时间
                    'code_share_ind' => $code_share_ind,//代码共享
                    'airline' => $fs['marketing_airline'],//市场方航空公司
                    'flight_number' => $fs['flight_number'],//市场方航班号
                    'operating_airline' => $fs['marketing_airline'],//承运方航空公司
                    'operating_flight_number' => $fs['flight_number'],//承运方航班号
                    'cabin' => $fs['booking_class_avail'],//舱位
                    'sub_cabin' => $product_no,//产品
                    'passenger_number' => $count_passenger,//旅客人数
                    'action_code' => $fs['status'],//行动代码
                    'pnr_segment_id' => $insert_pnr_segment_id
                ];
            }
            //3.添加pnr_passengers
            $add_order_passengers = [];//订单乘客表数据
            foreach ($passenger_arr as $p) {
                $insert_pnr_passenger_id = $pnr_passenger_model->insert([
                    'order_id' => 0,
                    'pnr' => $pnr,
                    'pnr_id' => $insert_pnr_id,
                    'rph' => $p['rph'],
                    'passenger_type' => PnrPassengerModel::PASSENGER_TYPE_MAP_2[$p['passenger_type_code']],
                    'doc_type' => $p['doc']['doc_type'],
                    'doc_id' => $p['doc']['doc_id'],
                    'person_name' => $p['surname'],
                    'passenger_age' => Tools\Idcard::get_age($p['doc']['doc_id']),
                    'language_type' => 'ZH',
                    'telephone' => $p['ctcm'],
                    'status' => 0,
                    'flag' => 1,
                    'gender' => PnrPassengerModel::GENDER_MAP[$p['gender']]
                ]);
                $add_order_passengers[] = [
                    'order_id' => 0,
                    'pnr_passenger_id' => $insert_pnr_passenger_id,
                    'passenger_type' => PnrPassengerModel::PASSENGER_TYPE_MAP_2[$p['passenger_type_code']],
                    'doc_type' => $p['doc']['doc_type'],
                    'doc_id' => $p['doc']['doc_id'],
                    'person_name' => $p['surname'],
                    'passenger_age' => Tools\Idcard::get_age($p['doc']['doc_id']),
                    'language_type' => 'ZH',
                    'gender' => PnrPassengerModel::GENDER_MAP[$p['gender']],
                    'telephone' => $p['ctcm'],
                    'status' => 0,
                ];
            }
            //4.根据PNR编号查询国内机票价格
            //几种乘客类型就调几次, 从返回值找到对应的产品进行比对价格
            $passenger_type_codes = array_unique(array_column($passenger_arr, 'passenger_type_code'));
            $store_prices = [];//各类型旅客全程的单价
            $fare_basis_codes = [];//接口返回的所有产品id
            foreach ($passenger_type_codes as $code) {
                $params = [
                    'passenger_type' => $code, // 旅客类型：ADT成人 CHD儿童 INF婴儿
                    'passenger_rph' => 0, // 旅客编号：0为查全部旅客的价格，1...9为查某个游客的价格        //TODO 此处是否需要给每个游客查询他对应的价格？？
                    'payment_type' => 'CASH', // 付款类型：CASH 现金 CREDIT_CARD 信用卡
                    'pnr' => $pnr // PNR
                ];
                $pnr_prices = $pricing->query_domestic_price_by_pnr($params);
                foreach ($pnr_prices as $p) {
                    $fare_basis_code = $p['fare_basis_code']; //产品编号
                    $fare_basis_codes[] = $fare_basis_code;
                    if (isset($cabin_price_total[$fare_basis_code])) {
                        $page_total_price = $cabin_price_total[$fare_basis_code][$code];
                        if ($page_total_price < $p['amount']) {
                            error(0, "PNR_{$pnr},产品编号:{$fare_basis_code},乘客类型:{$code},页面价格小于接口返回的价格：页面价格:{$page_total_price},接口返回的价格:{$p['amount']}");
                        }
                        $store_prices[] = [
                            'passenger_type' => $code,
                            'passenger_rph' => 0,
                            'payment_type' => 'CASH',
                            'pnr' => $pnr,
                            'fare_ref_rph' => $p['rph'],
                            'price' => $p['amount'],//价格
                            'taxes' => $p['taxes'],//税费
                            'fare_basis_code' => $fare_basis_code,//产品编号
                        ];
                    }
                }
            }
            if (empty($store_prices)) {
                error(0, '根据PNR编号查询国内机票价格失败, 提交上来的产品：' . implode(',',array_keys($cabin_price_total)) . '接口返回的产品：' . implode(',',$fare_basis_codes));
            }
            //5.把价格存储到PNR
            foreach ($store_prices as $sp) {
                $pricing->store_domestic_price_by_pnr($sp);
            }
            //6.添加PNR-价格表
            foreach ($store_prices as $sp) {
                $pnr_price_model->insert([
                    'pnr_id' => $insert_pnr_id,
                    'passenger_type' => PnrPassengerModel::PASSENGER_TYPE_MAP_2[$sp['passenger_type']],
                    'price' => $sp['price'],
                    'tax_cn' => $sp['taxes']['CN']['amount'] ?? 0.00,
                    'tax_yq' => $sp['taxes']['YQ']['amount'] ?? 0.00,
                    'tax_xt' => $sp['taxes']['XT']['amount'] ?? 0.00,
                    'tax_content' => json_encode($sp['taxes']),
                ]);
            }
            //7.添加机票订单表
            //航程(格式是多个航段的出发到达机场代码拼接，如CANWUH)
            $journey_info = '';
            foreach ($flight_segment_arr as $fs) {
                $journey_info .= $fs['departure_airport'] . $fs['arrival_airport'];
            }
            //旅客姓名(格式是旅客姓名用逗号拼接,如张三,李四)
            $passenger_names = implode(',',array_column($passenger_arr, 'surname'));
            //采购价总额(各类型乘客的价格总额)
            $total_supplier_price = 0;
            //总采购金额(采购价总额+税费总额)
            $total_supplier_amount = 0;
            //各税费总额
            $total_tax = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];
            $store_prices = array_column($store_prices, null, 'passenger_type');
            foreach ($passenger_arr as $pa) {
                $passenger_type_code = $pa['passenger_type_code'];
                $unit_price = $store_prices[$passenger_type_code]['price'];
                $taxes = $store_prices[$passenger_type_code]['taxes'];
                $total_supplier_price = bcadd($total_supplier_price, $unit_price, 2);
                foreach ($taxes as $key => $val) {
                    $total_tax[$key] = bcadd($total_tax[$key], $val['amount'], 2);
                    $total_supplier_amount = bcadd($total_supplier_amount, $val['amount'], 2);
                }
                $total_supplier_amount = bcadd($total_supplier_amount, $total_supplier_price, 2);
            }
            $order_no = $ticket_book_order_model->generate_order_no('T');//订单号
            $insert_order_id = $ticket_book_order_model->insert([
                'order_no'  => $order_no,
                'order_type'  => 1,
                'ticket_type'  => 1,
                'order_source'  => 1,
                'area_type'  => 1,
                'customer_type'  => 1,
                'customer_id'  => 0,//客户ID，包括直销会员和分销会员
                'pnr' => $pnr,
                'pnr_id' => $insert_pnr_id,
                'journey_type' => $flight_type,//航程类型
                'journey_info' => $journey_info,//航程
                'passenger_number' => $count_passenger,//旅客人数
                'passenger_names' => $passenger_names,//旅客姓名
                'contact_name' => $contact_name,//联系人
                'contact_telephone' => $contact_telephone,//联系电话
                'total_supplier_amount' => $total_supplier_amount,//总采购金额
                'total_customer_amount' => $total_supplier_amount,//总销售金额
                'office' => config('IBE')->office,//OFFICE号
                'status' => 1,//已订座
                'operator_id' => 1,//TODO 因登录前端还没接，先暂时写死
                'operator_name' => '测试账号'//TODO 因登录前端还没接，先暂时写死
            ]);
            if (empty($insert_order_id)) {
                error(0, '添加订单失败');
            }
            //8.添加订单航段表
            foreach ($add_order_segments as $os) {
                $os['order_id'] = $insert_order_id;
                $ticket_book_seg_model->insert($os);
            }
            //9.添加订单乘客表
            $add_order_detail = [];//订单明细表数据
            foreach ($add_order_passengers as $op) {
                $op['order_id'] = $insert_order_id;
                $insert_order_passenger_id = $ticket_book_pax_model->insert($op);
                $add_order_detail[] = [
                    'order_passenger_id' => $insert_order_passenger_id,
                    'passenger_type' => $op['passenger_type']
                ];
            }
            //10.添加订单明细表
            $add_order_ticket_price = [];//出票价格表数据
            foreach ($add_order_detail as $od) {
                $passenger_type = PnrPassengerModel::PASSENGER_TYPE_MAP[$od['passenger_type']];//旅客类型
                $order_passenger_id = $od['order_passenger_id'];//订单乘客ID
                foreach ($store_prices as $sp) {
                    if ($sp['passenger_type'] == $passenger_type) {
                        //采购总金额(采购价+税费)
                        $total_tax = 0;
                        $total_tax_detail = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];
                        foreach ($sp['taxes'] as $key => $val) {
                            $total_tax = bcadd($total_tax, $val['amount'], 2);
                            $total_tax_detail[$key] = $val['amount'];
                        }
                        $supplier_amount = bcadd($sp['price'], $total_tax, 2);//总采购金额
                        $insert_order_detail_id = $ticket_book_order_detail_model->insert([
                            'order_id' => $insert_order_id,
                            'order_passenger_id' => $order_passenger_id,
                            'product_type' => 1,
                            'product_id' => $insert_pnr_id,
                            'supplier_id' => 0,
                            'customer_id' => 0,
                            'supplier_amount' => $supplier_amount,//总采购金额
                            'customer_amount' => $supplier_amount,//总销售金额
                        ]);
                        $add_order_ticket_price[] = [
                            'order_detail_id' => $insert_order_detail_id,
                            'marketing_price' => $sp['price'],//市场价/账单价/票面价
                            'tax_cn' => $total_tax_detail['CN'],//机场建设费
                            'tax_yq' => $total_tax_detail['YQ'],//燃油附加费
                            'tax_xt' => $total_tax_detail['XT'],//其他税费
                            'total_price' => $supplier_amount,//票面总价
                            'supplier_price' => $sp['price'],//采购价
                            'supplier_amount' => $supplier_amount,//总采购金额
                            'customer_amount' => $supplier_amount,//总销售金额
                            'origin_marketing_price' => $sp['price'],
                            'origin_tax_cn' => $total_tax_detail['CN'],
                            'origin_tax_yq' => $total_tax_detail['YQ'],
                            'origin_tax_xt' => $total_tax_detail['XT'],
                        ];
                    }
                }
            }
            //11.添加出票价格表
            foreach ($add_order_ticket_price as $ap) {
                $ticket_book_order_ticket_price_model->insert($ap);
            }
            //12.回写PNR系列表的order_id
            $pnr_model->where('id', $insert_pnr_id)->set('order_id', $insert_order_id)->update();
            $pnr_passenger_model->where('pnr_id', $insert_pnr_id)->set('order_id', $insert_order_id)->update();
            $pnr_segment_model->where('pnr_id', $insert_pnr_id)->set('order_id', $insert_order_id)->update();
            $db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $db->transRollback();
            error(0, $e->getMessage());
        }
        return $insert_order_id;
    }

    public function domestic_book_order_list($params)
    {
        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 10;
        $pnr = trim($params['pnr']);
        $ticket_number = trim($params['ticket_number']);
        $person_name = trim($params['person_name']);
        $order_status = $params['order_status'] ?? '';
        $journey_type = intval($params['journey_type']);
        $order_no = trim($params['order_no']);
        $airline = trim($params['airline']);
        $flight_number = trim($params['flight_number']);
        $customer_type = intval($params['customer_type']);
        $order_source = intval($params['order_source']);
        $operator_name = trim($params['operator_name']);
        $order_date = isset($params['order_date']) && $params['order_date'] ? $params['order_date'] : [];
        $departure_date = isset($params['departure_date']) && $params['departure_date'] ? $params['departure_date'] : [];

        $data = [
            'list' => [],
            'total_status' => [],
            'total' => 0,
            'perPage' => 0,
            'pageCount' => 0,
            'currentPage' => 0,
        ];
        $order_model = model('TicketBookOrderModel');
        $order_passenger_model = model('TicketBookPaxModel');
        $pnr_segment_model = model('PnrSegmentModel');

        $order_passenger_where = [];
        $pnr_segment_where = [];
        $order_where = [];
        $order_where['area_type'] = 1;
        if ($pnr) {
            $order_where['pnr'] = $pnr;
        }
        if ($order_status !== '') {
            $order_where['status'] = $order_status;
        }
        if ($journey_type) {
            $order_where['journey_type'] = $journey_type;
        }
        if ($order_no) {
            $order_where['order_no'] = $order_no;
        }
        if ($customer_type) {
            $order_where['customer_type'] = $customer_type;
        }
        if ($order_source) {
            $order_where['order_source'] = $order_source;
        }
        if ($operator_name) {
            $order_where['operator_name'] = $operator_name;
        }
        if ($order_date) {
            $order_where['created_at_from'] = $order_date[0];
            $order_where['created_at_to'] = $order_date[1];
        }
        if ($ticket_number) {
            $order_passenger_where['ticket_number'] = $ticket_number;
        }
        if ($person_name) {
            $order_passenger_where['person_name'] = $person_name;
        }
        if ($airline) {
            $pnr_segment_where['airline'] = $airline;
        }
        if ($flight_number) {
            $pnr_segment_where['flight_number'] = $flight_number;
        }
        if ($departure_date) {
            $pnr_segment_where['departure_datetime >='] = $departure_date[0];
            $pnr_segment_where['departure_datetime <='] = $departure_date[1];
        }
        $order_where['ids'] = [];
        if (!empty($order_passenger_where)) {
            $order_passengers = $order_passenger_model->where($order_passenger_where)->findAll();
            if (empty($order_passengers)) {
                success('成功', $data);
            }
            $order_where['ids'] = array_column($order_passengers, 'order_id');
        }
        if (!empty($pnr_segment_where)) {
            foreach ($pnr_segment_where as $k => $v) {
                $pnr_segment_model->where($k, $v);
            }
            $pnr_segments = $pnr_segment_model->findAll();
            if (empty($pnr_segments)) {
                success('成功', $data);
            }
            $order_where['ids'] = array_merge($order_where['ids'], array_column($pnr_segments, 'order_id'));
        }

        //分页
        $list = $order_model->paginate_list($order_where, $page, $perPage);
        $pager = $order_model->pager;
        //分组
        $total_status = $order_model->total_status($order_where);
        $data = [
            'list' => $list,
            'total_status' => $total_status,
            'total' => $pager->getTotal(),//总条数
            'perPage' => $pager->getPerPage(),//每页显示条数
            'pageCount' => $pager->getPageCount(),//总页数
            'currentPage' => $pager->getCurrentPage(),//当前页数
        ];
        return $data;
    }

    public function domestic_rebook_order_list($params) {
        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 10;
        $pnr = trim($params['pnr']);
        $order_no = trim($params['order_no']);
        $person_name = trim($params['person_name']);
        $order_status = $params['order_status'] ?? '';
        $origin_order_no = trim($params['origin_order_no']);
        $airline = trim($params['airline']);
        $operator_name = trim($params['operator_name']);
        $order_date = $params['order_date'];

        $data = [
            'list' => [],
            'total_status' => [],
            'total' => 0,
            'perPage' => 0,
            'pageCount' => 0,
            'currentPage' => 0,
        ];
        $order_model = model('TicketRebookOrderModel');
        $order_passenger_model = model('TicketRebookPaxModel');
        $order_segment_model = model('TicketRebookSegModel');

        $order_passenger_where = [];
        $pnr_segment_where = [];
        $order_where = [];
        if ($pnr) {
            $order_where['pnr'] = $pnr;
        }
        if ($order_status !== '') {
            $order_where['status'] = $order_status;
        }
        if ($order_no) {
            $order_where['order_no'] = $order_no;
        }
        if ($origin_order_no) {
            $order_where['origin_order_no'] = $origin_order_no;
        }
        if ($operator_name) {
            $order_where['operator_name'] = $operator_name;
        }
        if (!empty($order_date[0])) {
            $order_where['created_at_from'] = $order_date[0];
        }
        if (!empty($order_date[1])) {
            $order_where['created_at_to'] = $order_date[1];
        }
        if ($person_name) {
            $order_passenger_where['person_name'] = $person_name;
        }
        if ($airline) {
            $order_segment_where['airline'] = $airline;
        }
        $order_where['ids'] = [];
        if (!empty($order_passenger_where)) {
            $order_passengers = $order_passenger_model->where($order_passenger_where)->findAll();
            if (empty($order_passengers)) {
                success('成功', $data);
            }
            $order_where['ids'] = array_column($order_passengers, 'order_id');
        }
        if (!empty($order_segment_where)) {
            foreach ($order_segment_where as $k => $v) {
                $order_segment_model->where($k, $v);
            }
            $order_segments = $order_segment_model->findAll();
            if (empty($order_segments)) {
                success('成功', $data);
            }
            $order_where['ids'] = array_merge($order_where['ids'], array_column($order_segments, 'order_id'));
        }

        //分页
        $list = $order_model->paginate_list($order_where, $page, $perPage);
        $pager = $order_model->pager;
        //分组
        $total_status = $order_model->total_status($order_where);
        $data = [
            'list' => $list,
            'total_status' => $total_status,
            'total' => $pager->getTotal(),//总条数
            'perPage' => $pager->getPerPage(),//每页显示条数
            'pageCount' => $pager->getPageCount(),//总页数
            'currentPage' => $pager->getCurrentPage(),//当前页数
        ];

        return $data;
    }

    public function domestic_refund_order_list($params) {
        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 10;
        $pnr = trim($params['pnr']);
        $order_no = trim($params['order_no']);
        $person_name = trim($params['person_name']);
        $order_status = $params['order_status'] ?? '';
        $origin_order_no = trim($params['origin_order_no']);
        $airline = trim($params['airline']);
        $operator_name = trim($params['operator_name']);
        $order_date = $params['order_date'];

        $data = [
            'list' => [],
            'total_status' => [],
            'total' => 0,
            'perPage' => 0,
            'pageCount' => 0,
            'currentPage' => 0,
        ];
        $order_model = model('TicketRefundOrderModel');
        $order_passenger_model = model('TicketRefundPaxModel');
        $order_segment_model = model('TicketRefundSegModel');

        $order_passenger_where = [];
        $pnr_segment_where = [];
        $order_where = [];
        if ($pnr) {
            $order_where['pnr'] = $pnr;
        }
        if ($order_status !== '') {
            $order_where['status'] = $order_status;
        }
        if ($order_no) {
            $order_where['order_no'] = $order_no;
        }
        if ($origin_order_no) {
            $order_where['origin_order_no'] = $origin_order_no;
        }
        if ($operator_name) {
            $order_where['operator_name'] = $operator_name;
        }
        if (!empty($order_date[0])) {
            $order_where['created_at_from'] = $order_date[0];
        }
        if (!empty($order_date[1])) {
            $order_where['created_at_to'] = $order_date[1];
        }
        if ($person_name) {
            $order_passenger_where['person_name'] = $person_name;
        }
        if ($airline) {
            $order_segment_where['airline'] = $airline;
        }
        $order_where['ids'] = [];
        if (!empty($order_passenger_where)) {
            $order_passengers = $order_passenger_model->where($order_passenger_where)->findAll();
            if (empty($order_passengers)) {
                success('成功', $data);
            }
            $order_where['ids'] = array_column($order_passengers, 'order_id');
        }
        if (!empty($order_segment_where)) {
            foreach ($order_segment_where as $k => $v) {
                $order_segment_model->where($k, $v);
            }
            $order_segments = $order_segment_model->findAll();
            if (empty($order_segments)) {
                success('成功', $data);
            }
            $order_where['ids'] = array_merge($order_where['ids'], array_column($order_segments, 'order_id'));
        }

        //分页
        $list = $order_model->paginate_list($order_where, $page, $perPage);
        $pager = $order_model->pager;
        //分组
        $total_status = $order_model->total_status($order_where);
        $data = [
            'list' => $list,
            'total_status' => $total_status,
            'total' => $pager->getTotal(),//总条数
            'perPage' => $pager->getPerPage(),//每页显示条数
            'pageCount' => $pager->getPageCount(),//总页数
            'currentPage' => $pager->getCurrentPage(),//当前页数
        ];

        return $data;
    }

    public function saveDomesticBookPrice($params)
    {
        $orderModel             = model('TicketBookOrderModel');
        $orderPassengerModel    = model('TicketBookPaxModel');
        $orderDetailModel       = model('TicketBookOrderDetailModel');

        // 获取订单信息
        $orderId   = intval($params['order_id']);
        if (empty($orderId)) {
            error(0, 'order_id错误');
        }
        $order = $orderModel->find($orderId);
        if (empty($order)) {
            error(0, 'order_id错误');
        }

        // 获取乘客信息
        $inputPassengers = $params['passengers'];
        $orderPassengers = $orderPassengerModel->where('order_id', $orderId)->findAll();
        $inputPassengerIds = array_column($params['passengers'], 'passenger_id');
        $dbPassengerIds = array_column($orderPassengers, 'id');
        // 比较输入的passenger_id和数据库的passenger_id，确保提交的数据是有效的
        if (!empty(array_diff($inputPassengerIds, $dbPassengerIds)) || !empty(array_diff($dbPassengerIds, $inputPassengerIds))) {
            error(0, '数据错误');
        }

        try {
            $this->db->transException(true)->transStart();

            // 更新order_detail
            $orderDetails = $orderDetailModel->where('order_id', $orderId)->findAll();
            $orderDetails = array_column($orderDetails, null, 'order_passenger_id');
            $total_supplier_amount = 0;
            $total_customer_amount = 0;
            foreach ($inputPassengers as $inputPassenger) {
                $passenger_id = $inputPassenger['passenger_id'];
                $is_free = $inputPassenger['is_free'];
                $ticket_marketing_price = $inputPassenger['ticket_marketing_price'];
                $ticket_supplier_agency_fee = $inputPassenger['ticket_supplier_agency_fee'];
                $ticket_supplier_service_fee = $inputPassenger['ticket_supplier_service_fee'];
                $ticket_customer_adjust_fee = $inputPassenger['ticket_customer_adjust_fee'];
                $ticket_customer_service_fee = $inputPassenger['ticket_customer_service_fee'];
                $ticket_tax_cn = $orderDetails[$passenger_id]['ticket_tax_cn'];
                $ticket_tax_yq = $orderDetails[$passenger_id]['ticket_tax_yq'];

                $ticket_total_price = bcadd(
                                            bcadd(
                                                $ticket_marketing_price,
                                                $ticket_tax_cn,
                                                2
                                            ),
                                            $ticket_tax_yq,
                                            2
                                        );
                $ticket_supplier_agency_fee_rate = bcmul(bcdiv($ticket_marketing_price, $ticket_supplier_agency_fee, 4), 100, 2);
                $supplier_amount = bcadd(
                        bcsub(
                            $ticket_total_price,
                            $ticket_supplier_agency_fee,
                            2,
                        ),
                        $ticket_supplier_service_fee,
                        2
                    );
                $customer_amount = bcadd(
                        bcadd(
                            $ticket_total_price,
                            $ticket_customer_adjust_fee,
                            2
                        ),
                        $ticket_customer_service_fee,
                        2,
                    );
                $total_supplier_amount += $supplier_amount;
                $total_customer_amount += $customer_amount;
                $order_detail_arr = [
                    'is_free' => $is_free,
                    'ticket_marketing_price' => $ticket_marketing_price,
                    'ticket_supplier_agency_fee' => $ticket_supplier_agency_fee,
                    'ticket_supplier_service_fee' => $ticket_supplier_service_fee,
                    'ticket_customer_adjust_fee' => $ticket_customer_adjust_fee,
                    'ticket_customer_service_fee' => $ticket_customer_service_fee,
                    'ticket_total_price' => $ticket_total_price,
                    'supplier_amount' => $supplier_amount,
                    'customer_amount' => $customer_amount,
                ];
                $orderDetailModel->where('order_passenger_id', $passenger_id)
                    ->where('order_id', $orderId)
                    ->set($order_detail_arr)
                    ->update();
                $orderPassengerModel->where('id', $passenger_id)
                    ->where('order_id', $orderId)
                    ->set(['total_amount' => $ticket_total_price])
                    ->update();
            }
            $orderModel->where('id', $orderId)
                ->set([
                        'total_supplier_amount' => $total_supplier_amount,
                        'total_customer_amount' => $total_customer_amount,
                    ])
                ->update();
            $this->db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $this->db->transRollback();
            error(0, $e->getMessage());
        }
    }

    public function saveDomesticRebookPrice($params)
    {
        $orderModel             = model('TicketRebookOrderModel');
        $orderPassengerModel    = model('TicketRebookPaxModel');
        $orderDetailModel       = model('TicketRebookOrderDetailModel');

        // 获取订单信息
        $orderId   = intval($params['order_id']);
        if (empty($orderId)) {
            error(0, 'order_id错误');
        }
        $order = $orderModel->find($orderId);
        if (empty($order)) {
            error(0, 'order_id错误');
        }

        // 获取乘客信息
        $inputPassengers = $params['passengers'];
        $orderPassengers = $orderPassengerModel->where('order_id', $orderId)->findAll();
        $inputPassengerIds = array_column($params['passengers'], 'passenger_id');
        $dbPassengerIds = array_column($orderPassengers, 'id');
        // 比较输入的passenger_id和数据库的passenger_id，确保提交的数据是有效的
        if (!empty(array_diff($inputPassengerIds, $dbPassengerIds)) || !empty(array_diff($dbPassengerIds, $inputPassengerIds))) {
            error(0, '数据错误');
        }

        try {
            $this->db->transException(true)->transStart();

            // 更新order_detail
            $orderDetails = $orderDetailModel->where('order_id', $orderId)->findAll();
            $orderDetails = array_column($orderDetails, null, 'order_passenger_id');
            $total_supplier_amount = 0;
            $total_customer_amount = 0;
            foreach ($inputPassengers as $inputPassenger) {
                $passenger_id = $inputPassenger['passenger_id'];
                $ticket_supplier_price_diff = $inputPassenger['ticket_supplier_price_diff'];
                $ticket_supplier_tax_diff = $inputPassenger['ticket_supplier_tax_diff'];
                $ticket_supplier_change_fee = $inputPassenger['ticket_supplier_change_fee'];
                $ticket_supplier_service_fee = $inputPassenger['ticket_supplier_service_fee'];
                $ticket_customer_price_diff = $inputPassenger['ticket_customer_price_diff'];
                $ticket_customer_tax_diff = $inputPassenger['ticket_customer_tax_diff'];
                $ticket_customer_change_fee = $inputPassenger['ticket_customer_change_fee'];
                $ticket_customer_service_fee = $inputPassenger['ticket_customer_service_fee'];

                $supplier_amount = bcadd(
                        bcadd(
                            bcadd(
                                $ticket_supplier_price_diff,
                                $ticket_supplier_tax_diff,
                                2,
                            ),
                            $ticket_supplier_change_fee,
                            2
                        ),
                        $ticket_supplier_service_fee,
                        2
                    );

                $customer_amount = bcadd(
                        bcadd(
                            bcadd(
                                $ticket_customer_price_diff,
                                $ticket_customer_tax_diff,
                                2,
                            ),
                            $ticket_customer_change_fee,
                            2
                        ),
                        $ticket_customer_service_fee,
                        2
                    );
                $total_supplier_amount += $supplier_amount;
                $total_customer_amount += $customer_amount;
                $order_detail_arr = [
                    'ticket_supplier_price_diff' => $ticket_supplier_price_diff,
                    'ticket_supplier_tax_diff' => $ticket_supplier_tax_diff,
                    'ticket_supplier_change_fee' => $ticket_supplier_change_fee,
                    'ticket_supplier_service_fee' => $ticket_supplier_service_fee,
                    'ticket_customer_price_diff' => $ticket_customer_price_diff,
                    'ticket_customer_tax_diff' => $ticket_customer_tax_diff,
                    'ticket_customer_change_fee' => $ticket_customer_change_fee,
                    'ticket_customer_service_fee' => $ticket_customer_service_fee,
                    'supplier_amount' => $supplier_amount,
                    'customer_amount' => $customer_amount,
                ];
                $orderDetailModel->where('order_passenger_id', $passenger_id)
                    ->where('order_id', $orderId)
                    ->set($order_detail_arr)
                    ->update();
            }
            $orderModel->where('id', $orderId)
                ->set([
                        'total_supplier_amount' => $total_supplier_amount,
                        'total_customer_amount' => $total_customer_amount,
                    ])
                ->update();
            $this->db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $this->db->transRollback();
            error(0, $e->getMessage());
        }
    }

    public function saveDomesticRefundPrice($params)
    {
        $orderModel             = model('TicketRefundOrderModel');
        $orderPassengerModel    = model('TicketRefundPaxModel');
        $orderDetailModel       = model('TicketRefundOrderDetailModel');

        // 获取订单信息
        $orderId   = intval($params['order_id']);
        if (empty($orderId)) {
            error(0, 'order_id错误');
        }
        $order = $orderModel->find($orderId);
        if (empty($order)) {
            error(0, 'order_id错误');
        }

        // 获取乘客信息
        $inputPassengers = $params['passengers'];
        $orderPassengers = $orderPassengerModel->where('order_id', $orderId)->findAll();
        $inputPassengerIds = array_column($params['passengers'], 'passenger_id');
        $dbPassengerIds = array_column($orderPassengers, 'id');
        // 比较输入的passenger_id和数据库的passenger_id，确保提交的数据是有效的
        if (!empty(array_diff($inputPassengerIds, $dbPassengerIds)) || !empty(array_diff($dbPassengerIds, $inputPassengerIds))) {
            error(0, '数据错误');
        }

        try {
            $this->db->transException(true)->transStart();

            // 更新order_detail
            $orderDetails = $orderDetailModel->where('order_id', $orderId)->findAll();
            $orderDetails = array_column($orderDetails, null, 'order_passenger_id');
            $total_supplier_amount = 0;
            $total_customer_amount = 0;
            foreach ($inputPassengers as $inputPassenger) {
                $passenger_id = $inputPassenger['passenger_id'];
                $ticket_marketing_price = $inputPassenger['ticket_marketing_price'];
                $ticket_tax_cn = $inputPassenger['ticket_tax_cn'];
                $ticket_tax_yq = $inputPassenger['ticket_tax_yq'];
                $ticket_supplier_deduction_fee = $inputPassenger['ticket_supplier_deduction_fee'];
                $ticket_supplier_service_fee = $inputPassenger['ticket_supplier_service_fee'];
                $ticket_customer_deduction_fee = $inputPassenger['ticket_customer_deduction_fee'];
                $ticket_customer_service_fee = $inputPassenger['ticket_customer_service_fee'];

                $total_price = bcadd(
                            bcadd(
                                $ticket_marketing_price,
                                $ticket_tax_cn,
                                2,
                            ),
                            $ticket_tax_yq,
                            2
                        );

                $supplier_amount =bcadd(
                            bcadd(
                                $total_price,
                                $ticket_supplier_deduction_fee,
                                2,
                            ),
                            $ticket_supplier_service_fee,
                            2
                        );

                $customer_amount = bcadd(
                            bcadd(
                                $total_price,
                                $ticket_customer_deduction_fee,
                                2,
                            ),
                            $ticket_customer_service_fee,
                            2
                        );
                $total_supplier_amount += $supplier_amount;
                $total_customer_amount += $customer_amount;
                $order_detail_arr = [
                    'ticket_marketing_price' => $ticket_marketing_price,
                    'ticket_tax_cn' => $ticket_tax_cn,
                    'ticket_tax_yq' => $ticket_tax_yq,
                    'ticket_supplier_deduction_fee' => $ticket_supplier_deduction_fee,
                    'ticket_supplier_service_fee' => $ticket_supplier_service_fee,
                    'ticket_customer_deduction_fee' => $ticket_customer_deduction_fee,
                    'ticket_customer_service_fee' => $ticket_customer_service_fee,
                    'supplier_amount' => $supplier_amount,
                    'customer_amount' => $customer_amount,
                ];
                $orderDetailModel->where('order_passenger_id', $passenger_id)
                    ->where('order_id', $orderId)
                    ->set($order_detail_arr)
                    ->update();
            }
            $orderModel->where('id', $orderId)
                ->set([
                        'total_supplier_amount' => $total_supplier_amount,
                        'total_customer_amount' => $total_customer_amount,
                    ])
                ->update();
            $this->db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $this->db->transRollback();
            error(0, $e->getMessage());
        }
    }
}