<?php

namespace App\Services\Intl;

use App\Services\BaseService;

class Ticket extends BaseService
{
    /**
     * 生成出票所需的PNR价格信息
     *
     * @param $orderId
     *
     * @return array
     */
    public function createPnrPassengerPriceData($orderId): array
    {
        $orderPassengerModel = model('TicketBookPaxModel');
        $orderDetailModel    = model('TicketBookOrderDetailModel');

        $orderPassengers = $orderPassengerModel->where(['order_id' => $orderId])->findAll();
        $orderDetails    = $orderDetailModel->where(['order_id' => $orderId])->findAll();
        $orderDetails    = array_column($orderDetails, null, 'order_passenger_id');

        $returnData = [];
        foreach ($orderPassengers as $pp) {
            $tmpData = [
                'rph'            => $pp['rph'],
                'person_name'    => $pp['person_name'],
                'passenger_type' => $pp['passenger_type'],
                'amount'         => $orderDetails[$pp['id']]['ticket_total_price'],
                'tax'            => $orderDetails[$pp['id']]['origin_tax_cn'] + $orderDetails[$pp['id']]['origin_tax_yq'] + $orderDetails[$pp['id']]['origin_tax_xt'],
            ];

            $returnData[] = $tmpData;
        }

        return $returnData;
    }
}