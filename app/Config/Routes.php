<?php

use App\Controllers\Admin\Intl\Rebook;
use App\Controllers\Admin\Intl\Refund;
use App\Controllers\Admin\Intl\Order;
use App\Controllers\Admin\Intl\Ticket;
use App\Controllers\Admin\Intl\Flight;
use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');
$routes->get('/zyztest', 'Home::zyztest');

$routes->get('/admin/login', 'Admin\Login::index');
$routes->post('/admin/login', 'Admin\Login::login');
$routes->post('/admin/logout', 'Admin\User::logout');
$routes->get('/admin/dashboard', 'Admin\Dashboard::index');
$routes->get('/admin/department/list', 'Admin\Department::list');
$routes->get('/admin/department/info', 'Admin\Department::info');
$routes->post('/admin/department/create', 'Admin\Department::create');
$routes->post('/admin/department/update', 'Admin\Department::update');
$routes->post('/admin/department/editStatus', 'Admin\Department::editStatus');

//公司管理
$routes->get('/admin/company', 'Admin\Company::index');
$routes->get('/admin/company/list', 'Admin\Company::list');
$routes->get('/admin/company/info', 'Admin\Company::info');
$routes->post('/admin/company/create', 'Admin\Company::create');
$routes->post('/admin/company/update', 'Admin\Company::update');
$routes->post('/admin/company/editStatus', 'Admin\Company::editStatus');

//员工管理
$routes->get('/admin/user/list', 'Admin\User::list');
$routes->get('/admin/user/info', 'Admin\User::info');
$routes->post('/admin/user/create', 'Admin\User::create');
$routes->post('/admin/user/update', 'Admin\User::update');
$routes->post('/admin/user/generateUsername', 'Admin\User::generateUsername');
$routes->post('/admin/user/editStatus', 'Admin\User::editStatus');
$routes->post('/admin/user/editPassword', 'Admin\User::editPassword');

//权限管理
$routes->get('/admin/permission/list', 'Admin\Permission::list');
$routes->post('/admin/permission/create', 'Admin\Permission::create');
$routes->post('/admin/permission/update', 'Admin\Permission::update');
$routes->post('/admin/permission/editStatus', 'Admin\Permission::editStatus');
$routes->post('/admin/permission/delete', 'Admin\Permission::delete');
$routes->get('/admin/menu/list', 'Admin\Menu::list');

//角色管理
$routes->get('/admin/role/info', 'Admin\Role::info');
$routes->post('/admin/role/create', 'Admin\Role::create');
$routes->post('/admin/role/update', 'Admin\Role::update');
$routes->post('/admin/role/editPerm', 'Admin\Role::editPerm');
$routes->post('/admin/role/delete', 'Admin\Role::delete');

//公共
$routes->post('/admin/common/listCompany', 'Admin\Common::listCompany');
$routes->post('/admin/common/listDepartment', 'Admin\Common::listDepartment');
$routes->post('/admin/common/listOrg', 'Admin\Common::listOrg');
$routes->post('/admin/common/listRole', 'Admin\Common::listRole');
$routes->post('/admin/common/listUser', 'Admin\Common::listUser');
$routes->post('/admin/common/listAirportCity', 'Admin\Common::listAirportCity');
$routes->post('/admin/common/listAirline', 'Admin\Common::listAirline');
$routes->post('/admin/common/listAirportIntl', 'Admin\Common::listAirportIntl');
$routes->post('/admin/common/listCountryCode', 'Admin\Common::listCountryCode');
$routes->post('/admin/common/listCustomerCashSubject', 'Admin\Common::listCustomerCashSubject');
$routes->post('/admin/common/listPaymentChannel', 'Admin\Common::listPaymentChannel');
$routes->post('/admin/common/listParentCustomer', 'Admin\Common::listParentCustomer');
$routes->post('/admin/common/listBank', 'Admin\Common::listBank');
$routes->post('/admin/common/uploadImages', 'Admin\common::uploadImages');

//机票
$routes->post('/admin/flight/domestic_list', 'Admin\Flight::domestic_list');
$routes->post('/admin/flight/cabin_prices', 'Admin\Flight::cabin_prices');
$routes->post('/admin/flight/refund_change_rules', 'Admin\Flight::refund_change_rules');
$routes->post('/admin/flight/check_price', 'Admin\Flight::check_price');
$routes->post('/admin/order/create_order', 'Admin\Order::create_order');
$routes->post('/admin/order/confirm_order', 'Admin\Order::confirm_order');
$routes->post('/admin/ticket/issue_domestic_ticket', 'Admin\Ticket::issue_domestic_ticket');
$routes->post('/admin/order/domestic_list', 'Admin\Order::domestic_list');
$routes->get('/admin/order/domestic_detail', 'Admin\Order::domestic_detail');
$routes->get('/admin/ticket/domestic_refund_apply', 'Admin\Ticket::domestic_refund_apply');
$routes->get('/admin/ticket/domestic_refund_fee', 'Admin\Ticket::domestic_refund_fee');
$routes->post('/admin/ticket/domestic_confirm_refund', 'Admin\Ticket::domestic_confirm_refund');
$routes->get('/admin/ticket/domestic_change_apply', 'Admin\Ticket::domestic_change_apply'); // 获取改签信息
$routes->get('/admin/flight/domestic_reshop', 'Admin\Flight::domestic_reshop');  // 改签航班查询
$routes->post('/admin/flight/domestic_reissue_price', 'Admin\Flight::domestic_reissue_price'); // 获取改签价格
$routes->post('/admin/ticket/domestic_confirm_change', 'Admin\Ticket::domestic_confirm_change'); // 改签机票接口
$routes->get('/admin/ticket/domestic_refund_detail', 'Admin\Ticket::domestic_refund_detail'); // 退票订单详情
$routes->get('/admin/ticket/domestic_rebook_detail', 'Admin\Ticket::domestic_rebook_detail'); // 改签订单详情
$routes->post('/admin/order/domestic_rebook_list', 'Admin\Order::domestic_rebook_list');
$routes->post('/admin/order/domestic_refund_list', 'Admin\Order::domestic_refund_list');
$routes->post('/admin/order/save_domestic_book_price', 'Admin\Order::save_domestic_book_price');
$routes->post('/admin/order/save_domestic_rebook_price', 'Admin\Order::save_domestic_rebook_price');
$routes->post('/admin/order/save_domestic_refund_price', 'Admin\Order::save_domestic_refund_price');

//【会员管理】
//散客会员
$routes->get('/admin/customer/list', 'Admin\Customer::list');
$routes->post('/admin/customer/create', 'Admin\Customer::create');
$routes->post('/admin/customer/update', 'Admin\Customer::update');
$routes->post('/admin/customer/detail', 'Admin\Customer::detail');
$routes->post('/admin/customer/import', 'Admin\Customer::import');
$routes->get('/admin/customer/export', 'Admin\Customer::export');
//企业会员
$routes->get('/admin/customerEnterprise/list', 'Admin\CustomerEnterprise::list');
$routes->post('/admin/customerEnterprise/create', 'Admin\CustomerEnterprise::create');
$routes->post('/admin/customerEnterprise/update', 'Admin\CustomerEnterprise::update');
$routes->post('/admin/customerEnterprise/detail', 'Admin\CustomerEnterprise::detail');
$routes->post('/admin/customerEnterprise/import', 'Admin\CustomerEnterprise::import');
$routes->get('/admin/customerEnterprise/export', 'Admin\CustomerEnterprise::export');
//公务会员
$routes->get('/admin/customerOfficial/list', 'Admin\CustomerOfficial::list');
$routes->post('/admin/customerOfficial/create', 'Admin\CustomerOfficial::create');
$routes->post('/admin/customerOfficial/update', 'Admin\CustomerOfficial::update');
$routes->post('/admin/customerOfficial/detail', 'Admin\CustomerOfficial::detail');
$routes->post('/admin/customerOfficial/import', 'Admin\CustomerOfficial::import');
$routes->get('/admin/customerOfficial/export', 'Admin\CustomerOfficial::export');
//会员公共模块
$routes->post('/admin/customer/editStatus', 'Admin\Customer::editStatus');
$routes->post('/admin/customer/getListByType', 'Admin\Customer::getListByType');//根据类型获取会员列表

//会员预存金
$routes->post('/admin/customerCash/list', 'Admin\CustomerCash::list');
$routes->post('/admin/customerCash/rechargeApply', 'Admin\customerCash::rechargeApply');
$routes->post('/admin/customerCash/rechargeAudit', 'Admin\customerCash::rechargeAudit');
//会员授信
$routes->post('/admin/customerCredit/list', 'Admin\customerCredit::list');
$routes->post('/admin/customerCredit/adjustAmount', 'Admin\customerCredit::adjustAmount');

$routes->get('/cli/pullOrder', []); // 拉取订单
/**
 * 国际机票相关
 */
$routes->get('/admin/intl/flight/list', [Flight::class, 'list']); // 查询机票列表
$routes->post('/admin/intl/flight/check_price', [Flight::class, 'checkPrice']); // （检查价格）通过航段查询价格
// 订单相关
$routes->post('/admin/intl/order/create_order', [Order::class, 'createOrder']); // 创建订单
$routes->post('/admin/intl/order/confirm_order', [Order::class, 'confirmOrder']); // 确认订单
$routes->post('/admin/intl/ticket/issue_ticket', [Ticket::class, 'issueTicket']); // 出票
$routes->get('/admin/intl/order/list', [Order::class, 'orderList']); // 订单列表
$routes->get('/admin/intl/order/detail', [Order::class, 'orderDetail']); // 订单详情
// 退款相关
$routes->get('/admin/intl/refund/apply', [Refund::class, 'apply']); // 退款申请
$routes->get('/admin/intl/refund/fee', [Refund::class, 'fee']); // 退款费用
$routes->post('/admin/intl/refund/confirm', [Refund::class, 'confirm']); // 确认退款
$routes->get('/admin/intl/refund/list', [Refund::class, 'refundList']); // 退款订单列表
$routes->get('/admin/intl/refund/detail', [Refund::class, 'refundDetail']); // 退款订单详情
// 改签相关
$routes->get('/admin/intl/rebook/apply', [Rebook::class, 'apply']); // 改签申请
$routes->post('/admin/intl/rebook/reshop', [Rebook::class, 'reshop']); // 查询改签航班
$routes->post('/admin/intl/rebook/fee', [Rebook::class, 'fee']); // 查询改签费用
$routes->post('/admin/intl/rebook/confirm', [Rebook::class, 'confirm']); // 确认改签
$routes->get('/admin/intl/rebook/list', [Rebook::class, 'list']); // 改签订单列表
$routes->get('/admin/intl/rebook/detail', [Rebook::class, 'detail']); // 改签订单详情