<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Services\PnrData\PullPnrDataService;

class PullPnrData extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'pull:pnr';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '从IBE接口拉取PNR数据并存储出票订单';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'pull:pnr [start_time] [end_time] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [
        'start_time' => '开始时间，格式：YYYY-MM-DD HH:MM:SS，不传则默认为当前时间前1小时',
        'end_time'   => '结束时间，格式：YYYY-MM-DD HH:MM:SS，必须与start_time同时传入',
    ];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        '--office'     => '指定OFFICE号，默认使用配置文件中的值',
        '--ticket_num' => '指定票号进行过滤',
        '--pnr'        => '指定PNR进行过滤',
    ];

    /**
     * Actually execute a command.
     *
     * @param  array  $params
     */
    public function run(array $params)
    {
        CLI::write('开始拉取PNR数据...', 'green');

        $pullService = new PullPnrDataService();
        try {
            // 解析时间参数
            $timeParams = $pullService->parseTimeParams($params);
            $startTime  = $timeParams['start_time'];
            $endTime    = $timeParams['end_time'];

            // 获取其他参数
            $ticketNum = CLI::getOption('ticket_num');
            $pnr       = CLI::getOption('pnr');

            CLI::write("拉取时间范围: {$startTime} ~ {$endTime}", 'yellow');

            if ($ticketNum) {
                CLI::write("过滤票号: {$ticketNum}", 'yellow');
            }
            if ($pnr) {
                CLI::write("过滤PNR: {$pnr}", 'yellow');
            }

            // 创建服务实例并执行拉取
            $result = $pullService->pullAndSaveData($startTime, $endTime, $ticketNum, $pnr);

            // 输出结果
            CLI::write("拉取完成！", 'green');
            CLI::write("总记录数: {$result['total_records']}", 'cyan');
            CLI::write("出票订单数: {$result['ticket_orders']}", 'cyan');
            CLI::write("跳过记录数: {$result['skipped_records']}", 'cyan');

            if (!empty($result['errors'])) {
                // 记录错误日志到文件
                log_message('error', json_encode($result['errors']));

                CLI::write("错误信息:", 'red');
                foreach ($result['errors'] as $error) {
                    CLI::write("  - {$error}", 'red');
                }
            }

        } catch (\Exception $e) {
            log_message('error', $e->getMessage());
            CLI::error('执行失败: ' . $e->getMessage());
            CLI::write($e->getTraceAsString(), 'red');
        }
    }
}
