<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class QuerySegments extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'query:segments';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '查询航段信息';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $orderId = $params[0] ?? 62;
        
        CLI::write("查询订单 {$orderId} 的航段信息", 'green');
        
        $db = \Config\Database::connect();
        $segments = $db->table('ticket_book_order_segments')->where('order_id', $orderId)->get()->getResultArray();
        
        CLI::write("找到 " . count($segments) . " 个航段", 'yellow');
        
        foreach ($segments as $segment) {
            CLI::write("RPH: {$segment['rph']}, 航班: {$segment['airline']}{$segment['flight_number']}, 舱位: {$segment['cabin']}", 'cyan');
            CLI::write("出发: {$segment['departure_datetime']}, 到达: {$segment['arrival_datetime']}", 'cyan');
        }
    }
}
