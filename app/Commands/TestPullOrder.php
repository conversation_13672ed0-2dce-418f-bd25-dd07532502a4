<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Services\PnrData\PullPnrDataService;

class TestPullOrder extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'test:pullorder';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '测试拉取订单接口';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('测试拉取订单接口...', 'green');
        
        try {
            // 创建服务实例
            $pullService = new PullPnrDataService();
            
            // 测试参数
            $startTime = '2024-01-01 00:00:00';
            $endTime = '2024-01-01 23:59:59';
            $ticketNumber = null;
            $pnr = null;
            
            CLI::write("调用 pullDataOnly 方法...", 'yellow');
            CLI::write("参数: start_time={$startTime}, end_time={$endTime}", 'cyan');
            
            // 调用方法
            $result = $pullService->pullDataOnly($startTime, $endTime, $ticketNumber, $pnr);
            
            CLI::write("结果:", 'yellow');
            CLI::write(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), 'white');
            
            CLI::write("测试完成！", 'green');
            
        } catch (\Exception $e) {
            CLI::error("错误: " . $e->getMessage());
            CLI::write("堆栈跟踪:", 'red');
            CLI::write($e->getTraceAsString(), 'red');
        }
    }
}
