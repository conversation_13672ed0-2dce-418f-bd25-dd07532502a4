<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class ShowTableStructure extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'show:table';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '显示数据库表结构';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $tableName = $params[0] ?? 'ticket_book_order_segments';
        
        CLI::write("显示表结构: {$tableName}", 'green');
        
        $db = \Config\Database::connect();
        
        try {
            $fields = $db->getFieldData($tableName);
            
            CLI::write("字段列表:", 'yellow');
            foreach ($fields as $field) {
                CLI::write("  {$field->name} - {$field->type}", 'cyan');
            }
        } catch (\Exception $e) {
            CLI::error("获取表结构失败: " . $e->getMessage());
        }
    }
}
