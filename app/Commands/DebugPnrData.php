<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Services\PnrData\PullPnrDataService;

class DebugPnrData extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'debug:pnr';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '调试PNR数据解析';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('调试PNR数据解析...', 'green');
        
        try {
            $service = new PullPnrDataService();
            
            // 使用反射来访问私有方法
            $reflection = new \ReflectionClass($service);
            
            // 测试PNR详情解析
            CLI::write('测试PNR详情解析...', 'yellow');
            $pnrMethod = $reflection->getMethod('callIbePnrDetailApi');
            $pnrMethod->setAccessible(true);
            $pnrDetail = $pnrMethod->invoke($service, 'HZNR2X', 'BJS191');
            
            CLI::write('PNR详情解析结果:', 'cyan');
            CLI::write(json_encode($pnrDetail, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), 'white');
            
            // 测试票务详情解析
            CLI::write('测试票务详情解析...', 'yellow');
            $ticketMethod = $reflection->getMethod('callIbeTicketDetailApi');
            $ticketMethod->setAccessible(true);
            $ticketDetail = $ticketMethod->invoke($service, '479-3995946711', 'BJS191', 'HZNR2X');
            
            CLI::write('票务详情解析结果:', 'cyan');
            CLI::write(json_encode($ticketDetail, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE), 'white');
            
        } catch (\Exception $e) {
            CLI::error('调试失败: ' . $e->getMessage());
            CLI::write($e->getTraceAsString(), 'red');
        }
    }
}
