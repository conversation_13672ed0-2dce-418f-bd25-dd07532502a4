<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\Api\IBE\Ticket;

class TestIbeTicket extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'test:ibe';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '测试IBE Ticket类的方法';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('测试IBE Ticket类...', 'green');
        
        try {
            $ticket = new Ticket();
            
            // 测试View Report接口
            CLI::write('测试View Report接口...', 'yellow');
            $reportData = $ticket->viewReport('2022-09-26', 'BJS191');
            CLI::write('View Report成功，记录数: ' . count($reportData['tslDetails'] ?? []), 'cyan');
            
            // 测试过滤出票订单
            CLI::write('测试过滤出票订单...', 'yellow');
            $ticketOrders = $ticket->filterTicketOrders($reportData['tslDetails'] ?? []);
            CLI::write('出票订单数: ' . count($ticketOrders), 'cyan');
            
            if (!empty($ticketOrders)) {
                $firstOrder = $ticketOrders[0];
                CLI::write('第一个出票订单PNR: ' . $firstOrder['pnr'], 'cyan');
                
                // 测试PNR详情接口
                CLI::write('测试PNR详情接口...', 'yellow');
                $pnrDetail = $ticket->viewReportPnr($firstOrder['pnr'], 'BJS191');
                CLI::write('PNR详情成功，乘客数: ' . count($pnrDetail['passengers'] ?? []), 'cyan');
                CLI::write('航段数: ' . count($pnrDetail['segments'] ?? []), 'cyan');
                
                // 测试票务详情接口
                CLI::write('测试票务详情接口...', 'yellow');
                $ticketDetail = $ticket->viewReportTicket($firstOrder['ticketNumber'], 'BJS191', $firstOrder['pnr']);
                CLI::write('票务详情成功，票号: ' . ($ticketDetail['ticketing']['ticket_number'] ?? 'N/A'), 'cyan');
                
                // 测试乘客类型映射
                CLI::write('测试乘客类型映射...', 'yellow');
                $passengerType1 = $ticket->mapPassengerType('ADT');
                $passengerType2 = $ticket->mapPassengerType('CNN');
                $passengerType3 = $ticket->mapPassengerType('INF');
                CLI::write("ADT -> {$passengerType1}, CNN -> {$passengerType2}, INF -> {$passengerType3}", 'cyan');
            }
            
            CLI::write('所有测试通过！', 'green');
            
        } catch (\Exception $e) {
            CLI::error('测试失败: ' . $e->getMessage());
            CLI::write($e->getTraceAsString(), 'red');
        }
    }
}
