<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\TicketBookOrderModel;
use App\Models\TicketBookOrderPassengerModel;
use App\Models\TicketBookOrderSegmentModel;

class TestPnrData extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'test:pnr';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '测试PNR数据是否正确保存';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('检查PNR数据...', 'green');
        
        $orderModel = new TicketBookOrderModel();
        $passengerModel = new TicketBookOrderPassengerModel();
        $segmentModel = new TicketBookOrderSegmentModel();
        
        // 查询最近的订单
        $orders = $orderModel->orderBy('id', 'DESC')->limit(3)->findAll();
        
        if (empty($orders)) {
            CLI::write('没有找到订单数据', 'red');
            return;
        }
        
        CLI::write('最近的订单详情:', 'yellow');
        foreach ($orders as $order) {
            CLI::write("=== 订单ID: {$order['id']} ===", 'cyan');
            CLI::write("订单号: {$order['order_no']}", 'cyan');
            CLI::write("PNR: {$order['pnr']}", 'cyan');
            CLI::write("状态: {$order['status']}", 'cyan');
            CLI::write("总金额: {$order['total_customer_amount']}", 'cyan');
            CLI::write("乘客数量: {$order['passenger_number']}", 'cyan');
            CLI::write("乘客姓名: {$order['passenger_names']}", 'cyan');
            CLI::write("航程类型: {$order['journey_type']}", 'cyan');
            CLI::write("航程信息: {$order['journey_info']}", 'cyan');
            CLI::write("联系人: {$order['contact_name']}", 'cyan');
            CLI::write("联系电话: {$order['contact_telephone']}", 'cyan');
            CLI::write("创建时间: " . date('Y-m-d H:i:s', $order['created_at']), 'cyan');
            
            // 查询乘客信息
            $passengers = $passengerModel->where('order_id', $order['id'])->findAll();
            if (!empty($passengers)) {
                CLI::write("乘客信息:", 'yellow');
                foreach ($passengers as $passenger) {
                    CLI::write("  - RPH: {$passenger['rph']}, 姓名: {$passenger['person_name']}, 类型: {$passenger['passenger_type']}, 票号: {$passenger['ticket_number']}", 'white');
                }
            }
            
            // 查询航段信息
            $segments = $segmentModel->where('order_id', $order['id'])->findAll();
            if (!empty($segments)) {
                CLI::write("航段信息:", 'yellow');
                foreach ($segments as $segment) {
                    CLI::write("  - RPH: {$segment['rph']}, 航班: {$segment['flight_number']}, {$segment['departure_airport']}->{$segment['arrival_airport']}, 舱位: {$segment['cabin']}", 'white');
                }
            }
            
            CLI::write('---', 'white');
        }
    }
}
