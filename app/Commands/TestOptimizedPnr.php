<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Libraries\Api\IBE\Ticket;
use App\Services\PnrData\PullPnrDataService;

class TestOptimizedPnr extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'test:optimized';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '测试优化后的PNR拉取功能';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('测试优化后的PNR拉取功能...', 'green');
        
        try {
            $ticket = new Ticket();
            
            // 1. 测试时间范围查询
            CLI::write('1. 测试时间范围查询...', 'yellow');
            $timeRangeData = $ticket->viewReportByTimeRange('2022-09-26 10:00:00', '2022-09-26 18:00:00', 'BJS191');
            CLI::write('时间范围查询成功，记录数: ' . count($timeRangeData['tslDetails'] ?? []), 'cyan');
            
            // 2. 测试过滤功能
            CLI::write('2. 测试过滤功能...', 'yellow');
            $service = new PullPnrDataService();
            
            // 使用反射访问私有方法
            $reflection = new \ReflectionClass($service);
            $filterMethod = $reflection->getMethod('filterByParams');
            $filterMethod->setAccessible(true);
            
            // 模拟订单数据
            $mockOrders = [
                ['pnr' => 'HZNR2X', 'ticketNumber' => '479-3995946711'],
                ['pnr' => 'ABC123', 'ticketNumber' => '479-1234567890'],
                ['pnr' => 'HZNR2X', 'ticketNumber' => '479-9876543210']
            ];
            
            // 测试PNR过滤
            $filteredByPnr = $filterMethod->invoke($service, $mockOrders, null, 'HZNR2X');
            CLI::write('PNR过滤结果: ' . count($filteredByPnr) . ' 条记录', 'cyan');
            
            // 测试票号过滤
            $filteredByTicket = $filterMethod->invoke($service, $mockOrders, '479-3995946711', null);
            CLI::write('票号过滤结果: ' . count($filteredByTicket) . ' 条记录', 'cyan');
            
            // 测试组合过滤
            $filteredBoth = $filterMethod->invoke($service, $mockOrders, '479-3995946711', 'HZNR2X');
            CLI::write('组合过滤结果: ' . count($filteredBoth) . ' 条记录', 'cyan');
            
            // 3. 测试完整流程
            CLI::write('3. 测试完整流程（无过滤）...', 'yellow');
            $result1 = $service->pullAndSaveData('2022-09-26 10:00:00', '2022-09-26 18:00:00', 'BJS191');
            CLI::write("无过滤结果 - 总记录: {$result1['total_records']}, 出票订单: {$result1['ticket_orders']}", 'cyan');
            
            CLI::write('4. 测试完整流程（PNR过滤）...', 'yellow');
            $result2 = $service->pullAndSaveData('2022-09-26 10:00:00', '2022-09-26 18:00:00', 'BJS191', null, 'HZNR2X');
            CLI::write("PNR过滤结果 - 总记录: {$result2['total_records']}, 出票订单: {$result2['ticket_orders']}", 'cyan');
            
            CLI::write('所有测试通过！', 'green');
            
        } catch (\Exception $e) {
            CLI::error('测试失败: ' . $e->getMessage());
            CLI::write($e->getTraceAsString(), 'red');
        }
    }
}
