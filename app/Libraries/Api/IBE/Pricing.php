<?php

namespace App\Libraries\Api\IBE;

class Pricing
{
    protected $ibeConfig;

    public function __construct()
    {
        $this->ibe_config = config('IBE');
    }

    // 按航段查询价格
    // 调用参数
    // 注意调用时，如果相同的城市对不同的舱分布在不同的记录，特别是不同航班号相同的舱的情况下，注意区分返回记录的对应关系，因为返回记录不带有航班号。
    //[
    //    [ // 多条记录
    //        'departure_datetime' => '2014-03-20T15:30:00', //出发时间
    //        'arrival_datetime' => '2014-03-20T17:00:00', //到达时间
    //        'flight_number' => 'CA1325', //航班号
    //        'departure_airport' => 'CGO', //出发机场
    //        'arrival_airport' => 'PEK', //到达机场
    //        'air_equip_type' => '738', //机型
    //        'passenger_type' => 'ADT', //乘客类型：ADT成人 CHD儿童 INF婴儿
    //        'class' => 'V', // 舱位
    //        'sub_class' => 'V1', // 子舱位（可选）
    //    ],
    //    [
    //        'departure_datetime' => '2014-03-21T15:30:00',
    //        'arrival_datetime' => '2014-03-21T17:00:00',
    //        'flight_number' => 'CA1326',
    //        'departure_airport' => 'PEK',
    //        'arrival_airport' => 'CGO',
    //        'air_equip_type' => '738',
    //        'passenger_type' => 'CHD',
    //        'class' => 'V',
    //        'sub_class' => 'V1',
    //    ],
    //];
    //
    // 返回数据：
    //[
    //    [CA-CGO-PEK] => // 多条记录 字段名为 航空公司-出发机场-到达机场
    //        [
    //            [V1] => // 舱位
    //                [
    //                    [price] => // 价格
    //                        [
    //                            [amount] => 340.0 //票面价
    //                            [texes] => // 税费
    //                                [
    //                                    [CN] => // 机场建设费
    //                                        [
    //                                            [amount] => 50.0
    //                                            [currency_code] => CNY
    //                                        ]
    //                                    [YQ] => // 燃油附加费
    //                                        [
    //                                            [amount] => 70.0
    //                                            [currency_code] => CNY
    //                                        ]
    //
    //                                ]
    //                            [remark] => FC/CGOA-13MAR15 CAPEK340.00V1 CNY340.00END
    //                            FN/FCNY340.00/SCNY340.00/C3.00/TCNY50.00CN/TCNY70.00YQ
    //                            FP/CASH,CNY
    //                        ]
    //                    [fare_type] => 5
    //                ]
    //            [V] => 
    //                [
    //                    [price] => 
    //                        [
    //                            [amount] => 380.0
    //                            [texes] => 
    //                                [
    //                                    [CN] => 
    //                                        [
    //                                            [amount] => 50.0
    //                                            [currency_code] => CNY
    //                                        ]
    //
    //                                    [YQ] => 
    //                                        [
    //                                            [amount] => 70.0
    //                                            [currency_code] => CNY
    //                                        ]
    //
    //                                ]
    //                            [remark] => FC/CGOA-13MAR15 CAPEK380.00VCNY380.00END
    //                            FN/FCNY380.00/SCNY380.00/C3.00/TCNY50.00CN/TCNY70.00YQ
    //                            FP/CASH,CNY
    //                        ]
    //                    [fare_type] => 5
    //                ]
    //        ]
    //]
    public function query_domestic_price_by_segment($params)
    {
        $requestXml = '<?xml version="1.0" encoding="utf-8"?>
            <OTA_AirPriceRQ>
                <POS>
                    <Source CityCode="' . $this->ibe_config->city . '" PseudoCityCode="' . $this->ibe_config->office . '">
                        <RequestorID>
                            <CompanyName Code="CA" />
                        </RequestorID>
                    </Source>
                </POS>
                <AirItinerary>
                    <OriginDestinationOptions>';
        foreach ($params as $flightSegment) {
            $requestXml .= '
                        <OriginDestinationOption>
                            <FlightSegment Status="HK"
                                DepartureDateTime="' . $flightSegment['departure_datetime'] . '"
                                ArrivalDateTime="' . $flightSegment['departure_datetime'] . '"
                                FlightNumber="' . $flightSegment['flight_number'] . '">
                                <DepartureAirport LocationCode="' . $flightSegment['departure_airport'] . '" />
                                <ArrivalAirport LocationCode="' . $flightSegment['arrival_airport'] . '" />
                                <Equipment AirEquipType="' . $flightSegment['air_equip_type'] . '" />
                                <BookingClassAvail ResBookDesigCode="' . $flightSegment['class'] . '">';
            if (!empty($flightSegment['sub_class'])) {
                $requestXml .= '
                                    <SubClass>V1</SubClass>';
            }
            $requestXml .= '
                                </BookingClassAvail>
                            </FlightSegment>
                        </OriginDestinationOption>';
        }
        $requestXml .= '
                    </OriginDestinationOptions>
                </AirItinerary>
                <TravelerInfoSummary>
                    <AirTravelerAvail>
                        <PassengerTypeQuantity Code="' . $flightSegment['passenger_type'] . '" />
                    </AirTravelerAvail>
                </TravelerInfoSummary>
            </OTA_AirPriceRQ>';

        //echo $requestXml;

        // TODO 发起接口调用

        // 模拟解析接口返回XML
        // TODO 解析正式接口返回XML
        $xml              = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/price_by_segment.xml'));
        $priceItinerarys = $xml->PricedItineraries->PricedItinerary;
        $arrTotalFare   = [];
        $arrFareInfo    = [];
        foreach ($priceItinerarys as $priceItinerary) {
            foreach ($priceItinerary->AirItineraryPricingInfo->ItinTotalFare as $itinTotalFare) {
                $tmpTax = [];
                foreach ($itinTotalFare->Taxes->Tax as $tax) {
                    $tmpTax[(string)$tax['TaxCode']] = [
                        'amount'        => (string)$tax['Amount'],
                        'currency_code' => (string)$tax['CurrencyCode'],
                    ];
                }
                $arrTotalFare[(string)$itinTotalFare->FareInfoRef['RPH']] = [
                    'amount' => (string)$itinTotalFare->BaseFare['Amount'],
                    'texes'  => $tmpTax,
                    'remark' => (string)$itinTotalFare->Remark->Text,
                ];
            }
            foreach ($priceItinerary->AirItineraryPricingInfo->FareInfos->FareInfo as $fareInfo) {
                $arrFareInfo[(string)$fareInfo->MarketingAirline['Code'] . '-' . (string)$fareInfo->DepartureAirport['LocationCode'] . '-' . (string)$fareInfo->ArrivalAirport['LocationCode']][(string)$fareInfo->FareBasisCodes->FareBasisCode] = [
                    'price'     => $arrTotalFare[(string)$fareInfo['RPH']],
                    'fare_type' => (string)$fareInfo['FareType'],
                ];
            }
        }

        // 模拟数据
        $db            = db_connect();
        $arrFareInfo = [];
        foreach ($params as $flightSegment) {
            $airline       = substr($flightSegment['flight_number'], 0, 2);
            $flightNumber = substr($flightSegment['flight_number'], 2, 4);
            $key           = $airline . '-' . $flightSegment['departure_airport'] . '-' . $flightSegment['arrival_airport'];
            $cabins        = $db->query("select * from cabin_price where airline = " . $db->escape($airline) . " 
                and (
                    (airport_1 = " . $db->escape($flightSegment['departure_airport']) . " and airport_2 = " . $db->escape($flightSegment['arrival_airport']) . ")
                    or
                    (airport_1 = " . $db->escape($flightSegment['arrival_airport']) . " and airport_2 = " . $db->escape($flightSegment['departure_airport']) . ")
                )
                and cabin = " . $db->escape($flightSegment['class']) . "
                order by price")->getResultArray();
            foreach ($cabins as $cabin) {
                $price = $cabin['price'];
                if ($flightSegment['passenger_type'] == 'CHD') {
                    $price = $price * 0.5;
                } else if ($flightSegment['passenger_type'] == 'INF') {
                    $price = $price * 0.1;
                }
                $arrFareInfo[$key][$cabin['cabin']] = [
                    'price'     => [
                        'amount' => $price,
                        'texes'  => [
                            'CN' => [
                                'amount'   => 50,
                                'currency' => 'CNY',
                            ],
                            'YQ' => [
                                'amount'   => 20,
                                'currency' => 'CNY',
                            ],
                        ],
                        'remark' => '',
                    ],
                    'fare_type' => 5,
                ];
            }
        }

        return $arrFareInfo;
    }


    // 根据PNR编号查询国内机票价格
    // 接口参数：
    //[
    //    'passenger_type' => 'ADT', // 旅客类型：ADT成人 CHD儿童 INF婴儿
    //    'passenger_rph' => 0, // 旅客编号：0为查全部旅客的价格，1...9为查某个游客的价格
    //    'payment_type' => 'CASH', // 付款类型：CASH 现金 CREDIT_CARD 信用卡
    //    'pnr' => 'H12345' // PNR
    //];
    // 返回数据：
    //[
    //    [0] =>  // 多条记录，记录为按舱位或基础价格代码的价格信息
    //        [
    //            [rph] => 1 // 编号
    //            [amount] => 1130 // 价格
    //            [currency_code] => CNY // 货币
    //            [taxes] => // 税费
    //                [
    //                    [CN] => // 多条记录，CN为机场建设费，YQ为燃油附加费
    //                        [
    //                            [amount] => 50 // 金额
    //                            [currency_code] => CNY // 货币
    //                        ]
    //                    [YQ] => 
    //                        [
    //                            [amount] => 50
    //                            [currency_code] => CNY
    //                        ]
    //                ]
    //            [fare_basis_code] => Y // 舱位或基础价格代码
    //        ]
    //]
    public function query_domestic_price_by_pnr($params)
    {
        $requestXml = '<OTA_AirPriceRQ TransactionIdentifier="PAT">
            <POS>
                <Source PseudoCityCode="' . $this->ibe_config->office . '" />
            </POS>
            <TravelerInfoSummary>
                <AirTravelerAvail>
                    <PassengerTypeQuantity Code="' . $params['passenger_type'] . '" />';
        if ($params['passenger_rph'] != 0) {
            $requestXml .= '
                    <AirTraveler>
                        <TravelerRefNumber RPH="' . $params['passenger_rph'] . '">
                    </AirTraveler>';
        }
        $requestXml .= '
                </AirTravelerAvail>
                <PaymentDetail PaymentType="' . $params['payment_type'] . '" />
            </TravelerInfoSummary>
            <BookingReferenceID ID="' . $params['pnr'] . '" />
        </OTA_AirPriceRQ>';

        // TODO 发起接口调用

        // 模拟解析接口返回XML
        // TODO 解析正式接口返回XML
        $xml              = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/pata_domestic_price_query.xml'));
        $itinTotalFares = $xml->PricedItineraries->PricedItinerary->AirItineraryPricingInfo->ItinTotalFare;
        $arrFares        = [];

        foreach ($itinTotalFares as $itinTotalFare) {
            $taxes = [];
            foreach ($itinTotalFare->Taxes->Tax as $tax) {
                $taxes[(string)$tax['TaxCode']] = [
                    'amount'        => (string)$tax['Amount'],
                    'currency_code' => (string)$tax['CurrencyCode'],
                ];
            }
            $arrFares[] = [
                'rph'             => (string)$itinTotalFare['RPH'],
                'amount'          => (string)$itinTotalFare->BaseFare['Amount'],
                'currency_code'   => (string)$itinTotalFare->BaseFare['CurrencyCode'],
                'taxes'           => $taxes,
                'fare_basis_code' => (string)$itinTotalFare->FareBasisCodes->FareBasisCode,
            ];
        }

        // 模拟数据
        $db       = db_connect();
        $pnrInfo = $db->table('pnr')->select('id')->where('pnr', $params['pnr'])->get()->getRowArray();
        if (empty($pnrInfo)) {
            return [];
        }
        $flightList = $db->table('ticket_book_seg')->where('id', '9')->orderBy('id', 'asc')->get()->getResultArray();
        if (empty($flightList)) {
            return [];
        }
        $arrFares = [];
        $tmpFare  = [];

        foreach ($flightList as $item) {
            $airline       = $item['airline'];
            $flightNumber = substr($item['flight_number'], 2, 4);
            $cabin         = $item['cabin'];

            $flight = $db->table('flights')->where('flight_number', $airline . $flightNumber)->get()->getRowArray();
            if (empty($flight)) {
                return [];
            }

            $cabinInfo = $db->query("select * from cabin_price where airline = " . $db->escape($airline) . " 
                and (
                    (airport_1 = " . $db->escape($flight['departure_airport']) . " and airport_2 = " . $db->escape($flight['arrival_airport']) . ")
                    or
                    (airport_1 = " . $db->escape($flight['arrival_airport']) . " and airport_2 = " . $db->escape($flight['departure_airport']) . ")
                )
                and cabin = " . $db->escape($cabin))->getRowArray();
            if (empty($cabinInfo)) {
                return [];
            }
            $price = $cabinInfo['price'];
            if ($params['passenger_type'] == 'CHD') {
                $price = $price * 0.5;
            } else if ($params['passenger_type'] == 'INF') {
                $price = $price * 0.1;
            }

            $tmpFare[] = [
                'cabin' => $cabin,
                'price' => $price,
            ];
        }
        $totalAmount = 0;
        $fbc          = '';
        $totalTaxCn = 0;
        $totalTaxYq = 0;
        foreach ($tmpFare as $v) {
            $totalAmount += $v['price'];
            if (empty($fbc)) {
                $fbc = $v['cabin'];
            } else {
                $fbc .= '+' . $v['cabin'];
            }
            $totalTaxCn += 50;
            $totalTaxYq += 20;
        }

        $arrFares = [
            0 => [
                'rph'             => 1,
                'amount'          => $totalAmount,
                'currency_code'   => 'CNY',
                'taxes'           => [
                    'CN' => [
                        'amount'        => $totalTaxCn,
                        'currency_code' => 'CNY',
                    ],
                    'YQ' => [
                        'amount'        => $totalTaxYq,
                        'currency_code' => 'CNY',
                    ],
                ],
                'fare_basis_code' => $fbc,
            ],
        ];

        return $arrFares;
    }

    // 根据PNR编号存储国内机票价格到PNR
    // 接口参数：
    //[
    //    'passenger_type' => 'ADT', // 旅客类型：ADT成人 CHD儿童 INF婴儿
    //    'passenger_rph' => 0, // 旅客编号：0为查全部旅客的价格，1...9为查某个游客的价格
    //    'payment_type' => 'CASH', // 付款类型：CASH 现金 CREDIT_CARD 信用卡
    //    'pnr' => 'H12345', // PNR
    //    'fare_ref_rph' => 1, // 存储查询返回的第rph条价格到PNR
    //];
    // 返回数据：
    //[
    //    [0] =>  // 多条记录，记录为按舱位或基础价格代码的价格信息
    //        [
    //            [rph] => 1 // 编号
    //            [amount] => 1130 // 价格
    //            [currency_code] => CNY // 货币
    //            [taxes] => // 税费
    //                [
    //                    [CN] => // 多条记录，CN为机场建设费，YQ为燃油附加费
    //                        [
    //                            [amount] => 50 // 金额
    //                            [currency_code] => CNY // 货币
    //                        ]
    //                    [YQ] => 
    //                        [
    //                            [amount] => 50
    //                            [currency_code] => CNY
    //                        ]
    //                ]
    //            [fare_basis_code] => Y // 舱位或基础价格代码
    //        ]
    //]
    public function store_domestic_price_by_pnr($params)
    {
        $requestXml = '<OTA_AirPriceRQ TransactionIdentifier="SFC" FareRefRPH="' . $params['fare_ref_rph'] . '">
            <POS>
                <Source PseudoCityCode="' . $this->ibe_config->office . '" />
            </POS>
            <TravelerInfoSummary>
                <AirTravelerAvail>
                    <PassengerTypeQuantity Code="' . $params['passenger_type'] . '" />';
        if ($params['passenger_rph'] != 0) {
            $requestXml .= '
                    <AirTraveler>
                        <TravelerRefNumber RPH="' . $params['passenger_rph'] . '">
                    </AirTraveler>';
        }
        $requestXml .= '
                </AirTravelerAvail>
                <PaymentDetail PaymentType="' . $params['payment_type'] . '" />
            </TravelerInfoSummary>
            <BookingReferenceID ID="' . $params['pnr'] . '" />
        </OTA_AirPriceRQ>';

        // TODO 发起接口调用

        // 模拟解析接口返回XML
        // TODO 解析正式接口返回XML
        $xml              = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/pata_domestic_price_sfc.xml'));
        $itinTotalFares = $xml->PricedItineraries->PricedItinerary->AirItineraryPricingInfo->ItinTotalFare;
        $arrFares        = [];

        foreach ($itinTotalFares as $itinTotalFare) {
            $taxes = [];
            foreach ($itinTotalFare->Taxes->Tax as $tax) {
                $taxes[(string)$tax['TaxCode']] = [
                    'amount'        => (string)$tax['Amount'],
                    'currency_code' => (string)$tax['CurrencyCode'],
                ];
            }
            $arrFares[] = [
                'rph'             => (string)$itinTotalFare['RPH'],
                'amount'          => (string)$itinTotalFare->BaseFare['Amount'],
                'currency_code'   => (string)$itinTotalFare->BaseFare['CurrencyCode'],
                'taxes'           => $taxes,
                'fare_basis_code' => (string)$itinTotalFare->FareBasisCodes->FareBasisCode,
            ];
        }

        // 模拟数据
        $db       = db_connect();
        $pnrInfo = $db->table('pnr')->select('id')->where('pnr', $params['pnr'])->get()->getRowArray();
        if (empty($pnrInfo)) {
            return [];
        }
        $flightList = $db->table('pnr_segments')->where('pnr_id', $pnrInfo['id'])->orderBy('id', 'asc')->get()->getResultArray();
        if (empty($flightList)) {
            return [];
        }
        $arrFares = [];
        $tmpFare  = [];
        foreach ($flightList as $item) {
            $airline       = $item['airline'];
            $flightNumber = substr($item['flight_number'], 2, 4);
            $cabin         = $item['cabin'];

            $flight = $db->table('flights')->where('flight_number', $airline . $flightNumber)->get()->getRowArray();
            if (empty($flight)) {
                return [];
            }

            $cabinInfo = $db->query("select * from cabin_price where airline = " . $db->escape($airline) . " 
                and (
                    (airport_1 = " . $db->escape($flight['departure_airport']) . " and airport_2 = " . $db->escape($flight['arrival_airport']) . ")
                    or
                    (airport_1 = " . $db->escape($flight['arrival_airport']) . " and airport_2 = " . $db->escape($flight['departure_airport']) . ")
                )
                and cabin = " . $db->escape($cabin))->getRowArray();
            if (empty($cabinInfo)) {
                return [];
            }
            $price = $cabinInfo['price'];
            if ($params['passenger_type'] == 'CHD') {
                $price = $price * 0.5;
            } else if ($params['passenger_type'] == 'INF') {
                $price = $price * 0.1;
            }

            $tmpFare[] = [
                'cabin' => $cabin,
                'price' => $price,
            ];
        }
        $totalAmount = 0;
        $fbc          = '';
        $totalTaxCn = 0;
        $totalTaxYq = 0;
        foreach ($tmpFare as $v) {
            $totalAmount += $v['price'];
            if (empty($fbc)) {
                $fbc = $v['cabin'];
            } else {
                $fbc .= '+' . $v['cabin'];
            }
            $totalTaxCn += 50;
            $totalTaxYq += 20;
        }

        $arrFares = [
            0 => [
                'rph'             => 1,
                'amount'          => $totalAmount,
                'currency_code'   => 'CNY',
                'taxes'           => [
                    'CN' => [
                        'amount'        => $totalTaxCn,
                        'currency_code' => 'CNY',
                    ],
                    'YQ' => [
                        'amount'        => $totalTaxYq,
                        'currency_code' => 'CNY',
                    ],
                ],
                'fare_basis_code' => $fbc,
            ],
        ];

        return $arrFares;
    }

    // 按PNR查询国内改签价格
    // 请求参数：
    // [
    //        'airline' => 'MU', // 航空公司
    //        'ticket_number_before' => '999-5785450339', // 原电子票号
    //        'is_infant' => false, // 是否婴儿
    //        'passenger_name' => 'ZHANG/JIANXIN', // 乘客姓名
    //        'involuntary_identifier' => false, // 是否非自愿改签
    //        'pnr' => 'L12345', // PNR
    //        'flights' => [ // 多条航段
    //            0 => [ // 航段
    //                'departure_airport' => 'XMN', // 出发机场
    //                'arrival_airport' => 'SHA', // 到达机场
    //                'air_equip_type' => '744', // 机型
    //                'marketing_airline' => 'MU', // 市场航空公司
    //                'operation_airline' => 'MU', // 执飞航空公司
    //                'flight_number' => 'MU1234', // 航班号
    //                'departure_time' => '2017-09-29 07:55', // 起飞时间
    //                'arrival_time' => '2017-09-29 10:55', // 到达时间
    //                'type' => 'NORMAL', // 航段状态
    //                'cabin' => 'F', // 舱位
    //                'action_code' => 'HK', // 行动代码
    //            ],
    //        ]
    //    ];
    //
    // 返回数据：
    //    [ // 多条价格数据
    //        [0] => // 价格数据
    //            [
    //                [changed_fee] => 1 // 计算得到的改期费
    //                [fare_diff] => 2 // 实际收取票价差
    //                [tax_diff] => 3 // 实际收取税差
    //                [total_fare] => 4 // 总费用
    //                [exchange_type] => 0 // OI方式标识 1出新票 0不出新票
    //                [exchange_fee] => 0 // 实际收取改期费
    //            ]
    //        [1] => 
    //            [
    //                [changed_fee] => 10
    //                [fare_diff] => 20
    //                [tax_diff] => 3
    //                [total_fare] => 40
    //                [exchange_type] => 1
    //                [exchange_fee] => 0
    //            ]
    //    ]
    public function query_domestic_reissue_price_by_pnr($params)
    {
        $requestXml = '<OTA_AirReIssuePriceRQ>
                <POS>
                    <Source>
                        <airlinecode>' . $params['airline'] . '</airlinecode>
                        <iatacode>' . $this->ibe_config->iata_code . '</iatacode>
                        <CityCode>' . $this->ibe_config->city . '</CityCode>
                        <PseudoCityCode>' . $this->ibe_config->office . '</PseudoCityCode>
                    </Source>
                </POS>
                <ReIssueinfos>
                    <ReIssueinfo>
                        <FlightSegments>';
        foreach ($params['flights'] as $flight) {
            $requestXml .= '
                            <FlightSegment>
                                <departure LocationCode="' . $flight['departure_airport'] . '" />
                                <arrival LocationCode="' . $flight['arrival_airport'] . '" />
                                <equipment AirEquipType="' . $flight['air_equip_type'] . '" />
                                <marketingCarrier Code="' . $flight['marketing_airline'] . '" />
                                <operationCarrier Code="' . $flight['operation_airline'] . '" />
                                <departureTime>' . $flight['departure_time'] . '</departureTime>
                                <arrivalTime>' . $flight['arrival_time'] . '</arrivalTime>
                                <type>' . $flight['type'] . '</type>
                                <flightno>' . $flight['flight_number'] . '</flightno>
                                <cabin>' . $flight['cabin'] . '</cabin>
                                <actionCode>' . $flight['action_code'] . '</actionCode>
                            </FlightSegment>';
        }
        $requestXml .= '
                        </FlightSegments>
                        <TicketNumber_Before>' . $params['ticket_number_before'] . '</TicketNumber_Before>
                        <IsInfant>' . ($params['is_infant'] ? "true" : "false") . '</IsInfant>
                        <PassName>' . $params['passenger_name'] . '</PassName>
                        <InvoluntaryIdentifier>' . ($params['involuntary_identifier'] ? "true" : "false") . '</InvoluntaryIdentifier>
                        <BookingReferenceID ID="' . $params['pnr'] . '" ID_Context="PNR" />
                    </ReIssueinfo>
                </ReIssueinfos>
            </OTA_AirReIssuePriceRQ>';

        // TODO 发起接口调用

        // 模拟解析接口返回XML
        // TODO 解析正式接口返回XML
        $xml         = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/query_domestic_reissue_price_by_pnr.xml'));
        $results     = $xml->TicketChangerResponse->reissueResult;
        $arrReissue = [];
        foreach ($results as $result) {
            $arrReissue[] = [
                'changed_fee'   => (float)$result->changedFee,
                'fare_diff'     => (float)$result->farediff,
                'tax_diff'      => (float)$result->taxdiff,
                'total_fare'    => (float)$result->Totalfare,
                'exchange_type' => (string)$result->exchangeType,
                'exchange_fee'  => (float)$result->exchangeFee,
            ];
        }

        return $arrReissue;
    }

    public function query_intl_reissue_price_by_pnr($params)
    {
        $requestXml = '<?xml version="1.0" encoding="UTF-8"?>
                <TES_AirTicketChangerInterRQ>
                    <POS>
                        <Source PseudoCityCode="' . $this->ibe_config->office . '" CityCode="' . $this->ibe_config->city . '" IataCode="' . $this->ibe_config->iata_code . '" ChannelID="CRS"
                            PayType="CASH" />
                    </POS>
                    <TransactionCommand>
                        <TicketNo>' . $params['ticket_number'] . '</TicketNo>
                        <ExchangeAirline>' . $params['exchange_airline'] . '</ExchangeAirline>
                        <PassengerName>' . $params['passenger_name'] . '</PassengerName>
                        <PassengerID>' . $params['passenger_id'] . '</PassengerID>
                        <InvoluntaryIdentifier>' . ($params['involuntary_identifier'] ? "true" : "false") . '</InvoluntaryIdentifier>
                        <!-- 新预定的 PNR -->
                        <BookingReferenceID ID="' . $params['pnr'] . '" ID_Context="PNR" />
                        <Endorsement>' . ($params['endorsement'] ? "true" : "false") . '</Endorsement>
                        <PassengerCount>' . $params['passenger_count'] . '</PassengerCount>
                    </TransactionCommand>
                    <AirItinerary>
                        <OriginDestinationOptions>
                            <OriginDestinationOption>';
        foreach ($params['flights'] as $flight) {
            $requestXml .= '
                                <FlightSegment RPH="1" DepartureDateTime="' . $flight['departure_datetime'] . '"
                                    ArrivalDateTime="' . $flight['arrival_datetime'] . '" CodeshareInd="' . $flight['code_share_ind'] . '" FlightNumber="' . $flight['flight_number'] . '"
                                    NumberInParty="' . $flight['passenger_number'] . '" Status="' . $flight['action_code'] . '"
                                    SegmentType="' . $flight['type'] . '">
                                    <OperatingAirline Code="' . $flight['operating_airline'] . '" FlightNumber="' . $flight['flight_number'] . '" />
                                    <DepartureAirport LocationCode="' . $flight['departure_airport'] . '" />
                                    <ArrivalAirport LocationCode="' . $flight['arrival_airport'] . '" />
                                    <Equipment AirEquipType="' . $flight['air_equip_type'] . '" />
                                    <MarketingAirline Code="' . $flight['marketing_airline'] . '" />
                                    <BookingClassAvail ResBookDesigCode="' . $flight['marketing_airline'] . '" />
                                </FlightSegment>';
        }
        $requestXml .= '
                            </OriginDestinationOption>
                        </OriginDestinationOptions>
                    </AirItinerary>
                    <FareskyParameters Changeable="true" Refundable="true" Currency="CNY" Waiver="NORMAL">
                        <FareTypes>
                            <FareType>PUBLIC</FareType>
                            <FareType>PRIVATE</FareType>
                            <FareType>IT_PUBLIC</FareType>
                            <FareType>IT_PRIVATE</FareType>
                        </FareTypes>
                                <!--AccountCode>
                                <AllowBlank>true</AllowBlank>
                                <Code>xxxx</Code>
                                </AccountCode-->
                        <PassengerTypeInfo>
                            <Type>ADT</Type>
                            <ExactPtcMatch>true</ExactPtcMatch>
                        </PassengerTypeInfo>
                    </FareskyParameters>
                    <PriceVerification>
                        <Fares>
                            <Fare>
                                <RuleTariff>004</RuleTariff>
                                <FareDataForReprice>10000::0::QR
                                    ::HKG::AMS::004::RY07::YLR6R1FO::YLR6R1FO::HKD::1::EH:: ::0002:: :: :: :: :: ::
                                    :: :: :: :: :: :: ::::1129.00::7230::CNY::::************::ATPCO:: :: :: :: ::
                                    ::ATPCO</FareDataForReprice>
                                <FareDataForDiagnostics>::::::::::::::HKG::AMS::8770::HKD::7230::CNY::1129.00::::::::::::::::::::IPREUAS::OUTBOUND::::::::::::ER
                                    :: ::
                                    ::21Jul21::EH::false::::::::::::::::::5763::::004::::::::::::::2021-07-21::::ATPCO::0087512::
                                    ::ECOMFORT::CFFQR::QR OTA BRANDING</FareDataForDiagnostics>
                                <Tier>0003</Tier>
                                <BrandName>ECONOMY COMFORT </BrandName>
                                <PrimeRbds>
                                    <Rbd>Y</Rbd>
                                </PrimeRbds>
                                <CoveredSegments>
                                    <Id>1</Id>
                                </CoveredSegments>
                            </Fare>
                        </Fares>
                    </PriceVerification>
                </TES_AirTicketChangerInterRQ>';
        echo $requestXml;
    }
}