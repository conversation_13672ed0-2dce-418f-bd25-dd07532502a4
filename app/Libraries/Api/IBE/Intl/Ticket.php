<?php

namespace App\Libraries\Api\IBE\Intl;

use App\Models\PnrPassengerModel;

/**
 * 国际机票出票服务类
 *
 * 提供国际机票的自动出票和BOP出票功能
 */
class Ticket extends BaseIntlService
{
    /**
     * 默认打印机编号
     */
    private const DEFAULT_PRINTER_NUMBER = '36';

    /**
     * BOP打印机编号
     */
    private const BOP_PRINTER_NUMBER = '8';

    /**
     * 默认行李额度
     */
    private const DEFAULT_BAGGAGE_ALLOWANCE = '10KG';

    /**
     * 支持的支付类型
     */
    private const PAYMENT_TYPES = ['CASH', 'CREDIT', 'DEBIT', 'CHECK'];

    /**
     * 票据状态
     */
    private const TICKET_STATUS = [
        'OPEN_FOR_USE' => 'OPEN FOR USE',
        'USED' => 'USED',
        'VOID' => 'VOID',
        'REFUNDED' => 'REFUNDED'
    ];

    /**
     * 国际自动出票（新版）
     *
     * @param array $params 出票参数包含：
     *        - pnr: PNR编码
     *        - airline: 航空公司代码
     *        - pnr_infos: 乘客信息数组
     * @param array $options 可选参数：
     *        - printer_number: 打印机编号
     *        - baggage_allowance: 行李额度
     *        - payment_type: 支付类型
     *
     * @return array 出票结果
     * @throws \InvalidArgumentException
     * @throws \Exception
     */
    public function issuingTicket(array $params, array $options = []): array
    {
        try {
            // 验证参数
            $this->validateTicketParams($params);

            // 构建请求XML
            $requestXml = $this->buildTicketRequestXml($params, $options);

            // 发起接口调用
            $responseData = $this->sendRequest($requestXml, $options);

            // 解析响应数据
            return $this->parseResponse($responseData);

        } catch (\Exception $e) {
            // 记录错误
            $this->errors[] = $e->getMessage();

            // 返回错误响应，但保持原有数据结构
            return $this->handleTicketError($e, $params);
        }
    }

    /**
     * 验证出票参数
     *
     * @param array $params
     * @throws \InvalidArgumentException
     */
    private function validateTicketParams(array $params): void
    {
        // 验证必填字段
        $requiredFields = ['pnr', 'airline', 'pnr_infos'];
        $this->validateRequiredParams($params, $requiredFields);

        // 验证PNR格式
        if (!preg_match('/^[A-Z0-9]{6}$/', $params['pnr'])) {
            throw new \InvalidArgumentException('PNR格式错误，应为6位字母数字组合');
        }

        // 验证航空公司代码
        if (!preg_match('/^[A-Z]{2}$/', $params['airline'])) {
            throw new \InvalidArgumentException('航空公司代码格式错误，应为2位大写字母');
        }

        // 验证乘客信息
        if (empty($params['pnr_infos']) || !is_array($params['pnr_infos'])) {
            throw new \InvalidArgumentException('乘客信息不能为空');
        }

        foreach ($params['pnr_infos'] as $index => $info) {
            $requiredInfoFields = ['rph', 'passenger_type', 'amount', 'tax', 'person_name'];
            foreach ($requiredInfoFields as $field) {
                if (!isset($info[$field])) {
                    throw new \InvalidArgumentException("乘客{$index}缺少必填字段: {$field}");
                }
            }

            // 验证乘客类型
            if (!isset(PnrPassengerModel::INTL_PASSENGER_MAP[$info['passenger_type']])) {
                throw new \InvalidArgumentException("乘客{$index}类型无效: {$info['passenger_type']}");
            }

            // 验证金额格式
            if (!is_numeric($info['amount']) || $info['amount'] < 0) {
                throw new \InvalidArgumentException("乘客{$index}票价金额格式错误");
            }

            if (!is_numeric($info['tax']) || $info['tax'] < 0) {
                throw new \InvalidArgumentException("乘客{$index}税费金额格式错误");
            }
        }
    }

    /**
     * 构建出票请求XML
     *
     * @param array $params
     * @param array $options
     * @return string
     */
    private function buildTicketRequestXml(array $params, array $options): string
    {
        $printerNumber = $options['printer_number'] ?? self::DEFAULT_PRINTER_NUMBER;
        $baggageAllowance = $options['baggage_allowance'] ?? self::DEFAULT_BAGGAGE_ALLOWANCE;

        $xml = '
<TES_AirfareAutoTicketRQ>
    <SITA_AirfarePriceRQ>
        <OTA_AirPriceRQ>';

        // 添加POS信息
        $xml .= $this->buildPOSXml();

        $xml .= '
            <TravelerInfoSummary>
                <AirTravelerAvail>';

        // 添加乘客类型信息
        foreach ($params['pnr_infos'] as $info) {
            $passengerTypeCode = PnrPassengerModel::INTL_PASSENGER_MAP[$info['passenger_type']];
            $xml .= '<PassengerTypeQuantity Code="' . $this->escapeXml($passengerTypeCode) . '" />';
        }

        $xml .= '
                </AirTravelerAvail>
                <PriceRequestInformation CurrencyCode="CNY" NegotiatedFaresOnly="false">
                    <NegotiatedFareCode Code="" />
                </PriceRequestInformation>
            </TravelerInfoSummary>
            <BookingReferenceID ID="' . $this->escapeXml($params['pnr']) . '">
            </BookingReferenceID>
        </OTA_AirPriceRQ>
        <AdditionalPriceRQData>
            <TicketingCarrier Code="' . $this->escapeXml($params['airline']) . '" />
        </AdditionalPriceRQData>
    </SITA_AirfarePriceRQ>
    <FareInfos>';

        // 添加运价信息
        foreach ($params['pnr_infos'] as $info) {
            $xml .= '
        <FareInfo RPH="' . $this->escapeXml($info['rph']) . '">
            <PassengerTypeQuantity Code="' . $this->escapeXml(PnrPassengerModel::INTL_PASSENGER_MAP[$info['passenger_type']]) . '" />
            <BaseFare Amount="' . $this->escapeXml($info['amount']) . '" CurrencyCode="CNY" />
            <TotalTax Amount="' . $this->escapeXml($info['tax']) . '" CurrencyCode="CNY" />
            <FareBasisCodes>
                <FareBasisCode></FareBasisCode>
            </FareBasisCodes>
            <BaggageInfo>
                <Baggage BaggageSegIndex="1" FreeBaggageAllowance="' . $this->escapeXml($baggageAllowance) . '" />
            </BaggageInfo>
            <Commission Percent="" />
        </FareInfo>';
        }

        $xml .= '
    </FareInfos>
    <DemandTicketDetail ReturnTicketInfoInd="true" NeedEtryInd="true" ReconfirmSegmentInd="false" BSPInd="true">
        <PrinterNumber>' . $this->escapeXml($printerNumber) . '</PrinterNumber>';

        // 添加乘客姓名信息
        foreach ($params['pnr_infos'] as $info) {
            $xml .= '
        <PassengerName>
            <fareRPH>' . $this->escapeXml($info['rph']) . '</fareRPH>
            <Surname>' . $this->escapeXml($info['person_name']) . '</Surname>
        </PassengerName>';
        }

        $xml .= '
    </DemandTicketDetail>
</TES_AirfareAutoTicketRQ>';

        return $xml;
    }

    /**
     * 处理出票错误
     *
     * @param \Exception $e
     * @param array $params
     * @return array
     */
    private function handleTicketError(\Exception $e, array $params): array
    {
        // 记录错误日志
        log_message('error', '出票失败: ' . $e->getMessage());

        // 返回空结果但保持数据结构
        return [
            'pnr' => $params['pnr'] ?? '',
            'tickets' => [],
            'error' => $e->getMessage(),
        ];
    }

    /**
     * 发送HTTP请求
     *
     * @param string $requestData
     * @param array $options
     * @return string
     */
    protected function sendRequest(string $requestData, array $options = []): string
    {
        // TODO: 实现真实的HTTP请求
        // 这里暂时返回模拟数据

        // 根据请求类型返回不同的模拟数据
        if (strpos($requestData, 'DPayPassword') !== false) {
            // BOP出票请求
            return file_get_contents(ROOTPATH . 'writable/xml/international/issue_bop_ticket.xml');
        } else {
            // 普通出票请求
            return file_get_contents(ROOTPATH . 'writable/xml/international/issue_ticket.xml');
        }
    }

    /**
     * 解析响应数据
     *
     * @param string $responseData
     * @return array
     */
    protected function parseResponse(string $responseData): array
    {
        try {
            // 解析XML响应
            $xml = $this->parseXmlSafely($responseData);

            // 解析出票响应
            return $this->parseTicketResponse($xml);

        } catch (\Exception $e) {
            // 记录错误
            $this->errors[] = $e->getMessage();

            // 返回空结果但保持数据结构
            return [
                'pnr' => '',
                'tickets' => [],
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * 解析出票响应
     *
     * @param \SimpleXMLElement $xml
     * @return array
     */
    private function parseTicketResponse(\SimpleXMLElement $xml): array
    {
        $returnData = [];
        $returnData['pnr'] = (string)$xml->BookingReferenceID['ID'];
        $returnData['tickets'] = [];

        foreach ($xml->TicketItemInfo as $ticket) {
            $arrFlightSegment = [];
            foreach ($ticket->FlightSegment as $flightSegment) {
                $arrFlightSegment[] = [
                    'departure_datetime' => (string)$flightSegment['DepartureDateTime'],
                    'ticket_status' => (string)$flightSegment['TicketStatus'],
                    'airline' => (string)$flightSegment->OperatingAirline['Code'],
                    'flight_number' => (string)$flightSegment['FlightNumber'],
                    'departure_airport' => (string)$flightSegment->DepartureAirport['LocationCode'],
                    'arrival_airport' => (string)$flightSegment->ArrivalAirport['LocationCode'],
                    'marketing_airline' => (string)$flightSegment->MarketingAirline['Code'],
                ];
            }

            $returnData['tickets'][] = [
                'ticket_number' => (string)$ticket['TicketNumber'],
                'total_amount' => (string)$ticket['TotalAmount'],
                'currency_code' => (string)$ticket['CurrencyCode'],
                'payment_type' => (string)$ticket['PaymentType'],
                'passenger_type' => (string)$ticket->PassengerName['PassengerTypeCode'],
                'passenger_surname' => (string)$ticket->PassengerName->Surname,
                'flight_segments' => $arrFlightSegment,
            ];
        }

        return $returnData;
    }

    /**
     * 获取模拟出票数据（保持向后兼容）
     *
     * @param array $params
     * @return array
     */
    private function getSimulatedTicketData(array $params): array
    {
        $db = db_connect();
        $pnrInfo = $db->table('pnr')->select('order_id')->where('pnr', $params['pnr'])->get()->getRowArray();

        if (empty($pnrInfo)) {
            return [];
        }

        $passengerList = $db->table('ticket_book_pax')
            ->where('order_id', $pnrInfo['order_id'])
            ->orderBy('id', 'asc')
            ->get()
            ->getResultArray();

        if (empty($passengerList)) {
            return [];
        }

        $flightList = $db->table('ticket_book_seg')
            ->where('order_id', $pnrInfo['order_id'])
            ->orderBy('id', 'asc')
            ->get()
            ->getResultArray();

        if (empty($flightList)) {
            return [];
        }

        $arrFlight = [];
        foreach ($flightList as $item) {
            $flight = $db->table('flights')->where('flight_number', $item['flight_number'])->get()->getRowArray();
            if (empty($flight)) {
                return [];
            }

            $arrFlight[] = [
                'departure_datetime' => $item['departure_datetime'],
                'ticket_status' => self::TICKET_STATUS['OPEN_FOR_USE'],
                'airline' => $item['airline'],
                'flight_number' => $item['flight_number'],
                'departure_airport' => $flight['departure_airport'],
                'arrival_airport' => $flight['arrival_airport'],
            ];
        }

        $arrPassenger = [];
        $arrPassengerType = [
            1 => 'ADT',
            2 => 'CNN',
            3 => 'INF',
        ];

        foreach ($passengerList as $passenger) {
            $arrPassenger[] = [
                'ticket_number' => mt_rand(100, 999) . '-' . mt_rand(1000000000, 9999999999),
                'total_amount' => 1000,
                'currency_code' => 'CNY',
                'payment_type' => 'CASH',
                'passenger_type' => $arrPassengerType[$passenger['passenger_type']],
                'passenger_surname' => $passenger['person_name'],
                'flight_segments' => $arrFlight,
            ];
        }

        return [
            'pnr' => $params['pnr'],
            'tickets' => $arrPassenger,
        ];
    }

    /**
     * BOP出票接口
     *
     * @param array $params 出票参数
     * @param array $options 可选参数
     * @return array 出票结果
     */
    public function issuingBOPTicket(array $params, array $options = []): array
    {
        try {
            // 验证参数
            $this->validateTicketParams($params);

            // 构建BOP出票请求XML
            $requestXml = $this->buildBOPTicketRequestXml($params, $options);

            // 记录请求日志
            $this->logRequest('issuingBOPTicket', $params, $requestXml);

            // 发起接口调用
            $responseData = $this->sendRequest($requestXml, array_merge($options, ['is_bop' => true]));

            // 解析响应数据
            return $this->parseResponse($responseData);

        } catch (\Exception $e) {
            // 记录错误
            $this->errors[] = $e->getMessage();

            // 返回错误响应，但保持原有数据结构
            return $this->handleTicketError($e, $params);
        }
    }

    /**
     * 构建BOP出票请求XML
     *
     * @param array $params
     * @param array $options
     * @return string
     */
    private function buildBOPTicketRequestXml(array $params, array $options): string
    {
        $printerNumber = $options['printer_number'] ?? self::BOP_PRINTER_NUMBER;
        $baggageAllowance = $options['baggage_allowance'] ?? self::DEFAULT_BAGGAGE_ALLOWANCE;
        $paymentType = $options['payment_type'] ?? 'CASH';
        $dPayPassword = $options['dpay_password'] ?? '123456';

        $xml = '
<TES_AirfareAutoTicketRQ>
    <SITA_AirfarePriceRQ>
        <OTA_AirPriceRQ>';

        // 添加POS信息
        $xml .= $this->buildPOSXml();

        $xml .= '
            <TravelerInfoSummary>
                <AirTravelerAvail>';

        // 添加乘客类型信息
        foreach ($params['pnr_infos'] as $info) {
            $passengerTypeCode = PnrPassengerModel::INTL_PASSENGER_MAP[$info['passenger_type']];
            $xml .= '<PassengerTypeQuantity Code="' . $this->escapeXml($passengerTypeCode) . '" />';
        }

        $xml .= '
                </AirTravelerAvail>
                <PriceRequestInformation CurrencyCode="CNY" />
            </TravelerInfoSummary>
            <BookingReferenceID ID="' . $this->escapeXml($params['pnr']) . '"></BookingReferenceID>
        </OTA_AirPriceRQ>
        <AdditionalPriceRQData>
            <TicketingCarrier Code="' . $this->escapeXml($params['airline']) . '" />
        </AdditionalPriceRQData>
    </SITA_AirfarePriceRQ>
    <FareInfos>';

        // 添加运价信息（BOP特有字段）
        foreach ($params['pnr_infos'] as $info) {
            $xml .= '
        <FareInfo RPH="' . $this->escapeXml($info['rph']) . '">
            <PassengerTypeQuantity Code="' . $this->escapeXml(PnrPassengerModel::INTL_PASSENGER_MAP[$info['passenger_type']]) . '" />
            <BaseFare Amount="' . $this->escapeXml($info['amount']) . '" CurrencyCode="CNY" />
            <TotalTax Amount="' . $this->escapeXml($info['tax']) . '" CurrencyCode="CNY" />
            <FareBasisCodes>
                <FareBasisCode></FareBasisCode>
            </FareBasisCodes>
            <BaggageInfo>
                <Baggage BaggageSegIndex="1" FreeBaggageAllowance="' . $this->escapeXml($baggageAllowance) . '" />
            </BaggageInfo>
            <Commission Percent="" />
            <EIInfo EI="V00C100 NONE ENDSORES PENALTY APPLYS CHG FEE CNY500"/>
            <PaymentInfo PaymentType="' . $this->escapeXml($paymentType) . '" Text="test" CurrencyCode="CNY"/>
        </FareInfo>';
        }

        $xml .= '
    </FareInfos>
    <DemandTicketDetail ReturnTicketInfoInd="true" NeedEtryInd="true" ReconfirmSegmentInd="true" DPayPassword="' . $this->escapeXml($dPayPassword) . '">
        <PrinterNumber>' . $this->escapeXml($printerNumber) . '</PrinterNumber>';

        // 添加乘客姓名信息
        foreach ($params['pnr_infos'] as $info) {
            $xml .= '
        <PassengerName>
            <fareRPH>' . $this->escapeXml($info['rph']) . '</fareRPH>
            <Surname>' . $this->escapeXml($info['person_name']) . '</Surname>
        </PassengerName>';
        }

        $xml .= '
    </DemandTicketDetail>
</TES_AirfareAutoTicketRQ>';

        return $xml;
    }

    /**
     * 增强出票数据
     *
     * @param array $ticketData
     * @return array
     */
    private function enhanceTicketData(array $ticketData): array
    {
        foreach ($ticketData['tickets'] as &$ticket) {
            // 添加票据状态描述
            if (!isset($ticket['status_description'])) {
                $ticket['status_description'] = $this->getTicketStatusDescription($ticket['ticket_status'] ?? '');
            }

            // 添加格式化的票号
            if (!isset($ticket['formatted_ticket_number'])) {
                $ticket['formatted_ticket_number'] = $this->formatTicketNumber($ticket['ticket_number']);
            }

            // 添加总价格格式化
            if (!isset($ticket['formatted_total_amount'])) {
                $ticket['formatted_total_amount'] = number_format((float)$ticket['total_amount'], 2);
            }

            // 添加货币符号
            if (!isset($ticket['currency_symbol'])) {
                $ticket['currency_symbol'] = $this->getCurrencySymbol($ticket['currency_code']);
            }
        }

        return $ticketData;
    }

    /**
     * 获取票据状态描述
     *
     * @param string $status
     * @return string
     */
    private function getTicketStatusDescription(string $status): string
    {
        $descriptions = [
            'OPEN FOR USE' => '有效未使用',
            'USED' => '已使用',
            'VOID' => '已作废',
            'REFUNDED' => '已退票',
        ];

        return $descriptions[$status] ?? $status;
    }

    /**
     * 格式化票号
     *
     * @param string $ticketNumber
     * @return string
     */
    private function formatTicketNumber(string $ticketNumber): string
    {
        // 如果票号包含连字符，保持原格式
        if (strpos($ticketNumber, '-') !== false) {
            return $ticketNumber;
        }

        // 否则按标准格式添加连字符
        if (strlen($ticketNumber) >= 10) {
            return substr($ticketNumber, 0, 3) . '-' . substr($ticketNumber, 3);
        }

        return $ticketNumber;
    }

    /**
     * 获取货币符号
     *
     * @param string $currencyCode
     * @return string
     */
    private function getCurrencySymbol(string $currencyCode): string
    {
        $symbols = [
            'CNY' => '¥',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
        ];

        return $symbols[$currencyCode] ?? $currencyCode;
    }

    /**
     * 获取出票历史
     *
     * @param array $filters
     * @return array
     */
    public function getTicketHistory(array $filters = []): array
    {
        $history = [];

        foreach ($this->requestLogs as $log) {
            if (in_array($log['method'], ['issuingTicket', 'issuingBOPTicket'])) {
                $historyItem = [
                    'ticket_time' => $log['timestamp'],
                    'ticket_type' => $log['method'],
                    'ticket_params' => $log['params'],
                    'ticket_id' => md5($log['timestamp'] . json_encode($log['params'])),
                ];

                // 应用过滤条件
                if ($this->matchesTicketFilters($historyItem, $filters)) {
                    $history[] = $historyItem;
                }
            }
        }

        return $history;
    }

    /**
     * 检查出票历史记录是否匹配过滤条件
     *
     * @param array $item
     * @param array $filters
     * @return bool
     */
    private function matchesTicketFilters(array $item, array $filters): bool
    {
        foreach ($filters as $key => $value) {
            if ($key === 'ticket_type' && $item['ticket_type'] !== $value) {
                return false;
            }

            if ($key === 'pnr' && isset($item['ticket_params']['pnr']) && $item['ticket_params']['pnr'] !== $value) {
                return false;
            }

            if ($key === 'date_from' && strtotime($item['ticket_time']) < strtotime($value)) {
                return false;
            }

            if ($key === 'date_to' && strtotime($item['ticket_time']) > strtotime($value)) {
                return false;
            }
        }

        return true;
    }
}