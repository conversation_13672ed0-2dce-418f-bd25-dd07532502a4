<?php

namespace App\Libraries\Api\IBE\Intl;

use App\Helpers\Tools\Time;

/**
 * 国际机票改签接口类
 *
 * 提供国际机票改签相关的API接口功能
 */
class Rebook
{
    protected $ibe_config;

    public function __construct()
    {
        $this->ibe_config = config('IBE');
    }

    /**
     * 改签搜索接口
     *
     * @param  array  $params  改签搜索参数
     *
     * @return array 改签搜索结果
     * @throws \Exception
     */
    public function rebookSearchFlight(array $params, $index): array
    {
        // 构建请求XML
        $requestXml = $this->buildRebookSearchXml($params);

        // TODO: 发起接口调用
        // $response = $this->sendRequest($requestXml);

        // TODO 轮流读取XML文件进行解析
        if ($index % 2 == 0) {
            $responseXml = file_get_contents(ROOTPATH . 'writable/xml/international/rebook_search_res.xml');
        } else {
            $responseXml = file_get_contents(ROOTPATH . 'writable/xml/international/rebook_search_res2.xml');
        }

        $responseData = $this->parseRebookSearchResponse($responseXml);

        return $responseData;
    }

    /**
     * 构建改签搜索请求XML
     *
     * @param  array  $params  请求参数
     *
     * @return string XML字符串
     */
    private function buildRebookSearchXml(array $params): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<TRR_VoluntaryReshopRQ>' . "\n";

        // 票号信息
        $xml .= '    <TicketInfo>' . "\n";
        $xml .= '        <TicketNo>' . $params['ticket_number'] . '</TicketNo>' . "\n";
        $xml .= '        <PassengerName>' . $params['person_name'] . '</PassengerName>' . "\n";
        $xml .= '        <ValidatingCarrier>' . $params['airline'] . '</ValidatingCarrier>' . "\n";
        $xml .= '    </TicketInfo>' . "\n";

        // POS信息
        $xml .= '    <POS>' . "\n";
        $xml .= '        <HostCode>1E</HostCode>' . "\n";
        $xml .= '        <TicketingCity>' . $this->ibe_config->city . '</TicketingCity>' . "\n";
        $xml .= '        <DepartmentCode> </DepartmentCode>' . "\n";
        $xml .= '        <OfficeCode>' . $this->ibe_config->office . '</OfficeCode>' . "\n";
        $xml .= '        <IataCode>' . $this->ibe_config->iata_code . '</IataCode>' . "\n";
        $xml .= '    </POS>' . "\n";

        // 原航段信息 - 包含订单下所有可改签航段
        $xml .= '    <Segments>' . "\n";
        if (isset($params['segments']) && is_array($params['segments'])) {
            foreach ($params['segments'] as $index => $segment) {
                $flightId = str_pad($index + 1, 2, '0', STR_PAD_LEFT); // 01, 02, 03...
                $xml      .= '        <Segment>' . "\n";
                $xml      .= '            <FlightId>' . $flightId . '</FlightId>' . "\n";
                $xml      .= '            <MarketingCarrier>' . $segment['marketing_carrier'] . '</MarketingCarrier>' . "\n";
                $xml      .= '            <FlightNo>' . $segment['flight_no'] . '</FlightNo>' . "\n";
                $xml      .= '            <CabinCode>' . $segment['cabin_code'] . '</CabinCode>' . "\n";
                $xml      .= '            <DepartureDateTime>' . $this->formatDateTime($segment['departure_datetime']) . '</DepartureDateTime>' . "\n";
                $xml      .= '            <Equipment>' . $segment['equipment'] . '</Equipment>' . "\n";
                $xml      .= '            <ArrivalDateTime>' . $this->formatDateTime($segment['arrival_datetime']) . '</ArrivalDateTime>' . "\n";
                $xml      .= '            <DepartureCity>' . $segment['departure_city'] . '</DepartureCity>' . "\n";
                $xml      .= '            <ArrivalCity>' . $segment['arrival_city'] . '</ArrivalCity>' . "\n";
                $xml      .= '            <BookingStatus>' . $segment['booking_status'] . '</BookingStatus>' . "\n";
                $xml      .= '        </Segment>' . "\n";
            }
        }
        $xml .= '    </Segments>' . "\n";

        // 新航段信息 - 要变更的航段信息
        $xml .= '    <NewSegments>' . "\n";
        if (isset($params['new_segments']) && is_array($params['new_segments'])) {
            foreach ($params['new_segments'] as $newSegment) {
                $xml .= '        <NewSegment>' . "\n";
                $xml .= '            <Origin>' . "\n";
                $xml .= '                <Name>' . $newSegment['origin'] . '</Name>' . "\n";
                $xml .= '                <Type>AIRPORT</Type>' . "\n";
                $xml .= '            </Origin>' . "\n";
                $xml .= '            <Destination>' . "\n";
                $xml .= '                <Name>' . $newSegment['destination'] . '</Name>' . "\n";
                $xml .= '                <Type>AIRPORT</Type>' . "\n";
                $xml .= '            </Destination>' . "\n";
                $xml .= '            <DepartDate>' . $newSegment['depart_date'] . '</DepartDate>' . "\n";
                $xml .= '            <CoveredSegments>' . "\n";
                // CoveredSegments包含要覆盖的原航段FlightId
                if (isset($newSegment['covered_segments']) && is_array($newSegment['covered_segments'])) {
                    foreach ($newSegment['covered_segments'] as $flightId) {
                        $xml .= '                <FlightId>' . str_pad($flightId, 2, '0', STR_PAD_LEFT) . '</FlightId>' . "\n";
                    }
                }
                $xml .= '            </CoveredSegments>' . "\n";
                $xml .= '        </NewSegment>' . "\n";
            }
        }
        $xml .= '    </NewSegments>' . "\n";

        $xml .= '</TRR_VoluntaryReshopRQ>';

        return $xml;
    }

    /**
     * 解析改签搜索响应
     *
     * @param  string  $responseXml  响应XML字符串
     *
     * @return array 解析后的数据
     * @throws \Exception
     */
    private function parseRebookSearchResponse(string $responseXml): array
    {
        libxml_use_internal_errors(true);

        $xml = simplexml_load_string($responseXml);

        // XML解析错误处理
        if ($xml === false) {
            $errors = [];
            foreach (libxml_get_errors() as $error) {
                $errors[] = $error->message;
            }
            throw new \Exception('XML解析失败: ' . implode(', ', $errors));
        }

        // 检查处理结果
        $processResult = (string)$xml->ProcessResult->Status;
        if ($processResult !== 'SUCCESS') {
            throw new \Exception('改签搜索失败: ' . $processResult);
        }

        return [
            'passengers'       => $this->parsePassengers($xml->DataLibrary->Passengers ?? null),
            //'fares'            => $this->parseFares($xml->DataLibrary->Fares ?? null),
            'flights'          => $this->parseFlights($xml->DataLibrary->Flights ?? null),
            //'taxes'            => $this->parseTaxes($xml->DataLibrary->Taxes ?? null),
            //'ticket_masks'     => $this->parseTicketMasks($xml->DataLibrary->TicketMasks ?? null),
            'itinerary_offers' => $this->parseItineraryOffers($xml->ItineraryOffers ?? null),
        ];
    }

    /**
     * 解析乘客信息
     *
     * @param  \SimpleXMLElement|null  $passengersXml
     *
     * @return array
     */
    private function parsePassengers($passengersXml): array
    {
        if (!$passengersXml) {
            return [];
        }

        $passengers = [];
        foreach ($passengersXml->Passenger as $passenger) {
            $passengerTypes = [];
            if (isset($passenger->Ptcs)) {
                foreach ($passenger->Ptcs->PassengerType as $ptc) {
                    $passengerTypes[] = (string)$ptc;
                }
            }

            $passengers[(string)$passenger->Id] = [
                'id'              => (string)$passenger->Id,
                'passenger_types' => $passengerTypes,
                'primary_type'    => $passengerTypes[0] ?? 'ADT',
            ];
        }

        return $passengers;
    }

    /**
     * 解析运价信息
     *
     * @param  \SimpleXMLElement|null  $faresXml
     *
     * @return array
     */
    private function parseFares($faresXml): array
    {
        if (!$faresXml) {
            return [];
        }

        $fares = [];
        foreach ($faresXml->Fare as $fare) {
            // 解析改签费用信息
            $penalty = [];
            if (isset($fare->Penalty)) {
                $penalty = [
                    'change' => [
                        'before_departure' => [
                            'allowed'  => (string)$fare->Penalty->Change->BeforeDeparture->Allowed === 'true',
                            'amount'   => isset($fare->Penalty->Change->BeforeDeparture->Price) ?
                                (float)$fare->Penalty->Change->BeforeDeparture->Price->Amount : 0,
                            'currency' => isset($fare->Penalty->Change->BeforeDeparture->Price) ?
                                (string)$fare->Penalty->Change->BeforeDeparture->Price->Currency : 'CNY',
                        ],
                        'after_departure'  => [
                            'allowed'  => (string)$fare->Penalty->Change->AfterDeparture->Allowed === 'true',
                            'amount'   => isset($fare->Penalty->Change->AfterDeparture->Price) ?
                                (float)$fare->Penalty->Change->AfterDeparture->Price->Amount : 0,
                            'currency' => isset($fare->Penalty->Change->AfterDeparture->Price) ?
                                (string)$fare->Penalty->Change->AfterDeparture->Price->Currency : 'CNY',
                        ],
                    ],
                    'refund' => [
                        'before_departure' => [
                            'allowed' => (string)$fare->Penalty->Refund->BeforeDeparture->Allowed === 'true',
                        ],
                        'after_departure'  => [
                            'allowed' => (string)$fare->Penalty->Refund->AfterDeparture->Allowed === 'true',
                        ],
                    ],
                    'noshow' => [
                        'allowed' => (string)$fare->Penalty->Noshow->Allowed === 'true',
                    ],
                ];
            }

            $fares[(string)$fare->Id] = [
                'id'                => (string)$fare->Id,
                'origin'            => (string)$fare->Origin,
                'destination'       => (string)$fare->Destination,
                'carrier'           => trim((string)$fare->Carrier),
                'fare_basis_code'   => (string)$fare->Fbc,
                'rule_tariff'       => (string)$fare->RuleTariff,
                'rule'              => (string)$fare->Rule,
                'private'           => (string)$fare->Private === 'true',
                'fbc_override'      => trim((string)$fare->FbcOverride),
                'ticket_designator' => trim((string)$fare->TicketDesignator1),
                'penalty'           => $penalty,
            ];
        }

        return $fares;
    }

    /**
     * 解析航班信息
     *
     * @param  \SimpleXMLElement|null  $flightsXml
     *
     * @return array
     */
    private function parseFlights($flightsXml): array
    {
        if (!$flightsXml) {
            return [];
        }

        $flights = [];
        foreach ($flightsXml->Flight as $flight) {
            // 解析机型信息
            $aircraftTypes = [];
            if (isset($flight->AircraftTypes)) {
                foreach ($flight->AircraftTypes->AircraftTypeCode as $aircraftType) {
                    $aircraftTypes[] = (string)$aircraftType;
                }
            }

            $flights[(string)$flight->Id] = [
                'id'                      => (string)$flight->Id,
                'reprice_status'          => (string)$flight->RepriceStatus,
                'marketing_carrier'       => trim((string)$flight->MarketingCarrier),
                'marketing_flight_number' => trim((string)$flight->MarketingFlightNumber),
                'operating_carrier'       => trim((string)$flight->OperatingCarrier),
                'departure_airport'       => (string)$flight->DepartureAirport,
                'departure_terminal'      => (string)$flight->DepartureTerminal,
                'arrival_airport'         => (string)$flight->ArrivalAirport,
                'arrival_terminal'        => (string)$flight->ArrivalTerminal,
                'departure_datetime'      => (string)$flight->DepartureDateTime,
                'arrival_datetime'        => (string)$flight->ArrivalDateTime,
                'aircraft_types'          => $aircraftTypes,
                'primary_aircraft_type'   => $aircraftTypes[0] ?? '',
                'cabin_code'              => trim((string)$flight->Rbd),
                'available_seats'         => (int)$flight->AvailableSeats,
            ];
        }

        return $flights;
    }

    /**
     * 解析税费信息
     *
     * @param  \SimpleXMLElement|null  $taxesXml
     *
     * @return array
     */
    private function parseTaxes($taxesXml): array
    {
        if (!$taxesXml) {
            return [];
        }

        $taxes = [];
        foreach ($taxesXml->Tax as $tax) {
            $taxes[(string)$tax->Id] = [
                'id'       => (string)$tax->Id,
                'category' => (string)$tax->Category,
                'code'     => trim((string)$tax->Code),
                'amount'   => (float)$tax->Price->Amount,
                'currency' => (string)$tax->Price->Currency,
            ];
        }

        return $taxes;
    }

    /**
     * 解析票号掩码信息
     *
     * @param  \SimpleXMLElement|null  $ticketMasksXml
     *
     * @return array
     */
    private function parseTicketMasks($ticketMasksXml): array
    {
        if (!$ticketMasksXml) {
            return [];
        }

        $ticketMasks = [];
        foreach ($ticketMasksXml->TicketMask as $ticketMask) {
            // 解析发行信息
            $issueInfo = [];
            if (isset($ticketMask->IssueInformation)) {
                $issueInfo = [
                    'seller_id'    => (string)$ticketMask->IssueInformation->SellerId,
                    'country'      => (string)$ticketMask->IssueInformation->Country,
                    'datetime'     => (string)$ticketMask->IssueInformation->DateTime,
                    'account_code' => (string)$ticketMask->IssueInformation->AccountCode,
                ];
            }

            // 解析票联信息
            $coupons = [];
            if (isset($ticketMask->Coupons)) {
                $coupons = [
                    'dep_airport'          => (string)$ticketMask->Coupons->DepAirport,
                    'arr_airport'          => (string)$ticketMask->Coupons->ArrAirport,
                    'status'               => (string)$ticketMask->Coupons->Status,
                    'stopover'             => (string)$ticketMask->Coupons->Stopover,
                    'carrier'              => trim((string)$ticketMask->Coupons->Carrier),
                    'flight_no'            => (string)$ticketMask->Coupons->FlightNo,
                    'rbd'                  => (string)$ticketMask->Coupons->Rbd,
                    'dep_date'             => (string)$ticketMask->Coupons->DepDate,
                    'dep_time'             => (string)$ticketMask->Coupons->DepTime,
                    'fbc_passenger_coupon' => (string)$ticketMask->Coupons->FbcPassengerCoupon,
                    'fbc_audit_coupon'     => (string)$ticketMask->Coupons->FbcAuditCoupon,
                    'nvb'                  => (string)$ticketMask->Coupons->Nvb,
                    'nva'                  => (string)$ticketMask->Coupons->Nva,
                    'allowed_baggage'      => [
                        'quantity' => (int)$ticketMask->Coupons->AllowedBaggage->Quantity,
                        'unit'     => (string)$ticketMask->Coupons->AllowedBaggage->Unit,
                    ],
                ];
            }

            // 解析运价信息
            $fareInfo = [];
            if (isset($ticketMask->FareInformation)) {
                $fareInfo = [
                    'base_fare'       => [
                        'amount'   => (float)$ticketMask->FareInformation->BaseFare->Amount,
                        'currency' => (string)$ticketMask->FareInformation->BaseFare->Currency,
                    ],
                    'equivalent_fare' => [
                        'amount'   => (float)$ticketMask->FareInformation->EquivalentFare->Amount,
                        'currency' => (string)$ticketMask->FareInformation->EquivalentFare->Currency,
                    ],
                    'total_fare'      => [
                        'amount'   => (float)$ticketMask->FareInformation->TotalFare->Amount,
                        'currency' => (string)$ticketMask->FareInformation->TotalFare->Currency,
                    ],
                    'eticket_taxes'   => [
                        'tax1' => (string)$ticketMask->FareInformation->ETicketTaxes->Tax1,
                        'tax2' => (string)$ticketMask->FareInformation->ETicketTaxes->Tax2,
                        'tax3' => (string)$ticketMask->FareInformation->ETicketTaxes->Tax3,
                    ],
                    'iata_taxes_id'   => (string)$ticketMask->FareInformation->IataTaxes->Id,
                    'yqyr_id'         => (string)$ticketMask->FareInformation->Yqyr->Id,
                ];
            }

            $ticketMasks[(string)$ticketMask->Id] = [
                'id'                => (string)$ticketMask->Id,
                'issue_information' => $issueInfo,
                'plating_carrier'   => trim((string)$ticketMask->PlatingCarrier),
                'orig_dest'         => (string)$ticketMask->OrigDest,
                'coupons'           => $coupons,
                'fare_information'  => $fareInfo,
                'endorsement_box'   => (string)$ticketMask->EndorsementBox,
            ];
        }

        return $ticketMasks;
    }

    /**
     * 解析行程报价信息
     *
     * @param  \SimpleXMLElement|null  $itineraryOffersXml
     *
     * @return array
     */
    private function parseItineraryOffers($itineraryOffersXml): array
    {
        if (!$itineraryOffersXml) {
            return [];
        }

        $offers = [];
        foreach ($itineraryOffersXml->ItineraryOffer as $offer) {
            // 解析行程航班
            $itineraryFlights = [];
            if (isset($offer->ItineraryFlights)) {
                foreach ($offer->ItineraryFlights->FlightId as $flightId) {
                    $itineraryFlights[] = (string)$flightId;
                }
            }

            // 解析乘客信息
            $passengers = [];
            if (isset($offer->Passengers)) {
                foreach ($offer->Passengers->Passenger as $passenger) {
                    $passengerData = [
                        'passenger_id'      => (string)$passenger->PassengerId,
                        'itinerary_pricing' => [],
                    ];

                    // 解析行程定价
                    if (isset($passenger->ItineraryPricing)) {
                        // 运价到航班映射
                        $faresToFlights = [];
                        if (isset($passenger->ItineraryPricing->FaresToFlightsMapping)) {
                            foreach ($passenger->ItineraryPricing->FaresToFlightsMapping as $mapping) {
                                $coveredFlights = [];
                                if (isset($mapping->CoveredFlights)) {
                                    foreach ($mapping->CoveredFlights->Id as $flightId) {
                                        $coveredFlights[] = (string)$flightId;
                                    }
                                }
                                $faresToFlights[] = [
                                    'fare_id'         => (string)$mapping->FareId,
                                    'covered_flights' => $coveredFlights,
                                ];
                            }
                        }

                        // 出票信息
                        $ticketingInfos = [];
                        if (isset($passenger->ItineraryPricing->TicketingInfos)) {
                            foreach ($passenger->ItineraryPricing->TicketingInfos->TicketingInfo as $ticketingInfo) {
                                $coveredFlights = [];
                                if (isset($ticketingInfo->CoveredFlights)) {
                                    foreach ($ticketingInfo->CoveredFlights->Id as $flightId) {
                                        $coveredFlights[] = (string)$flightId;
                                    }
                                }

                                // 解析重新定价摘要
                                $repriceSummary = [];
                                if (isset($ticketingInfo->RepriceSummary)) {
                                    $summary   = $ticketingInfo->RepriceSummary;
                                    $taxUsages = [];
                                    if (isset($summary->TaxUsage)) {
                                        foreach ($summary->TaxUsage as $taxUsage) {
                                            $taxUsages[] = [
                                                'tax_code' => trim((string)$taxUsage->TaxCode),
                                                'amount'   => (float)$taxUsage->Price->Amount,
                                                'currency' => (string)$taxUsage->Price->Currency,
                                                'usage'    => (string)$taxUsage->Usage,
                                            ];
                                        }
                                    }

                                    $repriceSummary = [
                                        'base_fare'       => [
                                            'amount'   => (float)$summary->BaseFare->Amount,
                                            'currency' => (string)$summary->BaseFare->Currency,
                                        ],
                                        'equivalent_fare' => [
                                            'amount'   => (float)$summary->EquivalentFare->Amount,
                                            'currency' => (string)$summary->EquivalentFare->Currency,
                                        ],
                                        'sales_fare'      => [
                                            'amount'   => (float)$summary->SalesFare->Amount,
                                            'currency' => (string)$summary->SalesFare->Currency,
                                        ],
                                        'tax_usages'      => $taxUsages,
                                    ];
                                }

                                $ticketingInfos[] = [
                                    'ticket_mask_id'    => (string)$ticketingInfo->TicketMaskId,
                                    'covered_flights'   => $coveredFlights,
                                    'base_fare_diff'    => [
                                        'amount'   => (float)$ticketingInfo->BaseFareDiff->Amount,
                                        'currency' => (string)$ticketingInfo->BaseFareDiff->Currency,
                                    ],
                                    'reprice_fare_diff' => [
                                        'amount'   => (float)$ticketingInfo->RepriceFareDiff->Amount,
                                        'currency' => (string)$ticketingInfo->RepriceFareDiff->Currency,
                                    ],
                                    'tax_diff'          => [
                                        'amount'   => (float)$ticketingInfo->TaxDiff->Amount,
                                        'currency' => (string)$ticketingInfo->TaxDiff->Currency,
                                    ],
                                    'service_fee'       => [
                                        'amount'   => (float)$ticketingInfo->ServiceFee->Amount,
                                        'currency' => (string)$ticketingInfo->ServiceFee->Currency,
                                    ],
                                    'total_fare'        => [
                                        'amount'   => (float)$ticketingInfo->TotalFare->Amount,
                                        'currency' => (string)$ticketingInfo->TotalFare->Currency,
                                    ],
                                    'reprice_summary'   => $repriceSummary,
                                ];
                            }
                        }

                        $passengerData['itinerary_pricing'] = [
                            'fares_to_flights_mapping' => $faresToFlights,
                            'ticketing_infos'          => $ticketingInfos,
                        ];
                    }

                    $passengers[] = $passengerData;
                }
            }

            $offers[] = [
                'seller_id'            => (string)$offer->SellerId,
                'ora'                  => trim((string)$offer->Ora),
                'itinerary_flights'    => $itineraryFlights,
                'total_flight_time'    => (int)$offer->TotalFlightTime,
                'number_of_overnights' => (int)$offer->NumberOfOvernights,
                'passengers'           => $passengers,
            ];
        }

        return $offers;
    }

    /**
     * 格式化日期时间
     *
     * @param  string  $datetime
     *
     * @return string
     */
    private function formatDateTime(string $datetime): string
    {
        // 如果已经是ISO格式，直接返回
        if (strpos($datetime, 'T') !== false) {
            return $datetime;
        }

        // 将 "2022-12-29 12:00:00" 格式转换为 "2022-12-29T12:00:00"
        return str_replace(' ', 'T', $datetime);
    }

    /**
     * 格式化日期
     *
     * @param  string  $date
     *
     * @return string
     */
    private function formatDate(string $date): string
    {
        return date('Y-m-d', strtotime($date));
    }

    /**
     * 解析逗号分隔的ID字符串
     *
     * @param  string  $ids
     *
     * @return array
     */
    private function parseCommaSeparatedIds(string $ids): array
    {
        if (empty($ids)) {
            return [];
        }

        return array_map('trim', explode(',', $ids));
    }

    /**
     * 改签价格查询接口
     *
     * @param  array  $params  价格查询参数
     *
     * @return array 价格查询结果
     * @throws \Exception
     */
    public function rebookPrice(array $params): array
    {
        // 构建请求XML
        $requestXml = $this->buildRebookPriceXml($params);

        // TODO: 发起接口调用
        // $response = $this->sendRequest($requestXml);

        // 模拟解析接口返回XML
        $responseXml = file_get_contents(ROOTPATH . 'writable/xml/international/rebook_price_res.xml');

        // 解析响应数据
        return $this->parseRebookPriceResponse($responseXml);
    }

    /**
     * 改签确认接口
     *
     * @param  array  $params  确认改签参数
     *
     * @return array 确认改签结果
     */
    public function rebookConfirm(array $params): array
    {
        // 构建请求XML
        $requestXml = $this->buildRebookConfirmXml($params);

        // TODO: 发起接口调用
        // $response = $this->sendRequest($requestXml);

        // 模拟解析接口返回XML
        $responseXml = file_get_contents(ROOTPATH . 'writable/xml/international/rebook_ticket_res.xml');

        // 解析响应数据
        return $this->parseRebookConfirmResponse($responseXml);
    }

    /**
     * 构建改签价格查询请求XML
     *
     * @param  array  $params  请求参数
     *
     * @return string XML字符串
     */
    private function buildRebookPriceXml(array $params): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<TES_AirTicketChangerInterRQ>' . "\n";

        // POS信息
        $xml .= '    <POS>' . "\n";
        $xml .= '        <Source PseudoCityCode="' . $this->ibe_config->office . '" CityCode="' . $this->ibe_config->city . '" IataCode="' . $this->ibe_config->iata_code . '" ChannelID="CRS" PayType="CASH" />' . "\n";
        $xml .= '    </POS>' . "\n";

        // 交易命令
        $xml .= '    <TransactionCommand>' . "\n";
        $xml .= '        <TicketNo>' . htmlspecialchars($params['ticket_no']) . '</TicketNo>' . "\n";
        $xml .= '        <ExchangeAirline>' . htmlspecialchars($params['exchange_airline']) . '</ExchangeAirline>' . "\n";
        $xml .= '        <PassengerName>' . htmlspecialchars($params['passenger_name']) . '</PassengerName>' . "\n";
        $xml .= '        <PassengerID>' . htmlspecialchars($params['passenger_id']) . '</PassengerID>' . "\n";
        $xml .= '        <InvoluntaryIdentifier>' . ($params['involuntary'] ? 'true' : 'false') . '</InvoluntaryIdentifier>' . "\n";
        $xml .= '        <BookingReferenceID ID="' . htmlspecialchars($params['new_pnr']) . '" ID_Context="PNR" />' . "\n";
        $xml .= '        <Endorsement>' . ($params['endorsement'] ? 'true' : 'false') . '</Endorsement>' . "\n";
        $xml .= '        <PassengerCount>' . (int)$params['passenger_count'] . '</PassengerCount>' . "\n";
        $xml .= '    </TransactionCommand>' . "\n";

        // 航程信息
        $xml .= '    <AirItinerary>' . "\n";
        $xml .= '        <OriginDestinationOptions>' . "\n";
        $xml .= '            <OriginDestinationOption>' . "\n";

        foreach ($params['segments'] as $index => $segment) {
            $rph               = $index + 1;
            $departureDateTime = $this->formatDateTime($segment['departure_time']);
            $arrivalDateTime   = $this->formatDateTime($segment['arrival_time']);

            $xml .= '                <FlightSegment RPH="' . $rph . '" DepartureDateTime="' . $departureDateTime . '" ArrivalDateTime="' . $arrivalDateTime . '" CodeshareInd="true" FlightNumber="' . htmlspecialchars($segment['flight_number']) . '" NumberInParty="12" Status="NN" SegmentType="NORMAL">' . "\n";
            $xml .= '                    <OperatingAirline Code="' . htmlspecialchars($segment['operation_airline']) . '" FlightNumber="' . htmlspecialchars($segment['flight_number']) . '"/>' . "\n";
            $xml .= '                    <DepartureAirport LocationCode="' . htmlspecialchars($segment['departure_airport']) . '"/>' . "\n";
            $xml .= '                    <ArrivalAirport LocationCode="' . htmlspecialchars($segment['arrival_airport']) . '"/>' . "\n";
            $xml .= '                    <Equipment AirEquipType="' . htmlspecialchars($segment['air_equip_type']) . '"/>' . "\n";
            $xml .= '                    <MarketingAirline Code="' . htmlspecialchars($segment['marketing_airline']) . '"/>' . "\n";
            $xml .= '                    <BookingClassAvail ResBookDesigCode="' . htmlspecialchars($segment['cabin']) . '"/>' . "\n";
            $xml .= '                </FlightSegment>' . "\n";
        }

        $xml .= '            </OriginDestinationOption>' . "\n";

        $xml .= '        </OriginDestinationOptions>' . "\n";
        $xml .= '    </AirItinerary>' . "\n";
        $xml .= '    <FareskyParameters Changeable="true" Refundable="true" Currency="CNY" Waiver="NORMAL">
                        <FareTypes>
                            <FareType>PUBLIC</FareType>
                            <FareType>PRIVATE</FareType>
                            <FareType>IT_PUBLIC</FareType>
                            <FareType>IT_PRIVATE</FareType>
                        </FareTypes>
                    </FareskyParameters>';
        $xml .= '    <PassengerTypeInfo>';
        $xml .= '        <Type>' . $params['passenger_type'] . '</Type>';
        $xml .= '        <ExactPtcMatch>true</ExactPtcMatch>';
        $xml .= '    </PassengerTypeInfo>';
        $xml .= '</TES_AirTicketChangerInterRQ>';

        return $xml;
    }

    /**
     * 构建改签确认请求XML
     *
     * @param  array  $params  请求参数
     *
     * @return string XML字符串
     */
    private function buildRebookConfirmXml(array $params): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<OTA_AirIntlReissueRQ>' . "\n";

        // POS信息
        $xml .= '    <POS>' . "\n";
        $xml .= '        <Source PseudoCityCode="' . $this->ibe_config->office . '" ChannelID="CRS" OtherID="28"/>' . "\n";
        $xml .= '    </POS>' . "\n";

        // 出票详情
        $xml .= '    <DemandTicketDetail ReconfirmSegmentInd="false" ReturnTicketInfoInd="true" LimitSegmentStatusInd="false">' . "\n";

        // 航程信息
        $xml .= '        <AirItinerary>' . "\n";
        $xml .= '            <OriginDestinationOptions>' . "\n";
        $xml .= '                <OriginDestinationOption>' . "\n";

        foreach ($params['flights'] as $index => $flight) {
            $rph               = $index + 1;
            $departureDateTime = $this->formatDateTime($flight['departure_time']);
            $arrivalDateTime   = $this->formatDateTime($flight['arrival_time']);

            $xml .= '                    <FlightSegment RPH="' . $rph . '" DepartureDateTime="' . $departureDateTime . '" ArrivalDateTime="' . $arrivalDateTime . '" CodeshareInd="false" FlightNumber="' . htmlspecialchars($flight['flight_number']) . '" NumberInParty="12" Status="HK" SegmentType="NORMAL" FareBasis="YORFFF" IsChanged="false">' . "\n";
            $xml .= '                        <OperatingAirline Code="' . htmlspecialchars($flight['operation_airline']) . '" FlightNumber="' . htmlspecialchars($flight['flight_number']) . '"/>' . "\n";
            $xml .= '                        <DepartureAirport LocationCode="' . htmlspecialchars($flight['departure_airport']) . '"/>' . "\n";
            $xml .= '                        <ArrivalAirport LocationCode="' . htmlspecialchars($flight['arrival_airport']) . '"/>' . "\n";
            $xml .= '                        <Equipment AirEquipType="' . htmlspecialchars($flight['air_equip_type']) . '"/>' . "\n";
            $xml .= '                        <MarketingAirline Code="' . htmlspecialchars($flight['marketing_airline']) . '"/>' . "\n";
            $xml .= '                        <BookingClassAvail ResBookDesigCode="' . htmlspecialchars($flight['cabin']) . '"/>' . "\n";
            $xml .= '                    </FlightSegment>' . "\n";
        }

        $xml .= '                </OriginDestinationOption>' . "\n";
        $xml .= '            </OriginDestinationOptions>' . "\n";
        $xml .= '        </AirItinerary>' . "\n";

        // PNR信息
        $xml .= '        <BookingReferenceID ID="' . htmlspecialchars($params['pnr']) . '" ID_Context="PNR"/>' . "\n";
        $xml .= '        <PaymentInfo PaymentType="CASH" CurrencyCode="CNY"/>' . "\n";

        // 退改价格信息
        $xml .= '        <ReIssueInfos>' . "\n";
        foreach ($params['tickets'] as $ticket) {
            $xml .= '            <ReIssueInfo>' . "\n";
            $xml .= '                <ReIssuePriceRQ>' . "\n";
            $xml .= '                    <TicketNumber_Before>' . htmlspecialchars($ticket['ticket_number_before']) . '</TicketNumber_Before>' . "\n";
            $xml .= '                    <IsInfant>' . ($ticket['is_infant'] ? 'true' : 'false') . '</IsInfant>' . "\n";
            $xml .= '                    <PassName>' . htmlspecialchars($ticket['passenger_name']) . '</PassName>' . "\n";
            $xml .= '                    <InvoluntaryIdentifier>' . ($ticket['involuntary_identifier'] ? 'true' : 'false') . '</InvoluntaryIdentifier>' . "\n";
            $xml .= '                    <AirlineCode>' . htmlspecialchars($ticket['airline']) . '</AirlineCode>' . "\n";
            $xml .= '                    <IataCode>' . $this->ibe_config->iata_code . '</IataCode>' . "\n";
            $xml .= '                    <CityCode>' . $this->ibe_config->city . '</CityCode>' . "\n";
            $xml .= '                    <Endorsement Info="' . htmlspecialchars($ticket['ei']) . '"/>' . "\n";
            $xml .= '                </ReIssuePriceRQ>' . "\n";
            $xml .= '                <ReIssuePriceInfo>' . "\n";
            $xml .= '                    <ReServiceFee Amount="' . $ticket['exchange_fee'] . '" Currency="CNY"/>' . "\n";
            $xml .= '                    <ReTotalFare Amount="' . $ticket['total_fare'] . '" Currency="CNY"/>' . "\n";
            $xml .= '                    <ReBaseFare Amount="' . $ticket['ticket_fare'] . '" Currency="CNY"/>' . "\n";
            $xml .= '                    <ReRepriceFareDiff Amount="' . $ticket['fare_diff'] . '" Currency="CNY"/>' . "\n";
            $xml .= '                    <ReBaseFareDiff Amount="' . $ticket['fare_diff'] . '" Currency="CNY"/>' . "\n";
            $xml .= '                    <ReTaxDiff Amount="' . $ticket['tax_diff'] . '" Currency="CNY"/>' . "\n";
            $xml .= '                </ReIssuePriceInfo>' . "\n";
            $xml .= '            </ReIssueInfo>' . "\n";
        }
        $xml .= '        </ReIssueInfos>' . "\n";

        // Faresky参数
        $xml .= '        <FareskyParameters Changeable="true" Refundable="true" Currency="CNY">' . "\n";
        $xml .= '            <FareTypes>' . "\n";
        $xml .= '                <FareType>PUBLIC</FareType>' . "\n";
        $xml .= '                <FareType>PRIVATE</FareType>' . "\n";
        $xml .= '                <FareType>IT_PUBLIC</FareType>' . "\n";
        $xml .= '                <FareType>IT_PRIVATE</FareType>' . "\n";
        $xml .= '            </FareTypes>' . "\n";
        $xml .= '            <PassengerTypeInfo>' . "\n";
        $xml .= '                <ExactPtcMatch>true</ExactPtcMatch>' . "\n";
        $xml .= '            </PassengerTypeInfo>' . "\n";
        $xml .= '        </FareskyParameters>' . "\n";

        $xml .= '    </DemandTicketDetail>' . "\n";
        $xml .= '</OTA_AirIntlReissueRQ>';

        return $xml;
    }

    /**
     * 解析改签价格查询响应
     *
     * @param  string  $responseXml  响应XML字符串
     *
     * @return array 解析后的数据
     */
    private function parseRebookPriceResponse(string $responseXml): array
    {
        libxml_use_internal_errors(true);

        $xml = simplexml_load_string($responseXml);

        if ($xml === false) {
            $errors = [];
            foreach (libxml_get_errors() as $error) {
                $errors[] = $error->message;
            }
            throw new \Exception('XML解析失败: ' . implode(', ', $errors));
        }

        $result = [
            'status'         => 'success',
            'reprice_result' => [],
        ];

        if (isset($xml->TRR_RepriceRS->RepriceResult)) {
            $repriceResult = $xml->TRR_RepriceRS->RepriceResult;

            $result['reprice_result'] = [
                'pos'          => [
                    'office'  => (string)$repriceResult->POS->Office,
                    'iata_no' => (string)$repriceResult->POS->IataNo,
                    'date'    => (string)$repriceResult->POS->Date,
                ],
                'ticket'       => [
                    'ticket_no'      => (string)$repriceResult->Ticket->TicketNo,
                    'passenger_name' => (string)$repriceResult->Ticket->PassengerName,
                    'passenger_type' => [
                        'original' => (string)$repriceResult->Ticket->PassengerType->OriPassengerType,
                        'new'      => (string)$repriceResult->Ticket->PassengerType->NewPassengerType,
                    ],
                ],
                'reprice_fare' => $this->parseRepriceFare($repriceResult->RepriceFare ?? null),
            ];
        }

        return $result;
    }

    /**
     * 解析改签确认响应
     *
     * @param  string  $responseXml  响应XML字符串
     *
     * @return array 解析后的数据
     */
    private function parseRebookConfirmResponse(string $responseXml): array
    {
        libxml_use_internal_errors(true);

        $xml = simplexml_load_string($responseXml);
        if ($xml === false) {
            $errors = [];
            foreach (libxml_get_errors() as $error) {
                $errors[] = $error->message;
            }
            throw new \Exception('XML解析失败: ' . implode(', ', $errors));
        }

        $result = [];
        // 解析票据信息，转换为与国内接口一致的格式
        if (isset($xml->TicketItemInfo)) {
            foreach ($xml->TicketItemInfo as $ticketInfo) {
                $flightSegments = [];
                if (isset($ticketInfo->FlightSegment)) {
                    foreach ($ticketInfo->FlightSegment as $segment) {
                        $flightSegments[] = [
                            'departure_time'    => (string)$segment['DepartureDateTime'],
                            'flight_number'     => (string)$segment['FlightNumber'],
                            'departure_airport' => (string)$segment->DepartureAirport['LocationCode'],
                            'arrival_airport'   => (string)$segment->ArrivalAirport['LocationCode'],
                            'airline_code'      => (string)$segment->MarketingAirline['Code'],
                        ];
                    }
                }

                $result[] = [
                    'ticket_number'  => (string)$ticketInfo['TicketNumber'],
                    'total_amount'   => (float)$ticketInfo['TotalAmount'],
                    'currency'       => (string)$ticketInfo['CurrencyCode'],
                    'payment_type'   => (string)$ticketInfo['PaymentType'],
                    'ei'             => (string)$ticketInfo['Endorsement'],
                    'passenger_name' => (string)$ticketInfo->PassengerName->Surname,
                    'passenger_type' => $this->convertPassengerType((string)$ticketInfo->PassengerName['PassengerTypeCode']),
                    'flights'        => $flightSegments,
                ];
            }
        }

        return $result;
    }

    /**
     * 解析重新定价运价信息
     *
     * @param  \SimpleXMLElement|null  $repriceFareXml
     *
     * @return array
     */
    private function parseRepriceFare($repriceFareXml): array
    {
        if (!$repriceFareXml) {
            return [];
        }

        $repriceFare = [];

        // 解析重新定价信息
        if (isset($repriceFareXml->RepriceInfo)) {
            $repriceInfo                 = $repriceFareXml->RepriceInfo;
            $repriceFare['reprice_info'] = [
                'gp_indicator'          => (string)$repriceInfo->GpIndicator === 'true',
                'involuntary_indicator' => (string)$repriceInfo->InvoluntaryIndicator === 'true',
                'auto_price_indicator'  => (string)$repriceInfo->AutoPriceIndicator === 'true',
                'exchange_indicator'    => (string)$repriceInfo->ExchangeIndicator === 'true',
                'is_history_fare'       => (string)$repriceInfo->IsHistoryFare,
                'ticketing_time_limit'  => (string)$repriceInfo->TicketingTimeLimit,
            ];
        }

        // 解析价格差异信息
        if (isset($repriceFareXml->FareDiff)) {
            $fareDiff                 = $repriceFareXml->FareDiff;
            $repriceFare['fare_diff'] = [
                'base_fare_diff'    => [
                    'amount'   => (float)$fareDiff->BaseFareDiff->Amount,
                    'currency' => (string)$fareDiff->BaseFareDiff->Currency,
                ],
                'reprice_fare_diff' => [
                    'amount'   => (float)$fareDiff->RepriceFareDiff->Amount,
                    'currency' => (string)$fareDiff->RepriceFareDiff->Currency,
                ],
                'tax_diff'          => [
                    'amount'   => (float)$fareDiff->TaxDiff->Amount,
                    'currency' => (string)$fareDiff->TaxDiff->Currency,
                ],
            ];
        }

        // 解析服务费
        if (isset($repriceFareXml->ServiceFee)) {
            $repriceFare['service_fee'] = [
                'amount'   => (float)$repriceFareXml->ServiceFee->Amount,
                'currency' => (string)$repriceFareXml->ServiceFee->Currency,
            ];
        }

        // 解析总费用
        if (isset($repriceFareXml->TotalFare)) {
            $repriceFare['total_fare'] = [
                'amount'   => (float)$repriceFareXml->TotalFare->Amount,
                'currency' => (string)$repriceFareXml->TotalFare->Currency,
            ];
        }

        // 解析运价详情
        if (isset($repriceFareXml->FareDetails)) {
            $repriceFare['fare_details'] = [];
            foreach ($repriceFareXml->FareDetails->FareDetail as $fareDetail) {
                $repriceFare['fare_details'][] = [
                    'base_fare'       => [
                        'amount'   => (float)$fareDetail->BaseFare->Amount,
                        'currency' => (string)$fareDetail->BaseFare->Currency,
                    ],
                    'equivalent_fare' => [
                        'amount'   => (float)$fareDetail->EquivalentFare->Amount,
                        'currency' => (string)$fareDetail->EquivalentFare->Currency,
                    ],
                    'total_fare'      => [
                        'amount'   => (float)$fareDetail->TotalFare->Amount,
                        'currency' => (string)$fareDetail->TotalFare->Currency,
                    ],
                ];
            }
        }

        return $repriceFare;
    }

    /**
     * 转换乘客类型代码
     *
     * @param  string  $passengerTypeCode  乘客类型代码
     *
     * @return int 乘客类型数字
     */
    private function convertPassengerType(string $passengerTypeCode): int
    {
        switch (strtoupper($passengerTypeCode)) {
            case 'ADT':
                return 1; // 成人
            case 'CNN':
            case 'CHD':
                return 2; // 儿童
            case 'INF':
                return 3; // 婴儿
            default:
                return 1; // 默认成人
        }
    }

}