<?php

namespace App\Libraries\Api\IBE;

use DateTime;

class Ticket
{
    protected $ibeConfig;

    public function __construct()
    {
        $this->ibe_config = config('IBE');
    }

    // 国内BSP出票
    // 调用参数：
    //    [
    //        'pnr' => 'H12345', // PNR
    //        'ei' => '不得退改签', // EI
    //        'payment_type' => 'CASH', // 支付方式
    //        'currency_code' => 'CNY', // 货币
    //        'passengers' => [ // 多个乘客
    //            [
    //                'passenger_type' => 'ADT', // 乘客类型：ADT成人 CHD儿童 INF婴儿
    //                'rph' => '1', // 乘客编号
    //                'surname' => 'WU/XIAO', // 姓名
    //            ],
    //            [
    //                'passenger_type' => 'ADT', // 乘客类型：ADT成人 CHD儿童 INF婴儿
    //                'rph' => '2', // 乘客编号
    //                'surname' => 'ZHANG/SAN', // 姓名
    //            ],
    //        ],
    //    ];
    // 返回数据：
    //[
    //    [pnr] => JSSYBQ // PNR
    //    [tickets] =>  // 电子票号
    //        [
    //            [0] => // 多个电子票号
    //                [
    //                    [ticket_number] => 781-3250419585 // 电子票号
    //                    [total_amount] => 1520.0 // 金额
    //                    [currency_code] => CNY // 货币
    //                    [payment_type] => CC // 支付方式
    //                    [passenger_type] => ADT // 乘客类型：ADT成人 CHD儿童 INF婴儿
    //                    [passenger_surname] => ZHOU/XIAO // 姓名
    //                    [flight_segments] => // 航段
    //                        [
    //                            [0] => // 多个航段
    //                                [
    //                                    [departure_datetime] => 2013-08-14T07:30:00 // 起飞时间
    //                                    [ticket_status] => OPEN FOR USE // 客票状态
    //                                    [airline] => MF // 执飞航空公司
    //                                    [flight_number] => 155 // 招飞航班号
    //                                    [departure_airport] => SHA // 出发机场
    //                                    [arrival_airport] => PVG // 到达机场
    //                                    [marketing_airline] => CA // 市场航空公司
    //                                ]
    //
    //                    ]
    //
    //            ]
    //                                    
    //        ]
    //    ]
    public function issue_domestic_ticket($params)
    {
        $requestXml = '<OTA_AirDemandTicketRQ>
                <POS>
                    <Source PseudoCityCode="' . $this->ibe_config->office . '" OtherID="' . $this->ibe_config->pid . '"></Source>
                </POS>
                <DemandTicketDetail ReconfirmSegmentInd="true"
                    ReturnTicketInfoInd="true" LimitSegmentStatusInd="true" RollbackInd="true">
                    <BookingReferenceID ID="' . $params['pnr'] . '" ID_Context="PNR" />
                    <Endorsement Info="' . $params['ei'] . '"></Endorsement>
                    <PaymentInfo PaymentType="' . $params['payment_type'] . '" CurrencyCode="' . $params['currency_code'] . '">
                    </PaymentInfo>';
        if (isset($params['passengers'])) {
            foreach ($params['passengers'] as $passenger) {
                $requestXml .= '
                    <PassengerName PassengerRefNumber="' . $passenger['rph'] . '" PassengerTypeCode="' . $passenger['passenger_type'] . '">
                        <Surname>' . $passenger['surname'] . '</Surname>
                    </PassengerName>';
            }
        }
        $requestXml .= '
                </DemandTicketDetail>
            </OTA_AirDemandTicketRQ>';

        //echo $requestXml;

        $xml                  = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/issue_domestic_ticket.xml'));
        $arrTicket            = [];
        $arrTicket['pnr']     = (string)$xml->BookingReferenceID['ID'];
        $arrTicket['tickets'] = [];
        foreach ($xml->TicketItemInfo as $ticket) {
            $arrFlightSegment = [];
            foreach ($ticket->FlightSegment as $flightSegment) {
                $arrFlightSegment[] = [
                    'departure_datetime' => (string)$flightSegment['DepartureDateTime'],
                    'ticket_status'      => (string)$flightSegment['TicketStatus'],
                    'airline'            => (string)$flightSegment->OperatingAirline['Code'],
                    'flight_number'      => (string)$flightSegment->OperatingAirline['FlightNumber'],
                    'departure_airport'  => (string)$flightSegment->DepartureAirport['LocationCode'],
                    'arrival_airport'    => (string)$flightSegment->ArrivalAirport['LocationCode'],
                    'marketing_airline'  => (string)$flightSegment->MarketingAirline['Code'],
                ];
            }
            $arrTicket['tickets'][] = [
                'ticket_number'     => (string)$ticket['TicketNumber'],
                'total_amount'      => (string)$ticket['TotalAmount'],
                'currency_code'     => (string)$ticket['CurrencyCode'],
                'payment_type'      => (string)$ticket['PaymentType'],
                'passenger_type'    => (string)$ticket->PassengerName['PassengerTypeCode'],
                'passenger_surname' => (string)$ticket->PassengerName->Surname,
                'flight_segments'   => $arrFlightSegment,
            ];
        }

        // 模拟数据
        $db        = db_connect();
        $arrTicket = [];
        $pnrInfo   = $db->table('pnr')->select('order_id')->where('pnr', $params['pnr'])->get()->getRowArray();
        if (empty($pnrInfo)) {
            return [];
        }
        $passengerList = $db->table('ticket_book_order_passengers')->where('order_id', $pnrInfo['order_id'])->orderBy('id', 'asc')->get()->getResultArray();
        if (empty($passengerList)) {
            return [];
        }
        $flightList = $db->table('ticket_book_order_segments')->where('order_id', $pnrInfo['order_id'])->orderBy('id', 'asc')->get()->getResultArray();
        if (empty($flightList)) {
            return [];
        }
        $arrFlight = [];
        foreach ($flightList as $item) {
            $flight = $db->table('flights')->where('flight_number', $item['flight_number'])->get()->getRowArray();
            if (empty($flight)) {
                return [];
            }
            $arrFlight[] = [
                'departure_datetime' => $item['departure_datetime'],
                'ticket_status'      => 'OPEN FOR USE',
                'airline'            => $item['airline'],
                'flight_number'      => $item['flight_number'],
                'departure_airport'  => $flight['departure_airport'],
                'arrival_airport'    => $flight['arrival_airport'],
            ];
        }
        $arrPassenger     = [];
        $arrPassengerType = [
            1 => 'ADT',
            2 => 'CHD',
            3 => 'INF',
        ];
        foreach ($passengerList as $passenger) {
            $arrPassenger[] = [
                'ticket_number'     => mt_rand(100, 999) . '-' . mt_rand(1000000000, 9999999999),
                'total_amount'      => 1000,
                'currency_code'     => 'CNY',
                'payment_type'      => 'CASH',
                'passenger_type'    => $arrPassengerType[$passenger['passenger_type']],
                'passenger_surname' => $passenger['person_name'],
                'flight_segments'   => $arrFlight,
            ];
        }
        $arrTicket = [
            'pnr'     => $params['pnr'],
            'tickets' => $arrPassenger,
        ];

        return $arrTicket;
    }

    public function issue_domestic_bop_ticket($params)
    {
        $requestXml = '<OTA_AirDemandTicketRQ>
                <POS>
                    <Source PseudoCityCode="' . $this->ibe_config->office . '" OtherID="' . $this->ibe_config->pid . '"></Source>
                </POS>
                <DemandTicketDetail ReconfirmSegmentInd="true" ReturnTicketInfoInd="true"
                    LimitSegmentStatusInd="true"
                    DPayPassword="159753">
                    <BookingReferenceID ID="' . $params['pnr'] . '" ID_Context="PNR"></BookingReferenceID>
                    <Endorsement Info="' . $params['ei'] . '"></Endorsement>
                </DemandTicketDetail>
            </OTA_AirDemandTicketRQ>';

        $xml                  = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/issue_domestic_bop_ticket.xml'));
        $arrTicket            = [];
        $arrTicket['pnr']     = (string)$xml->BookingReferenceID['ID'];
        $arrTicket['tickets'] = [];
        foreach ($xml->TicketItemInfo as $ticket) {
            $arrFlightSegment = [];
            foreach ($ticket->FlightSegment as $flightSegment) {
                $arrFlightSegment[] = [
                    'departure_datetime' => (string)$flightSegment['DepartureDateTime'],
                    'ticket_status'      => (string)$flightSegment['TicketStatus'],
                    'airline'            => (string)$flightSegment->OperatingAirline['Code'],
                    'flight_number'      => (string)$flightSegment->OperatingAirline['FlightNumber'],
                    'departure_airport'  => (string)$flightSegment->DepartureAirport['LocationCode'],
                    'arrival_airport'    => (string)$flightSegment->ArrivalAirport['LocationCode'],
                    'marketing_airline'  => (string)$flightSegment->MarketingAirline['Code'],
                ];
            }
            $arrTicket['tickets'][] = [
                'ticket_number'     => (string)$ticket['TicketNumber'],
                'total_amount'      => (string)$ticket['TotalAmount'],
                'currency_code'     => (string)$ticket['CurrencyCode'],
                'payment_type'      => (string)$ticket['PaymentType'],
                'passenger_type'    => (string)$ticket->PassengerName['PassengerTypeCode'],
                'passenger_surname' => (string)$ticket->PassengerName->Surname,
                'flight_segments'   => $arrFlightSegment,
            ];
        }

        // 模拟数据
        $db        = db_connect();
        $arrTicket = [];
        $pnrInfo   = $db->table('pnr')->select('id')->where('pnr', $params['pnr'])->get()->getRowArray();
        if (empty($pnrInfo)) {
            return [];
        }
        $passengerList = $db->table('pnr_passengers')->where('pnr_id', $pnrInfo['id'])->orderBy('id', 'asc')->get()->getResultArray();
        if (empty($passengerList)) {
            return [];
        }
        $flightList = $db->table('pnr_segments')->where('pnr_id', $pnrInfo['id'])->orderBy('id', 'asc')->get()->getResultArray();
        if (empty($flightList)) {
            return [];
        }
        $arrFlight = [];
        foreach ($flightList as $item) {
            $flight = $db->table('flights')->where('flight_number', $item['flight_number'])->get()->getRowArray();
            if (empty($flight)) {
                return [];
            }
            $arrFlight[] = [
                'departure_datetime' => $item['departure_datetime'],
                'ticket_status'      => 'OPEN FOR USE',
                'airline'            => $item['airline'],
                'flight_number'      => $item['flight_number'],
                'departure_airport'  => $flight['departure_airport'],
                'arrival_airport'    => $flight['arrival_airport'],
            ];
        }
        $arrPassenger     = [];
        $arrPassengerType = [
            1 => 'ADT',
            2 => 'CHD',
            3 => 'INF',
        ];
        foreach ($passengerList as $passenger) {
            $arrPassenger[] = [
                'ticket_number'     => mt_rand(100, 999) . '-' . mt_rand(1000000000, 9999999999),
                'total_amount'      => 1000,
                'currency_code'     => 'CNY',
                'payment_type'      => 'CASH',
                'passenger_type'    => $arrPassengerType[$passenger['passenger_type']],
                'passenger_surname' => $passenger['person_name'],
                'flight_segments'   => $arrFlight,
            ];
        }
        $arrTicket = [
            'pnr'     => $params['pnr'],
            'tickets' => $arrPassenger,
        ];

        return $arrTicket;
    }

    // 国内机票改签出票
    //  [
    //        'pnr' => 'L12345', // PNR
    //        'tickets' => [ // 多个原有电子票
    //            0 => [
    //                'ticket_number_before' => '999-5785450339', // 原有电子票号
    //                'is_infant' => false, // 是否婴儿
    //                'passenger_name' => 'ZHANG/JIANXIN', //乘客姓名
    //                'involuntary_identifier' => false, // 是否非自愿改签
    //                'airline' => 'MU', // 航空公司
    //                'ei' => '不得退改签', // EI
    //                'exchange_type' => 1, // 退改类型 0不出新票 1出新票
    //                'exchange_fee' => 0, // 退改签费
    //                'change_fee' => 0, // 改签费
    //                'ticket_fare' => 60, // 票面价
    //                'fare_diff' => 0, // 票面差价
    //                'tax_diff' => 0, // 税费差价
    //                'total_fare' => 0, // 总运价
    //            ],
    //            1 => [
    //                'ticket_number_before' => '999-2070150330',
    //                'is_infant' => false,
    //                'passenger_name' => 'ZHANG/XIAO',
    //                'involuntary_identifier' => false,
    //                'airline' => 'MU',
    //                'ei' => '不得退改签',
    //                'exchange_type' => 1,
    //                'exchange_fee' => 0,
    //                'change_fee' => 0,
    //                'ticket_fare' => 50,
    //                'fare_diff' => 0,
    //                'tax_diff' => 0,
    //                'total_fare' => 0,
    //            ],
    //        ],
    //        'flights' => [ // 新航段，多个
    //            0 => [
    //                'departure_airport' => 'XMN', // 出发机场
    //                'arrival_airport' => 'SHA', // 到达机场
    //                'air_equip_type' => '744', // 机型
    //                'marketing_airline' => 'MU', // 市场航空公司
    //                'operation_airline' => 'MU', // 执飞航空公司
    //                'flight_number' => '1234', // 航班号
    //                'departure_time' => '2017-09-29T07:55:00', // 起飞时间
    //                'arrival_time' => '2017-09-29T10:55:00', // 到达时间
    //                'type' => 'NORMAL', // 航段类型 NORMAL普通航班
    //                'cabin' => 'F', // 舱位
    //                'action_code' => 'HK', // 行动代码
    //            ],
    //        ]
    //    ];
    //
    // 返回数据：
    //    [ // 多个电子票号
    //        [0] => // 电子票号
    //            [
    //                [ticket_number] => 999-2070145713 // 票号
    //                [total_amount] => 0.0 // 票号总金额
    //                [currency] => CNY // 货币
    //                [payment_type] => CASH // 支付方式
    //                [ei] => 不得签转/变更退票收费 // 签注信息
    //                [passenger_type] => ADT // 乘客类型
    //                [passenger_name] => ZHANG/JIANXIN // 乘客姓名
    //                [flights] => // 多个航段信息
    //                    [
    //                        [0] => // 航段信息
    //                            [
    //                                [departure_airport] => SHA // 出发机场
    //                                [arrival_airport] => PEK // 到达机场
    //                                [marketing_airline] => CA // 市场航空公司
    //                                [departure_time] => 2018-08-19T07:55:00 // 出发时间
    //                                [flight_number] => 1858 // 航班号
    //                                [ticket_status] => OPEN FOR USE // 客票状态
    //                                [cabin] => Y // 舱位
    //                            ]
    //                        [1] => 
    //                            [
    //                                [departure_airport] => PEK
    //                                [arrival_airport] => SHA
    //                                [marketing_airline] => CA
    //                                [departure_time] => 2018-08-20T07:30:00
    //                                [flight_number] => 1831
    //                                [ticket_status] => OPEN FOR USE
    //                                [cabin] => Y
    //                            ]
    //                    ]
    //            ]
    //        [1] => 
    //            [
    //                [ticket_number] => 999-2070145714
    //                [total_amount] => 0.0
    //                [currency] => CNY
    //                [payment_type] => CASH
    //                [ei] => 不得签转/变更退票收费
    //                [passenger_type] => CHD
    //                [passenger_name] => ZHANG/XIAO
    //                [flights] => 
    //                    [
    //                        [0] => 
    //                            [
    //                                [departure_airport] => SHA
    //                                [arrival_airport] => PEK
    //                                [marketing_airline] => CA
    //                                [departure_time] => 2018-08-19T07:55:00
    //                                [flight_number] => 1858
    //                                [ticket_status] => OPEN FOR USE
    //                                [cabin] => Y
    //                            ]
    //                        [1] => 
    //                            [
    //                                [departure_airport] => PEK
    //                                [arrival_airport] => SHA
    //                                [marketing_airline] => CA
    //                                [departure_time] => 2018-08-20T07:30:00
    //                                [flight_number] => 1831
    //                                [ticket_status] => OPEN FOR USE
    //                                [cabin] => Y
    //                            ]
    //                    ]
    //            ]
    //    ]
    public function reissue_domestic_ticket($params)
    {
        $requestXml = '<OTA_AirReIssueAutoTicketRQ>
                <POS>
                    <Source PseudoCityCode="' . $this->ibe_config->office . '" OtherID="' . $this->ibe_config->pid . '" />
                </POS>
                <DemandTicketDetail ReconfirmSegmentInd="true"
                    ReturnTicketInfoInd="false" LimitSegmentStatusInd="true">
                    <AirItinerary>
                        <OriginDestinationOptions>
                            <OriginDestinationOption>';
        foreach ($params['flights'] as $flight) {
            $requestXml .= '
                                <FlightSegment RPH="1"
                                    DepartureDateTime="' . $flight['air_equip_type'] . '"
                                    ArrivalDateTime="' . $flight['air_equip_type'] . '" CodeshareInd="false"
                                    FlightNumber="' . $flight['flight_number'] . '" Status="' . $flight['action_code'] . '" SegmentType="' . $flight['type'] . '">
                                    <DepartureAirport LocationCode="' . $flight['departure_airport'] . '" />
                                    <ArrivalAirport LocationCode="' . $flight['arrival_airport'] . '" />
                                    <Equipment AirEquipType="' . $flight['air_equip_type'] . '" />
                                    <MarketingAirline Code="' . $flight['marketing_airline'] . '" />
                                    <OperatingAirline Code="' . $flight['operation_airline'] . '" />
                                    <BookingClassAvail ResBookDesigCode="' . $flight['cabin'] . '" />
                                </FlightSegment>';
        }
        $requestXml .= '
                            </OriginDestinationOption>
                        </OriginDestinationOptions>
                    </AirItinerary>
                    <BookingReferenceID ID="' . $params['pnr'] . '" ID_Context="PNR" />
                    <PaymentInfo PaymentType="CASH" CurrencyCode="CNY">
                    </PaymentInfo>
                    <!-- 退改价格信息 -->
                    <ReIssueInfos>';
        foreach ($params['tickets'] as $ticket) {
            $requestXml .= '
                        <ReIssueInfo>
                            <ReIssuePriceRQ>
                                <!-- 原票号 -->
                                <TicketNumber_Before>' . $ticket['ticket_number_before'] . '</TicketNumber_Before>
                                <IsInfant>' . ($ticket['is_infant'] ? "true" : "false") . '</IsInfant>
                                <PassName>' . $ticket['passenger_name'] . '</PassName>
                                <InvoluntaryIdentifier>' . ($ticket['involuntary_identifier'] ? "true" : "false") . '</InvoluntaryIdentifier>
                                <AirlineCode>' . $ticket['airline'] . '</AirlineCode>
                                <IataCode>' . $this->ibe_config->iata_code . '</IataCode>
                                <CityCode>' . $this->ibe_config->city . '</CityCode>
                                <Endorsement Info="' . $ticket['ei'] . '" />
                            </ReIssuePriceRQ>
                            <ReIssuePriceInfo>
                                <ExchangeType>' . $ticket['exchange_type'] . '</ExchangeType>
                                <ExchangeFee>' . $ticket['exchange_fee'] . '</ExchangeFee>
                                <ChangeFee>' . $ticket['change_fee'] . '</ChangeFee>
                                <TicketFare>' . $ticket['ticket_fare'] . '</TicketFare>
                                <FareDiff>' . $ticket['fare_diff'] . '</FareDiff>
                                <TaxDiff>' . $ticket['tax_diff'] . '</TaxDiff>
                                <TotalFare>' . $ticket['total_fare'] . '</TotalFare>
                            </ReIssuePriceInfo>
                        </ReIssueInfo>';
        }
        $requestXml .= '
                    </ReIssueInfos>
                </DemandTicketDetail>
            </OTA_AirReIssueAutoTicketRQ>';

        $xml       = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/reissue_domestic_ticket.xml'));
        $tickets   = $xml->TicketItemInfo;
        $arrTicket = [];
        foreach ($tickets as $ticket) {
            $arrFlight = [];
            foreach ($ticket->FlightSegment as $flight) {
                $arrFlight[] = [
                    'departure_airport' => (string)$flight->DepartureAirport['LocationCode'],
                    'arrival_airport'   => (string)$flight->ArrivalAirport['LocationCode'],
                    'marketing_airline' => (string)$flight->MarketingAirline['Code'],
                    'departure_time'    => (string)$flight['DepartureDateTime'],
                    'flight_number'     => (string)$flight['FlightNumber'],
                    'ticket_status'     => (string)$flight['TicketStatus'],
                    'cabin'             => (string)$flight['ResBookDesigCode'],
                ];
            }
            $arrTicket[] = [
                'ticket_number'  => (string)$ticket['TicketNumber'],
                'total_amount'   => (string)$ticket['TotalAmount'],
                'currency'       => (string)$ticket['CurrencyCode'],
                'payment_type'   => (string)$ticket['PaymentType'],
                'ei'             => (string)$ticket['Endorsement'],
                'passenger_type' => (string)$ticket->PassengerName['PassengerTypeCode'],
                'passenger_name' => (string)$ticket->PassengerName->Surname,
                'flights'        => $arrFlight,
            ];
        }

        return $arrTicket;
    }

    public function reissue_domestic_bop_ticket($params)
    {
        $requestXml = '<OTA_AirBOPReissueRQ>
                <POS>
                    <Source PseudoCityCode="' . $this->ibe_config->office . '" OtherID="' . $this->ibe_config->pid . '" />
                </POS>
                <DemandTicketDetail ReconfirmSegmentInd="true"
                    ReturnTicketInfoInd="false" LimitSegmentStatusInd="true"
                    DPayPassword="123456">
                    <AirItinerary>
                        <OriginDestinationOptions>
                            <OriginDestinationOption>';
        foreach ($params['flights'] as $flight) {
            $requestXml .= '
                                <FlightSegment RPH="1"
                                    DepartureDateTime="' . $flight['air_equip_type'] . '"
                                    ArrivalDateTime="' . $flight['air_equip_type'] . '" CodeshareInd="false"
                                    FlightNumber="' . $flight['flight_number'] . '" Status="' . $flight['action_code'] . '" SegmentType="' . $flight['type'] . '">
                                    <DepartureAirport LocationCode="' . $flight['departure_airport'] . '" />
                                    <ArrivalAirport LocationCode="' . $flight['arrival_airport'] . '" />
                                    <Equipment AirEquipType="' . $flight['air_equip_type'] . '" />
                                    <MarketingAirline Code="' . $flight['marketing_airline'] . '" />
                                    <OperatingAirline Code="' . $flight['operation_airline'] . '" />
                                    <BookingClassAvail ResBookDesigCode="' . $flight['cabin'] . '" />
                                </FlightSegment>';
        }
        $requestXml .= '
                            </OriginDestinationOption>
                        </OriginDestinationOptions>
                    </AirItinerary>
                    <BookingReferenceID ID="' . $params['pnr'] . '" ID_Context="PNR" />
                    <PaymentInfo PaymentType="CASH" CurrencyCode="CNY">
                    </PaymentInfo>
                    <!-- 退改价格信息 -->
                    <ReIssueInfos>';
        foreach ($params['tickets'] as $ticket) {
            $requestXml .= '
                        <ReIssueInfo>
                            <ReIssuePriceRQ>
                                <!-- 原票号 -->
                                <TicketNumber_Before>' . $ticket['ticket_number_before'] . '</TicketNumber_Before>
                                <IsInfant>' . ($ticket['is_infant'] ? "true" : "false") . '</IsInfant>
                                <PassName>' . $ticket['passenger_name'] . '</PassName>
                                <InvoluntaryIdentifier>' . ($ticket['involuntary_identifier'] ? "true" : "false") . '</InvoluntaryIdentifier>
                                <AirlineCode>' . $ticket['airline'] . '</AirlineCode>
                                <IataCode>' . $this->ibe_config->iata_code . '</IataCode>
                                <CityCode>' . $this->ibe_config->city . '</CityCode>
                                <Endorsement Info="' . $ticket['ei'] . '" />
                            </ReIssuePriceRQ>
                            <ReIssuePriceInfo>
                                <ExchangeType>' . $ticket['exchange_type'] . '</ExchangeType>
                                <ExchangeFee>' . $ticket['exchange_fee'] . '</ExchangeFee>
                                <ChangeFee>' . $ticket['change_fee'] . '</ChangeFee>
                                <TicketFare>' . $ticket['ticket_fare'] . '</TicketFare>
                                <FareDiff>' . $ticket['fare_diff'] . '</FareDiff>
                                <TaxDiff>' . $ticket['tax_diff'] . '</TaxDiff>
                                <TotalFare>' . $ticket['total_fare'] . '</TotalFare>
                            </ReIssuePriceInfo>
                        </ReIssueInfo>';
        }
        $requestXml .= '
                    </ReIssueInfos>
                </DemandTicketDetail>
            </OTA_AirBOPReissueRQ>';

        $xml       = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/reissue_domestic_ticket.xml'));
        $tickets   = $xml->TicketItemInfo;
        $arrTicket = [];
        foreach ($tickets as $ticket) {
            $arrFlight = [];
            foreach ($ticket->FlightSegment as $flight) {
                $arrFlight[] = [
                    'departure_airport' => (string)$flight->DepartureAirport['LocationCode'],
                    'arrival_airport'   => (string)$flight->ArrivalAirport['LocationCode'],
                    'marketing_airline' => (string)$flight->MarketingAirline['Code'],
                    'departure_time'    => (string)$flight['DepartureDateTime'],
                    'flight_number'     => (string)$flight['FlightNumber'],
                    'ticket_status'     => (string)$flight['TicketStatus'],
                    'cabin'             => (string)$flight['ResBookDesigCode'],
                ];
            }
            $arrTicket[] = [
                'ticket_number'  => (string)$ticket['TicketNumber'],
                'total_amount'   => (string)$ticket['TotalAmount'],
                'currency'       => (string)$ticket['CurrencyCode'],
                'payment_type'   => (string)$ticket['PaymentType'],
                'ei'             => (string)$ticket['Endorsement'],
                'passenger_type' => (string)$ticket->PassengerName['PassengerTypeCode'],
                'passenger_name' => (string)$ticket->PassengerName->Surname,
                'flights'        => $arrFlight,
            ];
        }

        return $arrTicket;
    }

    // 取水PNR后查询退票费用
    // 调用参数:
    //    [
    //        [
    //            'ticket_number' => '880-4196191380',
    //        ],
    //        [
    //            'ticket_number' => '880-4196191381',
    //        ],
    //    ]
    //
    // 返回数据:
    //    [
    //        [880-880-4196191380] => // 查询的电子票号
    //            [
    //                [gross_refund] => 1700 // 毛退款额
    //                [net_refund] => 870 // 净退款额
    //                [deduction] => 170 // 扣款
    //                [remarks] => // 备注信息
    //                    [
    //                        [0] => 
    //                        [1] => 
    //                    ]
    //                [coupon_numbers] => // 票证编号
    //                    [
    //                        [0] => 1000
    //                        [1] => 0000
    //                        [2] => 0000
    //                        [3] => 0000
    //                    ]
    //                [taxes] => // 税费
    //                    [
    //                        [0] => 
    //                            [
    //                                [tax_code] => CN // 税代码
    //                                [amount] => 50 // 金额
    //                            ]
    //
    //                        [1] => 
    //                            [
    //                                [tax_code] => YQ
    //                                [amount] => 140
    //                            ]
    //
    //                    ]
    //                [print_number] => 40 // 打印机
    //                [print_type] => BSP_DOMESTIC_ETICKET // 打印机类型
    //                [payment_type] => CASH // 支付方式
    //                [payment_currency_code] => CNY // 货币
    //                [commission] => // 代理费
    //                    [
    //                        [commission_amount] => 0 // 代理费
    //                        [commission_percent] => 50 // 代理费率
    //                    ]
    //                [passenger_surname] => WEN/YA // 乘客姓名
    //                [ticket_number] => 880-880-4196191380 // 电子票号
    //            ]
    //    ]
    public function query_refund_fee($params)
    {
        $requestXml = '<TES_AirTicketRefundRQ>
            <POS>
                <Source PseudoCityCode="' . $this->ibe_config->office . '" />
            </POS>
            <TicketRefundInfoDetails>';
        foreach ($params as $ticket) {
            $requestXml .= '
                <TicketRefundInfoDetail ReturnRefundFormInfoInd="true" ConfirmRefundInd="false">
                    <TicketItemInfo TicketNumber="' . $ticket['ticket_number'] . '" />
                    <PrinterInfo Number="' . $this->ibe_config->pid . '" Type="BSP_DOMESTIC_ETICKET" />
                </TicketRefundInfoDetail>';
        }
        $requestXml .= '
            </TicketRefundInfoDetails>
        </TES_AirTicketRefundRQ>';

        echo $requestXml;

        $xml               = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/query_refund_fee.xml'));
        $refundFormDetails = $xml->RefundFormDetails->RefundFormDetail;
        $arrRefundFormInfo = [];
        foreach ($refundFormDetails as $refundFormDetail) {
            $arrRemark        = [];
            $arrCouponNumber  = [];
            $arrTaxes         = [];
            $arrCommission    = [];
            $passengerSurname = '';
            $refundFormInfo   = $refundFormDetail->RefundForm->RefundFormInfo;
            foreach ($refundFormInfo->Remark as $remark) {
                $arrRemark[] = (string)$remark;
            }
            foreach ($refundFormInfo->CouponNumber as $couponNumber) {
                $arrCouponNumber[] = (string)$couponNumber;
            }
            foreach ($refundFormInfo->Taxes->Tax as $tax) {
                $arrTaxes[] = [
                    'tax_code' => (string)$tax['TaxCode'],
                    'amount'   => (float)$tax['Amount'],
                ];
            }
            if ($refundFormInfo->CommissionInfo->Base) {
                $arrCommission = [
                    'commission_amount'  => (float)$refundFormInfo->CommissionInfo->Base['Amount'],
                    'commission_percent' => (float)$refundFormInfo->CommissionInfo->Base['Percent'],
                ];
            }
            if ($refundFormInfo->PassengerName->Surname) {
                $passengerSurname = (string)$refundFormInfo->PassengerName->Surname;
            }
            $arrRefundFormInfo[(string)$refundFormInfo->TicketItemInfo['TicketNumber']] = [
                'gross_refund'          => (float)$refundFormInfo['GrossRefund'],
                'net_refund'            => (float)$refundFormInfo['NetRefund'],
                'deduction'             => (float)$refundFormInfo['Deduction'],
                'remarks'               => $arrRemark,
                'coupon_numbers'        => $arrCouponNumber,
                'taxes'                 => $arrTaxes,
                'print_number'          => (int)$refundFormInfo->PrinterInfo['Number'],
                'print_type'            => (string)$refundFormInfo->PrinterInfo['Type'],
                'payment_type'          => (string)$refundFormInfo->PaymentInfo['PaymentType'],
                'payment_currency_code' => (string)$refundFormInfo->PaymentInfo['CurrencyCode'],
                'commission'            => $arrCommission,
                'passenger_surname'     => $passengerSurname,
                'ticket_number'         => (string)$refundFormInfo->TicketItemInfo['TicketNumber'],
            ];
        }

        return $arrRefundFormInfo;
    }

    // 不取消PNR计算退票费
    // 调用参数：
    //    [
    //        'ticket_number' => '880-4196191380', // 电子票号
    //        'eticket_type' => 'DOMESTIC', // 电子票类型：DOMESTIC国内票 INTERNATIONAL国际票
    //        'is_infant' => true, // 是否婴儿
    //    ];
    //
    // 返回数据:
    //
    //    [
    //        [ticket_number] => 1020837901 // 电子票号
    //        [ticket_number_end] => 20837901 // 电子票号结束编码
    //        [passenger_name] => WU/XIAO CHD // 乘客姓名
    //        [conjunction] => 1 // 电子票号张数
    //        [payment_method] => TC // 退款支付方式
    //        [payment_currency_type] => CN // 货币
    //        [gross_refund] => 1140 // 毛退票额总和
    //        [deduction] => 0 // 退票手续费总和
    //        [net_refund] => 1250 // 净退总和
    //        [airline_refund] => 1280 // 航空公司向旅客退还的费用
    //        [commission] => // 佣金
    //            [
    //                [commission_amount] => 30 // 佣金金额
    //                [commission_rate] => 0 // 佣金比例
    //            ]
    //        [coupon_numbers] => // 退票航段
    //            [
    //                [0] => 1200
    //                [1] => 0000
    //                [2] => 0000
    //                [3] => 0000
    //            ]
    //        [taxes] => // 多条税费信息
    //            [
    //                [0] => 
    //                    [
    //                        [tax_code] => CN // 税代码 CN机场建设费，YQ燃油附加费
    //                        [amount] => 0 // 税款金额
    //                    ]
    //                [1] => 
    //                    [
    //                        [tax_code] => YQ
    //                        [amount] => 140
    //                    ]
    //
    //            ]
    //    ]
    public function query_refund_fee_no_cancel_pnr($params)
    {
        $requestXml = '<TES_AirTicketRefundCPRQ>
                <POS>
                    <Source CityCode="' . $this->ibe_config->city . '" PseudoCityCode="' . $this->ibe_config->office . '">
                        <RequestorID Type="13" IDContext="IATA_Number" ID="' . $this->ibe_config->iata_code . '" />
                    </Source>
                </POS>
                <TicketRefundDetailInfo>
                    <PrinterInfo PrintDeviceNo="' . $this->ibe_config->pid . '" />
                    <TicketRefundInfo ETicketType="' . $params['eticket_type'] . '" TicketNumber="' . $params['ticket_number'] . '"';
        if ($params['is_infant']) {
            $requestXml .= ' PassengerType="I" ';
        }
        $requestXml .= '
                    />
                </TicketRefundDetailInfo>
            </TES_AirTicketRefundCPRQ>';

        $xml              = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/query_refund_fee_no_cancel_pnr.xml'));
        $refundFormDetail = $xml->RefundFormDetail;

        $arrCouponNumber = [];
        $arrTaxes        = [];
        $arrCommission   = [];

        foreach ($refundFormDetail->CouponNumbers->CouponNumber as $couponNumber) {
            $arrCouponNumber[] = (string)$couponNumber['CouponNo'];
        }
        foreach ($refundFormDetail->Taxes->Tax as $tax) {
            $arrTaxes[] = [
                'tax_code' => (string)$tax['TaxCode'],
                'amount'   => (float)$tax['TaxAmount'],
            ];
        }
        if ($refundFormDetail->CommissionInfo) {
            $arrCommission = [
                'commission_amount' => (float)$refundFormDetail->CommissionInfo->Commission,
                'commission_rate'   => (float)$refundFormDetail->CommissionInfo->CommissionRate,
            ];
        }

        $arrRefundFormInfo = [
            'ticket_number'         => (string)$refundFormDetail->TicketItemInfo['TicketNumber'],
            'ticket_number_end'     => (string)$refundFormDetail->TicketItemInfo['TktNumberEnd'],
            'passenger_name'        => (string)$refundFormDetail->TicketItemInfo['PassengerName'],
            'conjunction'           => (string)$refundFormDetail->TicketItemInfo['Conjunction'],
            'payment_method'        => (string)$refundFormDetail->RefundInfo->PayMethod,
            'payment_currency_type' => (string)$refundFormDetail->RefundInfo->CurrencyType,
            'gross_refund'          => (float)$refundFormDetail->RefundInfo->GrossRefund,
            'deduction'             => (float)$refundFormDetail->RefundInfo->Deduction,
            'net_refund'            => (float)$refundFormDetail->RefundInfo->NetRefund,
            'airline_refund'        => (float)$refundFormDetail->RefundInfo->AirlineRefund,
            'commission'            => $arrCommission,
            'coupon_numbers'        => $arrCouponNumber,
            'taxes'                 => $arrTaxes,
        ];

        return $arrRefundFormInfo;
    }

    // 退票
    // 调用参数:
    //    [
    //          'ticket_number' => '880-4196191380',
    //          'type' => 1, // 1国内票 2国际票
    //    ]
    //
    // 返回数据:
    //    [
    //                [ticket_number] => 880-880-4196191380 // 电子票号
    //                [passenger_name] => WEN/YA // 乘客姓名
    //                [gross_refund] => 1700 // 毛退票额总和
    //                [deduction] => 170 // 退票手续费总和
    //                [net_refund] => 870 // 净退总和
    //                [payment_method] => CASH // 支付方式
    //                [payment_currency_type] => CNY // 货币
    //                [commission] => // 代理费
    //                    [
    //                        [commission_amount] => 0 // 代理费
    //                        [commission_rate] => 50 // 代理费率
    //                    ]
    //                [coupon_numbers] => // 票证编号
    //                    [
    //                        [0] => 1000
    //                        [1] => 0000
    //                        [2] => 0000
    //                        [3] => 0000
    //                    ]
    //                [taxes] => // 税费
    //                    [
    //                        [0] => 
    //                            [
    //                                [tax_code] => CN // 税代码
    //                                [amount] => 50 // 金额
    //                            ]
    //
    //                        [1] => 
    //                            [
    //                                [tax_code] => YQ
    //                                [amount] => 140
    //                            ]
    //
    //                    ]
    //                [remarks] => // 备注信息
    //                    [
    //                        [0] => 
    //                        [1] => 
    //                    ]
    //                [print_number] => 40 // 打印机
    //                [print_type] => BSP_DOMESTIC_ETICKET // 打印机类型
    //    ]
    public function refund_ticket($params)
    {
        $requestXml = '<TES_AirTicketRefundRQ>
            <POS>
                <Source PseudoCityCode="' . $this->ibe_config->office . '" />
            </POS>
            <TicketRefundInfoDetails>
                <TicketRefundInfoDetail ReturnRefundFormInfoInd="true" ConfirmRefundInd="true">
                    <TicketItemInfo TicketNumber="' . $params['ticket_number'] . '" />
                    <PrinterInfo Number="' . $params['type'] . '" Type="' . ($params['type'] == 1 ? 'BSP_DOMESTIC_ETICKET' : 'BSP_INTERNATIONAL_ETICKET') . '" />
                </TicketRefundInfoDetail>
            </TicketRefundInfoDetails>
        </TES_AirTicketRefundRQ>';

        $xml  = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/query_refund_fee.xml'));
        $info = $xml->RefundFormDetails->RefundFormDetail->RefundForm->RefundFormInfo;

        $arrRemark        = [];
        $arrCouponNumber  = [];
        $arrTaxes         = [];
        $arrCommission    = [];
        $passengerSurname = '';

        foreach ($info->Remark as $remark) {
            $arrRemark[] = (string)$remark;
        }
        foreach ($info->CouponNumber as $couponNumber) {
            $arrCouponNumber[] = (string)$couponNumber;
        }
        foreach ($info->Taxes->Tax as $tax) {
            $arrTaxes[] = [
                'tax_code' => (string)$tax['TaxCode'],
                'amount'   => (string)$tax['Amount'],
            ];
        }
        if ($info->CommissionInfo->Base) {
            $arrCommission = [
                'commission_amount' => (string)$info->CommissionInfo->Base['Amount'],
                'commission_rate'   => (string)$info->CommissionInfo->Base['Percent'],
            ];
        }
        if ($info->PassengerName->Surname) {
            $passengerSurname = (string)$info->PassengerName->Surname;
        }
        $arrRefundFormInfo = [
            'ticket_number'         => (string)$info->TicketItemInfo['TicketNumber'],
            'passenger_name'        => $passengerSurname,
            'gross_refund'          => (string)$info['GrossRefund'],
            'deduction'             => (string)$info['Deduction'],
            'net_refund'            => (string)$info['NetRefund'],
            'payment_method'        => (string)$info->PaymentInfo['PaymentType'],
            'payment_currency_type' => (string)$info->PaymentInfo['CurrencyCode'],
            'commission'            => $arrCommission,
            'coupon_numbers'        => $arrCouponNumber,
            'taxes'                 => $arrTaxes,
            'remarks'               => $arrRemark,
            'print_number'          => (string)$info->PrinterInfo['Number'],
            'print_type'            => (string)$info->PrinterInfo['Type'],
        ];

        return $arrRefundFormInfo;
    }

    // 废票
    // 调用参数：
    //    [
    //        'pnr' => 'HQRHX7', // PNR
    //        'ticket_number' => '999-3944318760', // 电子票号
    //        'is_bop' => false, // 是否BOP支付方式
    //    ]
    //
    // 返回数据：
    // true, false
    public function void_ticket($params)
    {
        $requestXml = '<OTA_AirTicketVoidRQ>
                <POS>
                    <Source PseudoCityCode="' . $this->ibe_config->office . '" OtherID="' . $this->ibe_config->pid . '" />
                </POS>
                <TicketVoidDetail>
                    <BookingReferenceID ID="' . $params['pnr'] . '" ID_Context="PNR" />
                    <TicketItemInfo TicketNumber="' . $params['ticket_number'] . '" />
                </TicketVoidDetail>
                <IsBOP>' . ($params['is_bop'] ? "true" : "false") . '</IsBOP>
            </OTA_AirTicketVoidRQ>';

        echo $requestXml;

        $xml = simplexml_load_string(file_get_contents(ROOTPATH . 'writable/xml/void_ticket.xml'));
        if ($xml->Success) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * IBE View Report接口 - 按时间范围获取销售报告数据
     *
     * @param  string  $startTime  开始时间 YYYY-MM-DD HH:MM:SS
     * @param  string  $endTime  结束时间 YYYY-MM-DD HH:MM:SS
     *
     * @return array
     * @throws \Exception
     */
    public function viewReportByTimeRange(string $startTime, string $endTime): array
    {
        // 构建请求参数
        $requestParams = $this->buildTimeRangeRequestParams($startTime, $endTime);

        // TODO: 实现真实的HTTP请求调用
        // $response = $this->callIbeApi('view_report', $requestParams);

        // 使用模拟数据
        $responseFile = ROOTPATH . 'writable/xml/international/view_report_res.json';
        if (!file_exists($responseFile)) {
            throw new \Exception("模拟数据文件不存在: {$responseFile}");
        }

        $responseData = file_get_contents($responseFile);
        $data         = json_decode($responseData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("解析响应数据失败: " . json_last_error_msg());
        }

        return $data;
    }

    /**
     * IBE PNR结构化信息提取接口
     *
     * @param  string  $pnr  PNR编码
     *
     * @return array
     * @throws \Exception
     */
    public function viewReportPnr(string $pnr): array
    {
        // 构建PNR查询请求XML
        $requestXml = $this->buildPnrDetailRequestXml($pnr);

        // TODO: 实现真实的HTTP请求调用
        // $response = $this->callIbeApi('view_report_pnr', $requestXml);

        // 使用模拟数据
        $responseFile = ROOTPATH . 'writable/xml/international/view_report_pnr_res.xml';
        if (!file_exists($responseFile)) {
            throw new \Exception("PNR模拟数据文件不存在: {$responseFile}");
        }

        $responseData = file_get_contents($responseFile);

        return $this->parsePnrDetailResponse($responseData);
    }

    /**
     * IBE电子客票信息提取接口
     *
     * @param  string  $ticketNumber  票号
     * @param  string  $pnr  PNR编码
     *
     * @return array
     * @throws \Exception
     */
    public function viewReportTicket(string $ticketNumber, string $pnr = ''): array
    {
        // 构建票务查询请求XML
        $requestXml = $this->buildTicketDetailRequestXml($ticketNumber, $pnr);

        // TODO: 实现真实的HTTP请求调用
        // $response = $this->callIbeApi('view_report_ticket', $requestXml);

        // 使用模拟数据
        $responseFile = ROOTPATH . 'writable/xml/international/view_report_ticket_res.xml';
        if (!file_exists($responseFile)) {
            throw new \Exception("票务模拟数据文件不存在: {$responseFile}");
        }

        $responseData = file_get_contents($responseFile);

        return $this->parseTicketDetailResponse($responseData);
    }

    /**
     * 构建时间范围请求参数
     *
     * @param  string  $startTime
     * @param  string  $endTime
     *
     * @return array
     * @throws \Exception
     */
    private function buildTimeRangeRequestParams(string $startTime, string $endTime): array
    {
        $startDateTime = new DateTime($startTime);
        $endDateTime   = new DateTime($endTime);

        return [
            'office'             => $this->ibe_config->office,
            'ticketingDate'      => [
                'day'   => $startDateTime->format('d'),
                'month' => $startDateTime->format('m'),
                'year'  => $startDateTime->format('Y'),
            ],
            'salesDateTimeStart' => [
                'hour'    => $startDateTime->format('H'),
                'minutes' => $startDateTime->format('i'),
                'seconds' => $startDateTime->format('s'),
            ],
            'salesDateTimeEnd'   => [
                'hour'    => $endDateTime->format('H'),
                'minutes' => $endDateTime->format('i'),
                'seconds' => $endDateTime->format('s'),
            ],
        ];
    }

    /**
     * 构建PNR详情查询请求XML
     *
     * @param  string  $pnr
     *
     * @return string
     */
    private function buildPnrDetailRequestXml(string $pnr): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
<OTA_AirResRetRQ EchoToken="String" TimeStamp="String" Version="String" Target="String">
    <POS>
        <Source PseudoCityCode="' . $this->ibe_config->office . '" />
    </POS>
    <BookingReferenceID ID="' . $pnr . '">
    </BookingReferenceID>
</OTA_AirResRetRQ>';
    }

    /**
     * 构建票务详情查询请求XML
     *
     * @param  string  $ticketNumber
     * @param  string  $pnr
     *
     * @return string
     */
    private function buildTicketDetailRequestXml(string $ticketNumber, string $pnr): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
                    <TES_AirTicketRetRQ>
                        <POS>
                            <Source PseudoCityCode="' . $this->ibe_config->office . '"/>
                        </POS>
                        <TicketNumber>' . $ticketNumber . '</TicketNumber>
                        <SecondFactor Code="CN" Value="' . $pnr . '"/>
                    </TES_AirTicketRetRQ>';
    }

    /**
     * 解析PNR详情响应
     *
     * @param  string  $responseData
     *
     * @return array
     * @throws \Exception
     */
    private function parsePnrDetailResponse(string $responseData): array
    {
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($responseData);

        if ($xml === false) {
            $errors = libxml_get_errors();
            throw new \Exception("解析PNR响应XML失败: " . implode(', ', array_column($errors, 'message')));
        }

        $result = [
            'pnr'            => (string)$xml->AirResRet->BookingReferenceID['ID'],
            'passengers'     => [],
            'segments'       => [],
            'contact_info'   => '',
            'ticketing_info' => [],
        ];

        // 解析乘客信息
        if (isset($xml->AirResRet->AirTraveler)) {
            foreach ($xml->AirResRet->AirTraveler as $traveler) {
                $result['passengers'][] = [
                    'rph'            => (string)$traveler['RPH'],
                    'passenger_type' => (string)$traveler['PassengerTypeCode'],
                    'gender'         => (string)$traveler['Gender'],
                    'birthday'       => (string)$traveler['Birthday'],
                    'surname'        => (string)$traveler->PersonName->Surname,
                    'given_name'     => (string)$traveler->PersonName->GivenName,
                    'name_pnr'       => (string)$traveler->PersonName->NamePNR,
                    'telephone'      => (string)$traveler->Telephone['PhoneNumber'],
                    'email'          => (string)$traveler->Email,
                    'address'        => [
                        'country_name' => (string)$traveler->Address->CountryName,
                        'state_prov'   => (string)$traveler->Address->StateProv,
                        'city_name'    => (string)$traveler->Address->CityName,
                        'address_line' => (string)$traveler->Address->AddressLine,
                    ],
                    'nationality'    => (string)$traveler->Address->CountryName,
                    'document'       => [
                        'doc_type'               => (string)$traveler->Document['DocType'],
                        'doc_type_detail'        => (string)$traveler->Document['DocTypeDetail'],
                        'doc_id'                 => (string)$traveler->Document['DocID'],
                        'expire_date'            => (string)$traveler->Document['ExpireDate'],
                        'doc_issue_country'      => (string)$traveler->Document['DocIssueCountry'],
                        'doc_holder_nationality' => (string)$traveler->Document['DocHolderNationality'],
                    ],
                    'age'            => (int)$traveler->PassengerTypeQuantity['Age'],
                    'quantity'       => (int)$traveler->PassengerTypeQuantity['Quantity'],
                ];
            }
        }

        // 解析航段信息
        if (isset($xml->AirResRet->FlightSegments->FlightSegment)) {
            foreach ($xml->AirResRet->FlightSegments->FlightSegment as $segment) {
                $result['segments'][] = [
                    'rph'                => (string)$segment['RPH'],
                    'flight_number'      => (string)$segment['FlightNumber'],
                    'departure_datetime' => (string)$segment['DepartureDateTime'],
                    'arrival_datetime'   => (string)$segment['ArrivalDateTime'],
                    'departure_airport'  => (string)$segment->DepartureAirport['LocationCode'],
                    'arrival_airport'    => (string)$segment->ArrivalAirport['LocationCode'],
                    'marketing_airline'  => (string)$segment->MarketingAirline['Code'],
                    'booking_class'      => (string)$segment->BookingClassAvail['ResBookDesigCode'],
                    'status'             => (string)$segment['Status'],
                    'passenger_number'   => (int)$segment['NumberInParty'],
                ];
            }
        }

        // 解析联系信息
        if (isset($xml->AirResRet->ContactInfo)) {
            $result['contact_info'] = (string)$xml->AirResRet->ContactInfo['ContactInfo'];
        }

        // 解析出票信息
        if (isset($xml->AirResRet->Ticketing)) {
            $result['ticketing_info'] = [
                'is_issued'         => (string)$xml->AirResRet->Ticketing['IsIssued'] === 'true',
                'office_code'       => (string)$xml->AirResRet->Ticketing['OfficeCode'],
                'ticket_time_limit' => (string)$xml->AirResRet->Ticketing['TicketTimeLimit'],
            ];
        }

        return $result;
    }

    /**
     * 解析票务详情响应
     *
     * @param  string  $responseData
     *
     * @return array
     * @throws \Exception
     */
    private function parseTicketDetailResponse(string $responseData): array
    {
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($responseData);

        if ($xml === false) {
            $errors = libxml_get_errors();
            throw new \Exception("解析票务响应XML失败: " . implode(', ', array_column($errors, 'message')));
        }

        $result = [
            'passenger' => [],
            'segments'  => [],
            'ticketing' => [],
            'pricing'   => [],
        ];

        // 解析乘客信息
        if (isset($xml->AirTicketRet->AirTraveler)) {
            $result['passenger'] = [
                'passenger_type' => (string)$xml->AirTicketRet->AirTraveler['PassengerTypeCode'],
                'surname'        => (string)$xml->AirTicketRet->AirTraveler->PersonName->Surname,
            ];
        }

        // 解析航段信息
        if (isset($xml->AirTicketRet->FlightSegments)) {
            foreach ($xml->AirTicketRet->FlightSegments as $segmentGroup) {
                foreach ($segmentGroup->FlightSegment as $segment) {
                    $result['segments'][] = [
                        'rph'                => (string)$segment['RPH'],
                        'flight_number'      => (string)$segment['FlightNumber'],
                        'departure_datetime' => (string)$segment['DepartureDateTime'],
                        'departure_airport'  => (string)$segment->DepartureAirport['LocationCode'],
                        'departure_terminal' => (string)$segment->DepartureAirport['Terminal'],
                        'arrival_datetime'   => (string)$segment['ArrivalDateTime'],
                        'arrival_airport'    => (string)$segment->ArrivalAirport['LocationCode'],
                        'arrival_terminal'   => (string)$segment->ArrivalAirport['Terminal'],
                        'marketing_airline'  => (string)$segment->MarketingAirline['Code'],
                        'operating_airline'  => (string)$segment->OperatingAirline['Code'],
                        'booking_class'      => (string)$segment->BookingClassAvail['ResBookDesigCode'],
                        'ticket_status'      => (string)$segment['TicketStatus'],
                        'segment_status'     => (string)$segment['SegmentStatus'],
                        'pnr_no'             => (string)$segment['PnrNo'],
                        'crs_pnr_no'         => (string)$segment['CrsPnrNo'],
                        'baggage_weight'     => (int)$segment->Baggage['BaggageWeight'],
                        'baggage_piece'      => (int)$segment->Baggage['BaggagePiece'],
                    ];
                }
            }
        }

        // 解析出票信息
        if (isset($xml->AirTicketRet->Ticketing->TicketItemInfo)) {
            $ticketInfo          = $xml->AirTicketRet->Ticketing->TicketItemInfo;
            $result['ticketing'] = [
                'ticket_number' => (string)$ticketInfo['TicketNumber'],
                'total_amount'  => (float)$ticketInfo['TotalAmount'],
                'endorsement'   => (string)$ticketInfo['Endorsement'],
                'issue_airline' => (string)$ticketInfo['IssueAirline'],
                'org_city'      => (string)$ticketInfo['OrgCity'],
                'dst_city'      => (string)$ticketInfo['DstCity'],
                'tax'           => (float)$ticketInfo['Tax'],
                'eticket_type'  => (string)$ticketInfo['ETicketType'],
            ];
        }

        // 解析价格信息
        if (isset($xml->AirTicketRet->AirItineraryPricingInfo->ItinTotalFare)) {
            $fareInfo          = $xml->AirTicketRet->AirItineraryPricingInfo->ItinTotalFare;
            $result['pricing'] = [
                'base_fare' => [
                    'amount'   => (float)$fareInfo->BaseFare['Amount'],
                    'currency' => (string)$fareInfo->BaseFare['CurrencyCode'],
                ],
                'taxes'     => [],
            ];

            // 解析税费信息
            if (isset($fareInfo->Taxes->Tax)) {
                foreach ($fareInfo->Taxes->Tax as $tax) {
                    $result['pricing']['taxes'][] = [
                        'tax_code' => (string)$tax['TaxCode'],
                        'amount'   => (float)$tax['Amount'],
                        'currency' => (string)$tax['CurrencyCode'],
                    ];
                }
            }
        }

        return $result;
    }

    /**
     * 过滤出票订单
     *
     * @param  array  $tslDetails
     *
     * @return array
     */
    public function filterTicketOrders(array $tslDetails): array
    {
        $ticketOrders = [];

        foreach ($tslDetails as $detail) {
            // 只处理出票订单，过滤退款、改签等订单
            if ($detail['saleStatusCode'] === 'ISSU') {
                $ticketOrders[] = $detail;
            }
        }

        return $ticketOrders;
    }
}