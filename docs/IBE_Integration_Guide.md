# IBE接口整合指南

## 概述

本文档描述了IBE（International Booking Engine）接口的整合实现，包括PNR数据拉取、结构化信息提取和电子客票信息提取等功能。

## 架构设计

### 核心组件

1. **Libraries\Api\IBE\Ticket.php** - IBE接口核心类
2. **Services\PnrData\PullPnrDataService.php** - PNR数据拉取服务
3. **Commands\PullPnrData.php** - 定时任务脚本

### 数据流程

```
1. View Report接口 → 获取销售报告数据
2. 过滤出票订单 → 筛选saleStatusCode为"ISSU"的记录
3. PNR详情接口 → 根据PNR获取结构化信息
4. 票务详情接口 → 根据票号获取电子客票信息
5. 数据整合保存 → 存储到数据库
```

## IBE Ticket类方法

### 主要接口方法

#### 1. viewReport($date, $office)
获取销售报告数据
```php
$ticket = new Ticket();
$reportData = $ticket->viewReport('2022-09-26', 'BJS191');
```

#### 2. viewReportPnr($pnr, $office)
获取PNR结构化信息
```php
$pnrDetail = $ticket->viewReportPnr('HZNR2X', 'BJS191');
```

#### 3. viewReportTicket($ticketNumber, $office, $pnr)
获取电子客票信息
```php
$ticketDetail = $ticket->viewReportTicket('479-3995946711', 'BJS191', 'HZNR2X');
```

### 辅助方法

#### 1. filterTicketOrders($tslDetails)
过滤出票订单
```php
$ticketOrders = $ticket->filterTicketOrders($reportData['tslDetails']);
```

#### 2. mapPassengerType($passengerType)
映射乘客类型
```php
$type = $ticket->mapPassengerType('ADT'); // 返回 1 (成人)
```

## 数据结构

### View Report响应结构
```json
{
  "tslDetails": [
    {
      "pnr": "HZNR2X",
      "ticketNumber": "479-3995946711",
      "saleStatusCode": "ISSU",
      "totalAmount": 1300.00,
      "origin": "PEK",
      "destination": "HAK"
    }
  ]
}
```

### PNR详情响应结构
```php
[
  'pnr' => 'HQ8WEW',
  'passengers' => [
    [
      'rph' => '1',
      'passenger_type' => 'ADT',
      'surname' => '郭应禄'
    ]
  ],
  'segments' => [
    [
      'rph' => '3',
      'flight_number' => '1355',
      'departure_airport' => 'PEK',
      'arrival_airport' => 'HAK'
    ]
  ],
  'contact_info' => '/T SHA/021-52918766',
  'ticketing_info' => []
]
```

### 票务详情响应结构
```php
[
  'passenger' => [
    'passenger_type' => 'CHD',
    'surname' => 'DIAO/SI'
  ],
  'ticketing' => [
    'ticket_number' => '999-4171348017',
    'total_amount' => 33760.0
  ],
  'pricing' => [
    'base_fare' => [
      'amount' => 30000.0,
      'currency' => 'CNY'
    ]
  ]
]
```

## 使用示例

### 基本使用
```php
use App\Libraries\Api\IBE\Ticket;

$ticket = new Ticket();

// 1. 获取销售报告
$reportData = $ticket->viewReport('2022-09-26');

// 2. 过滤出票订单
$ticketOrders = $ticket->filterTicketOrders($reportData['tslDetails']);

// 3. 处理每个出票订单
foreach ($ticketOrders as $order) {
    // 获取PNR详情
    $pnrDetail = $ticket->viewReportPnr($order['pnr']);
    
    // 获取票务详情
    $ticketDetail = $ticket->viewReportTicket($order['ticketNumber'], null, $order['pnr']);
    
    // 处理数据...
}
```

### 定时任务使用
```bash
# 拉取指定日期数据
php spark pull:pnr 2022-09-26

# 拉取昨天数据（默认）
php spark pull:pnr

# 指定OFFICE号
php spark pull:pnr 2022-09-26 --office=BJS191
```

## 配置说明

### IBE配置
在配置文件中设置IBE相关参数：
```php
// app/Config/IBE.php
public $office = 'BJS191';
public $endpoint = 'https://ibe.example.com/api';
```

### 模拟数据文件
- `writable/xml/international/view_report_res.json` - 销售报告模拟数据
- `writable/xml/international/view_report_pnr_res.xml` - PNR详情模拟数据
- `writable/xml/international/view_report_ticket_res.xml` - 票务详情模拟数据

## 数据库表结构

### 主要表
- `ticket_book_orders` - 主订单表
- `ticket_book_order_passengers` - 乘客信息表
- `ticket_book_order_segments` - 航段信息表
- `ticket_book_order_detail` - 订单详情表

## 错误处理

### 常见错误
1. **模拟数据文件不存在** - 检查文件路径
2. **XML解析失败** - 检查XML格式
3. **数据库字段不匹配** - 检查表结构

### 调试方法
```bash
# 测试IBE接口
php spark test:ibe

# 调试PNR数据解析
php spark debug:pnr
```

## 扩展说明

### 真实接口集成
将模拟数据替换为真实HTTP请求：
```php
// TODO: 在Ticket类中实现
private function callIbeApi($endpoint, $data) {
    // 实现HTTP请求逻辑
}
```

### 监控和日志
- 添加接口调用日志
- 实现失败重试机制
- 配置告警通知

## 版本历史

- v1.0 - 初始版本，支持基本的PNR数据拉取
- v1.1 - 整合IBE接口到Ticket类
- v1.2 - 完善错误处理和数据验证
