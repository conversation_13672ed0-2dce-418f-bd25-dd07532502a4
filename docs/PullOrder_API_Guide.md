# 拉取订单接口文档

## 概述

该接口用于从IBE系统拉取出票订单数据，支持时间范围查询和票号/PNR过滤，返回完整的订单信息给前端，不进行数据库存储。

## 接口信息

- **路由**: `/cli/pullOrder`
- **方法**: `GET`
- **控制器**: `App\Controllers\Admin\Order::pullOrder`
- **服务类**: `App\Services\PnrData\PullPnrDataService::pullDataOnly`

## 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| start_time | string | 是 | 开始时间，格式：YYYY-MM-DD HH:MM:SS | 2024-01-01 00:00:00 |
| end_time | string | 是 | 结束时间，格式：YYYY-MM-DD HH:MM:SS | 2024-01-01 23:59:59 |
| ticket_number | string | 否 | 票号过滤，最少2位字符 | 479-3995946711 |
| pnr | string | 否 | PNR编码过滤，最少2位字符 | PNRPNR |

## 请求示例

```bash
GET /cli/pullOrder?start_time=2024-01-01%2000:00:00&end_time=2024-01-01%2023:59:59
GET /cli/pullOrder?start_time=2024-01-01%2000:00:00&end_time=2024-01-01%2023:59:59&ticket_number=479-3995946711
GET /cli/pullOrder?start_time=2024-01-01%2000:00:00&end_time=2024-01-01%2023:59:59&pnr=PNRPNR
```

## 响应格式

### 成功响应

```json
{
    "code": 200,
    "msg": "获取数据成功",
    "data": {
        "total_records": 2,
        "filtered_records": 1,
        "orders": [
            {
                "pnr": "PNRPNR",
                "ticket_number": "479-3995946711",
                "total_amount": "1300.00",
                "currency_code": "CNY",
                "journey_type": 1,
                "journey_type_text": "单程",
                "journey_info": "PEKHAK",
                "passenger_number": 2,
                "contact_name": "ZHOU JIANABCDEFG",
                "contact_telephone": "021-52918766",
                "passengers": [
                    {
                        "rph": "1",
                        "name": "张",
                        "given_name": "三丰",
                        "passenger_type": "ADT",
                        "passenger_type_text": "成人",
                        "doc_type": "1",
                        "doc_id": "350123196708130011",
                        "telephone": "13666666666",
                        "birthday": "1967-08-13",
                        "gender": "MALE"
                    }
                ],
                "segments": [
                    {
                        "rph": "1",
                        "departure_datetime": "2013-12-28 13:55:00",
                        "arrival_datetime": "2013-12-28 15:55:00",
                        "airline": "CA",
                        "flight_number": "931",
                        "departure_airport": "PEK",
                        "arrival_airport": "FRA",
                        "booking_class": "Y"
                    }
                ],
                "ticket_type": "4",
                "begin_flight_date": "2022-10-22",
                "created_time": "2025-06-25 11:14:03"
            }
        ],
        "errors": []
    }
}
```

### 错误响应

```json
{
    "code": 500,
    "msg": "拉取数据失败: 具体错误信息",
    "data": null
}
```

## 响应字段说明

### 主要字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total_records | int | IBE接口返回的总记录数 |
| filtered_records | int | 过滤后的出票订单数量 |
| orders | array | 订单列表 |
| errors | array | 错误信息列表 |

### 订单字段 (orders[])

| 字段名 | 类型 | 说明 |
|--------|------|------|
| pnr | string | PNR编码 |
| ticket_number | string | 票号 |
| total_amount | string | 订单总金额 |
| currency_code | string | 货币代码 |
| journey_type | int | 航程类型：1=单程，2=往返，3=联程-两航段，4=联程-多航段，5=多航段 |
| journey_type_text | string | 航程类型文本描述 |
| journey_info | string | 航程信息（出发地+目的地） |
| passenger_number | int | 乘客数量 |
| contact_name | string | 联系人姓名 |
| contact_telephone | string | 联系电话 |
| passengers | array | 乘客信息列表 |
| segments | array | 航段信息列表 |
| ticket_type | string | 票务类型 |
| begin_flight_date | string | 首航班日期 |
| created_time | string | 数据创建时间 |

### 乘客字段 (passengers[])

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rph | string | 乘客序号 |
| name | string | 姓氏 |
| given_name | string | 名字 |
| passenger_type | string | 乘客类型：ADT=成人，CHD=儿童，INF=婴儿 |
| passenger_type_text | string | 乘客类型文本描述 |
| doc_type | string | 证件类型 |
| doc_id | string | 证件号码 |
| telephone | string | 联系电话 |
| birthday | string | 生日 |
| gender | string | 性别：MALE=男，FEMALE=女 |

### 航段字段 (segments[])

| 字段名 | 类型 | 说明 |
|--------|------|------|
| rph | string | 航段序号 |
| departure_datetime | string | 出发时间 |
| arrival_datetime | string | 到达时间 |
| airline | string | 航空公司代码 |
| flight_number | string | 航班号 |
| departure_airport | string | 出发机场代码 |
| arrival_airport | string | 到达机场代码 |
| booking_class | string | 舱位等级 |

## 数据流程

1. **接收参数** - 验证时间范围和过滤条件
2. **调用IBE接口** - 获取指定时间范围的销售报告数据
3. **过滤出票订单** - 筛选saleStatusCode为"ISSU"的记录
4. **参数过滤** - 根据ticket_number和pnr进一步过滤
5. **获取详细信息** - 调用PNR详情和票务详情接口
6. **数据整合** - 合并基础订单、PNR详情和票务详情
7. **格式化输出** - 转换为前端需要的数据格式

## 注意事项

1. **时间格式**: 必须使用 `YYYY-MM-DD HH:MM:SS` 格式
2. **时间范围**: 开始时间必须早于结束时间
3. **过滤条件**: ticket_number和pnr参数可选，支持精确匹配
4. **数据来源**: 当前使用模拟数据，实际部署时需要配置真实的IBE接口
5. **错误处理**: 单个订单获取失败不会影响其他订单，错误信息会记录在errors字段中

## 相关文件

- 控制器: `app/Controllers/Admin/Order.php`
- 服务类: `app/Services/PnrData/PullPnrDataService.php`
- IBE接口类: `app/Libraries/Api/IBE/Ticket.php`
- 路由配置: `app/Config/Routes.php`
