# PullPnrData 脚本使用指南

## 概述

PullPnrData 是一个优化后的定时任务脚本，用于从IBE接口拉取PNR数据并存储出票订单。支持时间范围查询和多种过滤条件。

## 功能特性

### ✨ **新增功能**
- ✅ **时间范围查询** - 支持指定开始时间和结束时间
- ✅ **智能默认值** - 不传参数时默认查询前1小时数据
- ✅ **参数过滤** - 支持按票号和PNR过滤
- ✅ **参数验证** - 严格的参数格式验证
- ✅ **错误处理** - 完善的错误提示和处理

### 🔄 **保留功能**
- ✅ **三步数据拉取** - View Report → PNR详情 → 票务详情
- ✅ **完整数据保存** - 订单、乘客、航段、详情信息
- ✅ **防重复保存** - 通过PNR检查避免重复
- ✅ **出票订单过滤** - 只处理saleStatusCode为"ISSU"的记录

## 使用方法

### 基本语法
```bash
php spark pull:pnr [start_time] [end_time] [options]
```

### 参数说明

#### 时间参数
- **start_time**: 开始时间，格式：`YYYY-MM-DD HH:MM:SS`
- **end_time**: 结束时间，格式：`YYYY-MM-DD HH:MM:SS`

**重要规则：**
- 要么不传任何时间参数（使用默认值）
- 要么同时传入开始时间和结束时间
- 不能只传一个时间参数

#### 选项参数
- `--office`: 指定OFFICE号，默认使用配置文件中的值
- `--ticket_num`: 指定票号进行过滤
- `--pnr`: 指定PNR进行过滤

### 使用示例

#### 1. 默认查询（前1小时）
```bash
php spark pull:pnr
```
**说明**: 查询当前时间前1小时的数据

#### 2. 指定时间范围
```bash
php spark pull:pnr "2022-09-26 10:00:00" "2022-09-26 18:00:00"
```
**说明**: 查询2022年9月26日 10:00-18:00的数据

#### 3. 指定OFFICE号
```bash
php spark pull:pnr "2022-09-26 10:00:00" "2022-09-26 18:00:00" --office=BJS191
```

#### 4. 按PNR过滤
```bash
php spark pull:pnr "2022-09-26 10:00:00" "2022-09-26 18:00:00" --pnr=HZNR2X
```
**说明**: 只处理指定PNR的订单

#### 5. 按票号过滤
```bash
php spark pull:pnr "2022-09-26 10:00:00" "2022-09-26 18:00:00" --ticket_num=479-3995946711
```
**说明**: 只处理指定票号的订单

#### 6. 组合过滤
```bash
php spark pull:pnr "2022-09-26 10:00:00" "2022-09-26 18:00:00" --pnr=HZNR2X --ticket_num=479-3995946711
```
**说明**: 同时按PNR和票号过滤

## 输出信息

### 成功执行示例
```
开始拉取PNR数据...
拉取时间范围: 2022-09-26 10:00:00 ~ 2022-09-26 18:00:00
OFFICE号: BJS191
过滤PNR: HZNR2X
拉取完成！
总记录数: 2
出票订单数: 1
跳过记录数: 1
```

### 统计信息说明
- **总记录数**: 从IBE接口获取的原始记录总数
- **出票订单数**: 成功保存的出票订单数量
- **跳过记录数**: 被过滤掉的记录数量（包括非出票订单和不匹配过滤条件的记录）

## 错误处理

### 常见错误及解决方法

#### 1. 时间参数错误
```
错误: 时间参数必须同时传入开始时间和结束时间，或者都不传（使用默认值）
解决: 检查参数数量，确保同时传入两个时间参数或都不传
```

#### 2. 时间格式错误
```
错误: 开始时间格式错误，请使用 YYYY-MM-DD HH:MM:SS 格式
解决: 检查时间格式，确保使用正确的格式
```

#### 3. 时间范围错误
```
错误: 开始时间必须早于结束时间
解决: 检查时间顺序，确保开始时间早于结束时间
```

## 定时任务配置

### Cron配置示例

#### 每小时执行一次（拉取前1小时数据）
```bash
0 * * * * cd /path/to/project && php spark pull:pnr
```

#### 每天凌晨2点拉取前一天数据
```bash
0 2 * * * cd /path/to/project && php spark pull:pnr "$(date -d 'yesterday' '+%Y-%m-%d 00:00:00')" "$(date -d 'yesterday' '+%Y-%m-%d 23:59:59')"
```

#### 每15分钟执行一次
```bash
*/15 * * * * cd /path/to/project && php spark pull:pnr
```

## 数据流程

### 1. 数据获取
```
IBE View Report接口 → 获取指定时间范围的销售数据
```

### 2. 数据过滤
```
原始数据 → 出票订单过滤 → 参数过滤 → 最终处理数据
```

### 3. 详细信息获取
```
基础订单 → PNR详情接口 → 票务详情接口 → 完整订单数据
```

### 4. 数据保存
```
完整数据 → 主订单表 → 乘客表 → 航段表 → 详情表
```

## 测试命令

### 测试优化功能
```bash
php spark test:optimized
```

### 测试IBE接口
```bash
php spark test:ibe
```

## 注意事项

1. **时间格式**: 必须使用 `YYYY-MM-DD HH:MM:SS` 格式
2. **参数顺序**: 时间参数必须在选项参数之前
3. **过滤逻辑**: 多个过滤条件是AND关系（同时满足）
4. **重复处理**: 相同PNR的订单不会重复保存
5. **模拟数据**: 当前使用模拟数据，实际部署时需要配置真实接口

## 版本历史

- **v2.0** - 优化版本，支持时间范围和过滤功能
- **v1.1** - 整合IBE接口到Ticket类
- **v1.0** - 初始版本，基本的PNR数据拉取功能
