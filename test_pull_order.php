<?php

/**
 * 测试拉取订单接口
 */

// 设置基本路径
define('ROOTPATH', __DIR__ . DIRECTORY_SEPARATOR);
define('APPPATH', ROOTPATH . 'app' . DIRECTORY_SEPARATOR);
define('SYSTEMPATH', ROOTPATH . 'system' . DIRECTORY_SEPARATOR);
define('FCPATH', ROOTPATH . 'public' . DIRECTORY_SEPARATOR);

// 加载 CodeIgniter
require_once SYSTEMPATH . 'bootstrap.php';

// 创建应用实例
$app = \Config\Services::codeigniter();
$app->initialize();

try {
    echo "测试拉取订单接口...\n";
    
    // 创建服务实例
    $pullService = new \App\Services\PnrData\PullPnrDataService();
    
    // 测试参数
    $startTime = '2024-01-01 00:00:00';
    $endTime = '2024-01-01 23:59:59';
    $ticketNumber = null;
    $pnr = null;
    
    echo "调用 pullDataOnly 方法...\n";
    echo "参数: start_time={$startTime}, end_time={$endTime}\n";
    
    // 调用方法
    $result = $pullService->pullDataOnly($startTime, $endTime, $ticketNumber, $pnr);
    
    echo "结果:\n";
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    echo "\n测试完成！\n";
    
} catch (\Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
